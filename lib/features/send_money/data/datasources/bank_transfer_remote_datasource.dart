import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/send_money/data/datasources/bank_local_datasource.dart';
import 'package:cbrs/features/send_money/data/models/account_lookup_response_model.dart';
import 'package:cbrs/features/send_money/data/models/bank_models.dart';
import 'package:cbrs/features/send_money/data/models/bank_transfer_request_model.dart';
import 'package:cbrs/features/send_money/data/models/bank_transfer_response_model.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/send_money/data/models/exchange_rate_model.dart';
import 'package:cbrs/features/send_money/data/models/otp_resend_response.dart';
import 'package:cbrs/features/send_money/data/models/transfer_status_model.dart';
import 'package:cbrs/features/send_money/data/models/wallet_details_model.dart';
import 'package:cbrs/features/send_money/data/models/paginated_banks_response.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:retry/retry.dart';

abstract class BankTransferRemoteDataSource {
  Future<AccountLookupResponseModel> lookupAccount({
    required String accountNumber,
    required String bankCode,
  });

  Future<BankTransferResponseModel> transferToBank({
    required String beneficiaryAccountNo,
    required String beneficiaryName,
    required double amount,
    required String currency,
    required String bankName,
    required String bankCode,
    required String senderName,
  });

  Future<List<BankModel>> getSupportedBanks();

  Future<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String productType,
  });

  Future<void> validateTransferAmount({
    required double amount,
    required String currency,
  });

  Future<TransferStatusModel> checkTransferStatus({
    required String transactionId,
  });

  Future<WalletDetailsModel> getWalletDetails();

  Future<double> getExchangeRate({required String orgId});

  Future<bool> validateWalletBalance({
    required double amount,
    required String currency,
  });

  Future<PaginatedBanksResponse> getSupportedPaginatedBanks({
    required String orgType,
    required int page,
    required int limit,
  });
}

class BankTransferRemoteDataSourceImpl implements BankTransferRemoteDataSource {
  BankTransferRemoteDataSourceImpl({
    required ApiService apiService,
    required this.authLocalDataSource,
    required BankLocalDataSource localDataSource,
    this.cacheDuration = const Duration(hours: 24),
  })  : _apiService = apiService,
        _localDataSource = localDataSource;
  final ApiService _apiService;
  final AuthLocalDataSource authLocalDataSource;
  final BankLocalDataSource _localDataSource;
  final Duration cacheDuration;

  @override
  Future<AccountLookupResponseModel> lookupAccount({
    required String accountNumber,
    required String bankCode,
  }) async {
    debugPrint('remote lookup method');

    try {
      final postData = {
        'accountNumber': accountNumber,
        'bankCode': bankCode,
      };
      final result = await _apiService.post(
        ApiEndpoints.accountLookup,
        data: postData,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return AccountLookupResponseModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*

    try {
      // Validate input length
      if (accountNumber.length < 8 || accountNumber.length > 20) {
        return AccountLookupResponseModel.fromJson(const {
          'success': false,
          'message': 'Invalid account number length',
        });
      }

      final response = await _safeApiCall(
        () => _dio.post<DataMap>(
          ApiEndpoints.accountLookup,
          data: {
            'accountNumber': accountNumber,
            'bankCode': bankCode,
          },
        ),
      );

      debugPrint('Response lookup account response : ${response.data}');

      if (response.data == null) {
        throw const ApiException(
          message: 'Empty response from server',
          statusCode: 500,
        );
      }

      return AccountLookupResponseModel.fromJson(response.data!);
    } on DioException catch (e) {
      debugPrint('eror from lookup e $e');
      if (e.response?.statusCode == 404) {
        return AccountLookupResponseModel.fromJson({
          'success': false,
          'message': e.response?.data?['message'] ?? 'Account not found',
        });
      }
      rethrow;
    } catch (e) {
      debugPrint('Error from lookup E   $e');
      return AccountLookupResponseModel.fromJson(const {
        'success': false,
        'message': 'Something went Wrong, please try again Later',
      });


    }

    */
  }

  @override
  Future<BankTransferResponseModel> transferToBank({
    required String beneficiaryAccountNo,
    required String beneficiaryName,
    required double amount,
    required String currency,
    required String bankName,
    required String bankCode,
    required String senderName,
  }) async {
    try {
      final postData = BankTransferRequestModel(
        beneficiaryAccountNo: beneficiaryAccountNo,
        beneficiaryName: beneficiaryName,
        amount: amount,
        currency: currency,
        bankName: bankName,
        bankCode: bankCode,
        senderName: senderName,
      );
      final result = await _apiService.post(
        ApiEndpoints.bankTransfer,
        data: postData.toJson(),
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return BankTransferResponseModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
    try {
      final request = BankTransferRequestModel(
        beneficiaryAccountNo: beneficiaryAccountNo,
        beneficiaryName: beneficiaryName,
        amount: amount,
        currency: currency,
        bankName: bankName,
        bankCode: bankCode,
        senderName: senderName,
      );

      debugPrint('Request Payload: ${request.toJson()}');

      final response = await _safeApiCall(
        () => _dio.post<DataMap>(
          ApiEndpoints.bankTransfer,
          data: request.toJson(),
        ),
      );

      try {
        final transferResponse =
            BankTransferResponseModel.fromJson(response.data!);

        debugPrint('bank transfer genrating bill response ${response.data}');

        return transferResponse;
      } catch (e) {
        throw const ApiException(
          message: 'Failed to parse USD bank transfer response',
          statusCode: 500,
        );
      }
    } catch (e) {
      rethrow;
    }

    */
  }

  @override
  Future<List<BankModel>> getSupportedBanks() async {
    try {
      final banks = await _fetchBanksFromRemote();

      return banks;
    } catch (e) {
      throw const ApiException(
        message: 'Failed to fetch banks',
        statusCode: 400,
      );
    }
  }

  Future<List<BankModel>> _fetchBanksFromRemote() async {
    try {
      final result = await _apiService.get(
        ApiEndpoints.supportedBanks,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((response) {
        final data = response['data'];

        final institutions = data as List<dynamic>;
        return institutions
            .where(
              (institution) =>
                  institution is Map<String, dynamic> &&
                  (institution['type']?.toString().toLowerCase() == 'bank' ||
                      institution['type']?.toString().toLowerCase() ==
                          'wallet'),
            )
            .map((institution) => BankModel.fromJson(institution as DataMap))
            .toList();
        // return TopUpSuccessResponseModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*

    try {
      // Direct API call without authentication
      final response = await _dio.get<DataMap>(
        ApiEndpoints.supportedBanks,
        options: Options(
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      final data = response.data;
      debugPrint('Response Data: $data');

      if (data == null || !data.containsKey('data')) {
        debugPrint('Invalid response format: $data');
        throw const ApiException(
          message: 'Invalid response format',
          statusCode: 500,
        );
      }

      final institutions = data['data'] as List<dynamic>;
      return institutions
          .where(
            (institution) =>
                institution is Map<String, dynamic> &&
                (institution['type']?.toString().toLowerCase() == 'bank' ||
                    institution['type']?.toString().toLowerCase() == 'wallet'),
          )
          .map((institution) => BankModel.fromJson(institution as DataMap))
          .toList();
    } catch (e) {
      debugPrint('Final error in getSupportedBanks: $e');
      rethrow;
    }
    */
  }

  @override
  Future<void> validateTransferAmount({
    required double amount,
    required String currency,
  }) async {}

  @override
  Future<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String productType,
  }) async {
    try {
      final postData = {
        'amount': amount,
        'currency': currency,
        'productType': productType,
      };
      final result = await _apiService.post(
        ApiEndpoints.validateTransferAmount,
        data: postData,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return CheckTransferRulesResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
    final response = await _safeApiCall(
      () => _dio.post<Map<String, dynamic>>(
        ApiEndpoints.validateTransferAmount,
        data: {
          'amount': amount,
          'currency': currency,
          'productType': productType,
          //'bank_transfer',
        },
      ),
    );

    if (response.data == null) {
      throw const ApiException(
        message: 'Failed to check transfer rules',
        statusCode: 500,
      );
    }

    debugPrint('Response Data: ${response.data}');

    if (response.statusCode == 400) {
      throw ApiException(
        message:
            response.data?['message']?.toString() ?? 'Transfer limit exceeded',
        statusCode: 400,
      );
    }

    try {
      return CheckTransferRulesResponse.fromJson(response.data!);
    } catch (e) {
      throw ApiException(
        message: 'Failed to parse transfer rules response: $e',
        statusCode: 500,
      );
    }
    */
  }

  @override
  Future<TransferStatusModel> checkTransferStatus({
    required String transactionId,
  }) async {
    try {
      final result = await _apiService.get(
        '${ApiEndpoints.transferStatus}/$transactionId',
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((success) {
        return TransferStatusModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }
/*

    final response = await _safeApiCall(
      () => _dio.get(
        '${ApiEndpoints.transferStatus}/$transactionId',
      ),
    );

    try {
      return TransferStatusModel.fromJson(response.data as DataMap);
    } catch (e) {
      throw const ApiException(
        message: 'Failed to parse transfer status response',
        statusCode: 500,
      );
    }

    */
  }

  @override
  Future<WalletDetailsModel> getWalletDetails() async {
    final userId = await authLocalDataSource.getUserId();

    try {
      final result = await _apiService.get(
        '${ApiEndpoints.baseUrl}/members/$userId',
        parser: (data) => data as Map<String, dynamic>,
      );

      //  throw ApiException(
      //     message: "error.message",
      //     statusCode: 400 ?? 400,
      //   );


      debugPrint("Response of getWalletDetails  ${result}");
      
      return result.fold((success) {
        return WalletDetailsModel.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
    const retryOptions = RetryOptions(
      maxAttempts: 3,
      delayFactor: Duration(seconds: 1),
    );

    try {
      return await retryOptions.retry(
        () async {
          final token = await authLocalDataSource.getAuthToken();
          final userId = await authLocalDataSource.getUserId();

          if (token == null) {
            throw const ApiException(
              message: 'Authorization token not found',
              statusCode: 401,
            );
          }

          debugPrint('Fetching user details for ID: $userId');
          final response = await _dio.get(
            '${ApiEndpoints.baseUrl}/members/$userId',
            options: Options(
              headers: {
                'Authorization': 'Bearer $token',
                'Content-Type': 'application/json',
              },
              validateStatus: (status) => status != null && status < 500,
            ),
          );

          if (response.data == null) {
            throw const ApiException(
              message: 'Invalid response format',
              statusCode: 500,
            );
          }

          debugPrint('Response Data: ${response.data}');

          // Pass the entire response data to the model
          return WalletDetailsModel.fromJson(
            response.data as Map<String, dynamic>,
          );
        },
        retryIf: (e) {
          debugPrint('Retry condition check: $e');
          return e is ApiException && e.statusCode >= 500;
        },
      );
    } catch (e) {
      debugPrint('Final error in getWalletDetails: $e');
      rethrow;
    }

    */
  }

  @override
  Future<double> getExchangeRate({required String orgId}) async {
    try {
      final result = await _apiService.get(
        '${ApiEndpoints.baseUrl}/exchanges/search-exchanges',
        queryParameters: {
          'org_id': orgId,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold((responseData) {
        final exchangeRate = ExchangeRateModel.fromJson(responseData);
        return exchangeRate.rate;
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*
    try {
      final token = await authLocalDataSource.getAuthToken();

      debugPrint('orgID xchange rate $orgId');

      final response = await _dio.get<Map<String, dynamic>>(
        '${ApiEndpoints.baseUrl}/exchanges/search-exchanges',
        queryParameters: {
          'org_id': orgId,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      debugPrint('Eth xchange rate ${response.data}');
      final responseData = response.data;
      if (responseData == null || !responseData.containsKey('data')) {
        throw const ApiException(
          message: 'Invalid response format',
          statusCode: 500,
        );
      }

      try {
        final exchangeRate = ExchangeRateModel.fromJson(responseData);
        return exchangeRate.rate;
      } catch (e) {
        debugPrint('Error parsing exchange rate: $e');
        // Return default rate if parsing fails
        return 110.0;
      }
    } catch (e) {
      debugPrint('Error getting exchange rate: $e');
      // Return default rate for any error
      return 110.0;
    }

    */
  }

  @override
  Future<bool> validateWalletBalance({
    required double amount,
    required String currency,
  }) async {
    try {
      final walletDetails = await getWalletDetails();
      final availableBalance = currency == 'ETB'
          ? walletDetails.etbBalance
          : walletDetails.usdBalance;

      return availableBalance >= amount;
    } catch (e) {
      debugPrint('Error validating wallet balance: $e');
      return false;
    }
  }

  @override
  Future<PaginatedBanksResponse> getSupportedPaginatedBanks({
    required String orgType,
    required int page,
    required int limit,
  }) async {
    try {
      final queryParams = {
        'org_type': orgType,
        'page': page,
        'limit': limit,
      };
      final result = await _apiService.get(
        '${ApiEndpoints.baseUrl}/banks/paginate',
        queryParameters: queryParams,
        parser: (data) => data as Map<String, dynamic>,
      );

      debugPrint('');

      return result.fold((success) {
        return PaginatedBanksResponse.fromJson(success);
      }, (error) {
        throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        );
      });
    } on ApiException catch (error) {
      rethrow;
    }

/*

    const retryOptions = RetryOptions(
      maxAttempts: 3,
      delayFactor: Duration(seconds: 1),
    );

    try {
      return await retryOptions.retry(
        () async {
          final response = await _dio.get<DataMap>(
            '${ApiEndpoints.baseUrl}/banks/paginate',
            queryParameters: {
              'org_type': orgType,
              'page': page,
              'limit': limit,
            },
            options: Options(
              validateStatus: (status) => status != null && status < 500,
            ),
          );

          final data = response.data;
          debugPrint('Response Data: $data');

          if (data == null) {
            debugPrint('Invalid response format: $data');
            throw const ApiException(
              message: 'Invalid response format',
              statusCode: 500,
            );
          }

          return PaginatedBanksResponse.fromJson(data);
        },
        retryIf: (e) {
          debugPrint('Retry condition check: $e');
          return e is ApiException && e.statusCode >= 500;
        },
      );
    } catch (e) {
      debugPrint('Final error in getSupportedPaginatedBanks: $e');
      rethrow;
    }

    */
  }
}
