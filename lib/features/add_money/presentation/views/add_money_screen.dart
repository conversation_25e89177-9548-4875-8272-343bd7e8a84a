import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_otp_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/add_money/application/bloc/add_money_bloc.dart';
import 'package:cbrs/features/add_money/domain/entities/add_money_response.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/presentation/widgets/account_selector.dart';
import 'package:cbrs/features/add_money/presentation/widgets/no_linked_account_widget.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/transfer_to_wallet/application/bloc/check_rule_transfer_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class AddMoneyScreen extends StatefulWidget {
  const AddMoneyScreen({
    super.key,
    this.defaultBankId,
    this.defaultAccountNumber,
  });
  final String? defaultBankId;
  final String? defaultAccountNumber;

  @override
  State<AddMoneyScreen> createState() => _AddMoneyScreenState();
}

// Custom controller that prevents keyboard input when no account is selected
class AddMoneyCurrencyController extends CurrencyInputController {
  AddMoneyCurrencyController({
    required this.context,
    required this.hasAccountSelected,
    required super.currencyType,
    required double maxAmount,
    super.minTransferLimit,
    super.ignoreWalletAmountCheck,
  }) : super(
          maxBalance: maxAmount,
        );
  final BuildContext context;
  final bool Function() hasAccountSelected;

  @override
  void onKeyPressed(String value) {
    if (!hasAccountSelected()) {
      CustomToastification(
        context,
        message: 'Please select an account first',
      );
      return;
    }
    super.onKeyPressed(value);
  }
}

class _AddMoneyScreenState extends State<AddMoneyScreen> {
  LinkedAccount? _selectedAccount;
  bool isLoading = false;
  // late AddMoneyCurrencyController _currencyController;
  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;
  String _currentBillRefNo = '';
  late CurrencyInputController _currencyController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    context.read<AddMoneyBloc>().add(
          const GetLinkedAccountsEvent(
            page: 1,
            limit: 20,
          ),
        );

    _currencyController = CurrencyInputController(
      currencyType: CurrencyType.etb,
      maxBalance: 0,
      ignoreWalletAmountCheck: true,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.addMoney,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        // Check if we have an OTP verification state
        final currentState = context.read<AddMoneyBloc>().state;

        if (currentState is AddMoneyOtpVerified) {
          // If OTP was verified, use it with the PIN
          context.read<TransactionBloc>().add(
                ConfirmTransferEvent(
                  pin: pin,
                  billRefNo: _currentBillRefNo,
                  transactionType: tx_type.TransactionType.addMoney,
                ),
              );
        } else {
          // Use the stored billRefNo instead of trying to get it from the state
          if (_currentBillRefNo.isNotEmpty) {
            context.read<TransactionBloc>().add(
                  ConfirmTransferEvent(
                    pin: pin,
                    billRefNo: _currentBillRefNo,
                    transactionType: tx_type.TransactionType.addMoney,
                  ),
                );
          } else {
            // Handle the case where billRefNo is not available
            CustomToastification(
              context,
              message: 'Something went wrong. Please try again.',
            );
          }
        }
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  void _showConfirmScreenBottomSheet(AddMoneyResponseEntity response) {
    final data = {
      'Transaction Type': 'Add Money',
      'Sender Name': response.senderName,
      'Bank Name': response.bankName,
      'Bank Account Number': response.senderAccountNo,
      'Amount': '${response.billAmount} ETB',
      'Service Charge': '${response.serviceCharge} ETB',
      'VAT': '${response.vat} ETB',
      'Date': AppMapper.safeFormattedDate(DateTime.now().toString()),
    };
    // Store the billRefNo for later use
    _currentBillRefNo = response.billRefNo;

    // Check if OTP is required from the state
    final requiresOtp = response.authorizationType == 'PIN_AND_OTP';

    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: data,
      totalAmount: _currencyController.numericAmount,
      billAmount: _currencyController.numericAmount,
      confirmButtonText: 'Confirm Add Money',
      requiresOtp: requiresOtp,
      billRefNo: _currentBillRefNo,
      originalCurrency: 'ETB',
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      totalAmount: transaction.totalAmount,
      billAmount: transaction.billAmount,
      transactionId: transaction.id,
      billRefNo: transaction.billRefNo,
      originalCurrency: 'ETB',
      status: 'Paid',
      {
        'Transaction Type': 'Add Money',
        'Sender Name': transaction.senderName,
        'Account Number': transaction.senderAccountNo,
        'Bank Name': transaction.bankName,
        'Amount': '${transaction.billAmount} ETB',
        'serviceCharge': '${transaction.serviceCharge} ETB',
        'VAT': '${transaction.vat} ETB',
        'Connect Ref No.': transaction.walletFTNumber,
        'Date': AppMapper.safeFormattedDate(transaction.createdAt),
        'FT Number': transaction.ftNumber,
      },
    );
  }

  Future<void> _checkTransferRule() async {
    final amount = _currencyController.numericAmount;

    context.read<CheckRuleTransferBloc>().add(
          CheckWalletTransferRulesRequested(
            productType: 'add_money',
            amount: amount,
            currency: 'ETB',
            accountNumber: _selectedAccount?.accountNumber,
            bankID: _selectedAccount?.bank.id,
            // productType: 'add_money',
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Money'),
      ),
      body: Container(
        child: BlocConsumer<AddMoneyBloc, AddMoneyState>(
          listener: (context, state) {
            // after check transfer rules - this should be hit
            if (state is AddMoneyAmountValidated) {
              // Get sender name
            } else if (state is AddMoneyConfirmedState) {
              setState(() => isLoading = false);

              _showConfirmScreenBottomSheet(state.response);
            }
            if (state is AddMoneyError) {
              CustomToastification(context, message: state.message);
              setState(() {
                isLoading = false;
              });
            }
            // if (state is EmptyLinkedAccountState) {
            //   CustomToastification(context, message: 'EmptyLinkedAccountState');
            // }
          },
          builder: (context, state) {
            if (state is AddMoneyLoading && _selectedAccount == null) {
              return const CustomConnectLoader();
            }
            if (state is EmptyLinkedAccountState) {
              return const NoLinkedAccountWidget();
            }
            return BlocListener<CheckRuleTransferBloc, CheckRuleTransferState>(
              listener: (context, state) {
                if (state is CheckingWalletTransferRules) {
                  setState(() => isLoading = true);
                }

                if (state is CheckTransferRulesChecked) {
                  // Get the amount from the controller

                  // Initiate add money transfer
                  context.read<AddMoneyBloc>().add(
                        AddMoneyTransferEvent(
                          accountNumber: _selectedAccount!.accountNumber,
                          bankId: _selectedAccount!.bank.id,
                          amount: _currencyController.numericAmount,
                          currency: 'ETB',
                          senderName: _selectedAccount?.member.fullName ?? '',
                        ),
                      );
                } else if (state is CheckTransferRulesFailure) {
                  setState(() {
                    isLoading = false;
                  });
                  CustomToastification(context, message: state.message);
                }
              },
              child: CurrencyInputWidget(
                controller: _currencyController,
                title: '',
                subtitle: '',
                fetchTransferLimits: true,
                transactionType: 'add_money',
                customWalletType: 'ETB',
                customBalance: _selectedAccount != null
                    ? _selectedAccount!.accountBalance
                    : 0.0,

                // TODO  -- update balcne like 10000ETB
                // balanceLabel: _selectedAccount != null
                //     ? _selectedAccount!.accountBalance.toString()
                //     : '0.0',
                walletBalanceLabel: 'Account Balance:',
                header: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 16.h,
                  ),
                  child: Column(
                    children: [
                      CustomPageHeader(
                        pageTitle:
                            'Add Money to you Wallet ${_currencyController.numericAmount}',
                        description:
                            'Select a linked account, top up your wallet, and get it ready for transfers.',
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      _selectAccount(),
                    ],
                  ),
                ),
                isLoading: isLoading,
                onContinue: () {
                  if (_selectedAccount == null) {
                    CustomToastification(
                      context,
                      isInfo: true,
                      isError: false,
                      successTitle: 'Info',
                      message: 'Please select an account first',
                    );
                    return;
                  }

                  _checkTransferRule();

                  if (isLoading) return;
                  setState(() {
                    isLoading = true;
                  });
                },
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _selectAccount() {
    return GestureDetector(
      onTap: _showSelectAccountModal,
      child: Container(
        padding: EdgeInsets.only(
          left: _selectedAccount != null ? 8.w : 12.w,
          right: _selectedAccount != null ? 15.w : 12.w,
          top: _selectedAccount != null ? 8.h : 15.h,
          bottom: _selectedAccount != null ? 8.h : 15.h,
        ),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            if (_selectedAccount != null)
              Container(
                height: 40.h,
                width: 40.w,
                margin: EdgeInsets.only(right: 8.w),
                padding: const EdgeInsets.all(4),
                child: CustomCachedImage(
                  url: _selectedAccount!.bank.logo,
                ),
              ),
            Expanded(
              child: _selectedAccount != null
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomBuildText(
                          text: _selectedAccount != null
                              ? '${_selectedAccount!.member.firstName} '
                                  '${_selectedAccount!.member.middleName} '
                                  '${_selectedAccount!.member.lastName}'
                              : '',
                          style: GoogleFonts.outfit(
                            fontWeight: FontWeight.w400,
                            fontSize: 13.sp,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          _selectedAccount!.accountNumber,
                          style: GoogleFonts.outfit(
                            fontSize: 13.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    )
                  : const CustomBuildText(
                      text: 'Select Account',
                    ),
            ),
            Icon(
              Icons.keyboard_arrow_down,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _showSelectAccountModal() {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BlocProvider<AddMoneyBloc>(
        create: (context) => sl<AddMoneyBloc>(),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(24.r),
            ),
          ),
          child: AccountSelector(
            selectedAccount: _selectedAccount,
            onAccountSelected: (account) {
              setState(() {
                _selectedAccount = account;

                // Get current transfer limits from the TransactionBloc
                final transactionState = context.read<TransactionBloc>().state;
                var minLimit = 0.0; // Default minimum

                // Extract limits from TransactionBloc if available
                if (transactionState is TransferLimitLoaded) {
                  minLimit = transactionState.transferLimit.minTransferLimit;
                }

                _currencyController = AddMoneyCurrencyController(
                  context: context,
                  hasAccountSelected: () => _selectedAccount != null,
                  currencyType: CurrencyType.etb,
                  maxAmount: account.accountBalance,
                  minTransferLimit: minLimit,
                );
              });
            },
          ),
        ),
      ),
    );
  }
}
