import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/add_money/application/bloc/add_money_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/top_up/applications/top_up_bloc.dart';
import 'package:cbrs/features/top_up/applications/top_up_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';

class AddMoneyPinBottomSheet extends StatefulWidget {
  const AddMoneyPinBottomSheet({
    required this.onPinSubmitted,
    required this.isLoading,
    required this.pinController,
    super.key,
  });
  final Function(String pin) onPinSubmitted;
  final bool isLoading;
  final TextEditingController pinController;

  @override
  State<AddMoneyPinBottomSheet> createState() => _AddMoneyPinBottomSheetState();
}

class _AddMoneyPinBottomSheetState extends State<AddMoneyPinBottomSheet>
    with SingleTickerProviderStateMixin {
  final _pinController = TextEditingController();
  bool _obscurePin = true;

  String _enteredPin = '';
  bool _isLoading = false;
  late AnimationController _animationController;
  final bool _isLocalLoading = false;

  // bool get shouldLoad => _enteredPin.length == 6 && _isLoading;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _animationController.forward();
  }

  void _onKeyPressed(String value) {
    if (_enteredPin.length >= 6) return;

    setState(() {
      _enteredPin += value;
      widget.pinController.text = _enteredPin;
    });

    if (_enteredPin.length == 6) {
      setState(() {
        _isLoading = true;
      });
      widget.onPinSubmitted(_enteredPin);
    }
  }

  void _onBackspace() {
    if (_enteredPin.isEmpty) return;

    setState(() {
      _enteredPin = _enteredPin.substring(0, _enteredPin.length - 1);
      widget.pinController.text = _enteredPin;
    });
  }

  Widget _buildKeypadButton(
    BuildContext context, {
    required String label,
    required VoidCallback? onPressed,
    bool isGo = false,
  }) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 12.w),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            width: 124.w,
            height: 64.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isGo ? Theme.of(context).primaryColor : Colors.grey[50],
            ),
            child: Center(
              child: _isLoading && isGo
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      label,
                      style: GoogleFonts.outfit(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w600,
                        color: isGo ? Colors.white : Colors.black,
                      ),
                    ),
            ),
          ),
        ),
      ).animate().scale(duration: 200.ms),
    );
  }

  Widget _buildNumericKeypad(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          [1, 2, 3],
          [4, 5, 6],
          [7, 8, 9],
          ['⌫', 0, '→'],
        ].map((row) {
          return Padding(
            padding: EdgeInsets.only(bottom: 13.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: row.map((item) {
                if (item == '⌫') {
                  return _buildKeypadButton(
                    context,
                    label: item.toString(),
                    onPressed: _onBackspace,
                  );
                } else if (item == '→') {
                  return _buildKeypadButton(
                    context,
                    label: _enteredPin.length == 6 ? '' : item.toString(),
                    onPressed: _enteredPin.length == 6
                        ? null
                        : () => _onKeyPressed(item.toString()),
                    isGo: true,
                  );
                } else {
                  return _buildKeypadButton(
                    context,
                    label: item.toString(),
                    onPressed: () => _onKeyPressed(item.toString()),
                  );
                }
              }).toList(),
            ),
          );
        }).toList(),
      ),
    ).animate().fadeIn(duration: 800.ms, delay: 400.ms);
  }

  Widget _buildPinDisplay() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(6, (index) {
        final isFilled = index < _enteredPin.length;
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 6.w),
          width: 48.w,
          height: 48.w,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              isFilled ? (_obscurePin ? '*' : _enteredPin[index]) : '',
              style: GoogleFonts.outfit(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AddMoneyBloc, AddMoneyState>(
      listener: (context, state) {
        if (state is AddMoneySuccess) {
          Navigator.of(context).pop();
        } else if (state is AddMoneyPinInvalid) {
          setState(() {
            _isLoading = false;
            _enteredPin = '';
          });
          // CustomToastification(context, message: state.message);
        }
      },
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.only(
            top: 32.h,
            bottom: MediaQuery.of(context).viewInsets.bottom + 32.h,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(32.r)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Enter PIN to Verify Transfer',
                style: GoogleFonts.outfit(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w700,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'Please enter your PIN to verify this transfer.',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 32.h),
              _buildPinDisplay(),
              TextButton(
                onPressed: () => setState(() => _obscurePin = !_obscurePin),
                child: Text(
                  _obscurePin ? 'Show PIN' : 'Hide PIN',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              SizedBox(height: 24.h),
              _buildNumericKeypad(
                context,
              ),
            ],
          ),
        );
      },
    );
    // return Container(
    //   padding: EdgeInsets.only(
    //     left: 24.w,
    //     right: 24.w,
    //     top: 24.w,
    //     bottom: MediaQuery.of(context).viewInsets.bottom + 24.w,
    //   ),
    //   decoration: BoxDecoration(
    //     color: Colors.white,
    //     borderRadius: BorderRadius.vertical(top: Radius.circular(32.r)),
    //   ),
    //   child: SingleChildScrollView(
    //     child: Column(
    //       mainAxisSize: MainAxisSize.min,
    //       children: [
    //         // Bottom sheet handle
    //         Container(
    //           margin: EdgeInsets.only(bottom: 16.h),
    //           height: 4,
    //           width: 40,
    //           decoration: BoxDecoration(
    //             color: Colors.grey.shade300,
    //             borderRadius: BorderRadius.circular(2),
    //           ),
    //         ),
    //         Text(
    //           'Enter PIN',
    //           style: GoogleFonts.outfit(
    //             fontSize: 20.sp,
    //             fontWeight: FontWeight.w600,
    //           ),
    //         ),
    //         SizedBox(height: 8.h),
    //         Text(
    //           'Please enter your 6-digit PIN to confirm the transfer',
    //           textAlign: TextAlign.center,
    //           style: GoogleFonts.outfit(
    //             fontSize: 14.sp,
    //             color: Colors.grey[600],
    //           ),
    //         ),
    //         SizedBox(height: 24.h),
    //         Pinput(
    //           controller: _pinController,
    //           length: 6,
    //           obscureText: _obscurePin,
    //           obscuringCharacter: '●',
    //           defaultPinTheme: PinTheme(
    //             width: 48.w,
    //             height: 48.w,
    //             decoration: BoxDecoration(
    //               color: const Color(0xFFF5F5F5),
    //               borderRadius: BorderRadius.circular(8.r),
    //             ),
    //             textStyle: GoogleFonts.outfit(
    //               fontSize: 24.sp,
    //               fontWeight: FontWeight.w600,
    //             ),
    //           ),
    //         ),
    //         SizedBox(height: 16.h),
    //         TextButton(
    //           onPressed: () => setState(() => _obscurePin = !_obscurePin),
    //           child: Text(
    //             _obscurePin ? 'Show PIN' : 'Hide PIN',
    //             style: GoogleFonts.outfit(
    //               fontSize: 16.sp,
    //               color: Theme.of(context).primaryColor,
    //               fontWeight: FontWeight.w500,
    //             ),
    //           ),
    //         ),
    //         SizedBox(height: 24.h),
    //         CustomButton(
    //           text: 'Verify',
    //           onPressed: () {
    //             if (_pinController.text.length == 6) {
    //               widget.onPinSubmitted(_pinController.text);
    //             }
    //           },
    //           options: CustomButtonOptions(
    //             height: 56.h,
    //             color: Theme.of(context).primaryColor,
    //             textStyle: GoogleFonts.outfit(
    //               fontSize: 16.sp,
    //               fontWeight: FontWeight.w600,
    //               color: Colors.white,
    //             ),
    //             borderRadius: BorderRadius.circular(32.r),
    //           ),
    //         ),
    //       ],
    //     ),
    //   ),
    // );
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }
}
