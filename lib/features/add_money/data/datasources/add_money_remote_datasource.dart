import 'package:cbrs/features/add_money/data/models/linked_account_response_model.dart';
import 'package:cbrs/features/add_money/data/models/add_money_response_model.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/send_money/data/models/otp_resend_response.dart';

abstract class AddMoneyRemoteDataSource {
  Future<PaginatedLinkedAccountsResponseModel> getLinkedAccounts({
    required int page,
    required int limit,
    String status = 'LINKED',
  });

  Future<LinkedAccountResponseModel> checkAccountBalance({
    required String bankId,
    required String accountNumber,
  });

  Future<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
      required String bankId,
    required String accountNumber,
  });

  Future<AddMoneyResponseModel> addMoney({
    required String accountNumber,
    required String bankId,
    required double amount,
    required String currency,
    required String senderName,
  });

  Future<AddMoneyResponseModel> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  });

  Future<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  });

  Future<AddMoneyResponseModel> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  });
}
