import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/add_money/domain/entities/add_money_response.dart';
import 'package:flutter/material.dart';

class AddMoneyResponseModel extends AddMoneyResponseEntity {
  const AddMoneyResponseModel({
    required super.senderId,
    required super.senderName,
    required super.transactionOwner,
    required super.beneficiaryId,
    required super.beneficiaryName,
    required super.beneficiaryAccountNo,
    required super.transactionType,
    required super.billAmount,
    required super.originalCurrency,
    required super.billRefNo,
    required super.billReason,
    required super.authorizationType,
    required super.status,
    required super.createdAt,
    required super.lastModified,
    required super.id,
    required super.serviceCharge,
    required super.vat,
    required super.totalAmount,
    required super.paidAmount,
    required super.bankName,
    required super.walletFtNumber,
    super.senderPhone,
    super.senderEmail,
    super.beneficiaryPhone,
    super.beneficiaryEmail,
    super.senderAccountNo,
  });

  factory AddMoneyResponseModel.fromJson(Map<String, dynamic> json) {
    debugPrint("😔total amount of json ${json['totalAmount']}");
    return AddMoneyResponseModel(
      id: AppMapper.safeString(json['id']),
      senderId: AppMapper.safeString(json['senderId']),
      senderName: AppMapper.safeString(json['senderName']),
      senderPhone: AppMapper.safeString(json['senderPhone']),
      senderEmail: AppMapper.safeString(json['senderEmail']),
      transactionOwner: AppMapper.safeString(json['transactionOwner']),
      beneficiaryId: AppMapper.safeString(json['beneficiaryId']),
      beneficiaryName: AppMapper.safeString(json['beneficiaryName']),
      beneficiaryPhone: AppMapper.safeString(json['beneficiaryPhone']),
      beneficiaryAccountNo: AppMapper.safeString(json['beneficiaryAccountNo']),
      beneficiaryEmail: AppMapper.safeString(json['beneficiaryEmail']),
      bankName: AppMapper.safeString(json['bankName']),
      transactionType: AppMapper.safeString(json['transactionType']),
      billAmount: AppMapper.safeDouble(json['billAmount']),
      originalCurrency: AppMapper.safeString(json['originalCurrency']),
      serviceCharge: AppMapper.safeDouble(json['serviceCharge']),
      vat: AppMapper.safeDouble(json['VAT']),
      totalAmount: AppMapper.safeDouble(json['totalAmount']),
      billRefNo: AppMapper.safeString(json['billRefNo']),
      status: AppMapper.safeString(json['status']),
      authorizationType: AppMapper.safeString(json['authorization_type']),
      walletFtNumber: AppMapper.safeString(json['walletFTNumber']),
      billReason: AppMapper.safeString(json['billReason']),
      createdAt: AppMapper.safeString(json['createdAt']),
      lastModified: AppMapper.safeString(json['lastModified']),
      paidAmount: AppMapper.safeDouble(json['paidAmount']),
      senderAccountNo: AppMapper.safeString(json['senderAccountNo']),
    );
  }
}
