import 'package:cbrs/features/add_money/domain/entities/add_money_request.dart';

class AddMoneyRequestModel extends AddMoneyRequest {
  const AddMoneyRequestModel({
    required super.beneficiaryAccountNo,
    required super.bankId,
    required super.amount,
    required super.currency,
    super.transactionType = 'add_money',
  });

  Map<String, dynamic> toJson() {
    return {
      'beneficiaryAccountNo': beneficiaryAccountNo,
      'bankID': bankId,
      'amount': amount,
      'currency': currency,
      'transactionType': transactionType,
    };
  }

  factory AddMoneyRequestModel.fromJson(Map<String, dynamic> json) {
    return AddMoneyRequestModel(
      beneficiaryAccountNo: json['beneficiaryAccountNo'] as String,
      bankId: json['bankID'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      transactionType: json['transactionType'] as String? ?? 'add_money',
    );
  }
}