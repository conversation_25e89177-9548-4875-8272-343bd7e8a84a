part of 'add_money_bloc.dart';

abstract class AddMoneyState extends Equatable {
  const AddMoneyState();

  @override
  List<Object?> get props => [];
}

class AddMoneyInitial extends AddMoneyState {
  const AddMoneyInitial();
}

class AddMoneyLoading extends AddMoneyState {
  const AddMoneyLoading();
}

class EmptyLinkedAccountState extends AddMoneyState {
  const EmptyLinkedAccountState();
  @override
  List<Object?> get props => [];
}

class LinkedAccountsLoaded extends AddMoneyState {
  const LinkedAccountsLoaded(this.accounts);
  final PaginatedLinkedAccounts accounts;

  @override
  List<Object?> get props => [accounts];
}

class AccountBalanceChecked extends AddMoneyState {
  const AccountBalanceChecked(this.account);
  final LinkedAccount account;

  @override
  List<Object?> get props => [account];
}

class AddMoneyAmountValidated extends AddMoneyState {
  const AddMoneyAmountValidated(this.rules);
  final CheckTransferRulesResponse rules;

  @override
  List<Object?> get props => [rules];
}

class AddMoneySuccess extends AddMoneyState {
  const AddMoneySuccess(this.response);
  final AddMoneyResponseModel response;

  @override
  List<Object?> get props => [response];
}


class AddMoneyConfirmedState extends AddMoneyState {
  const AddMoneyConfirmedState(this.response);
  final AddMoneyResponseEntity response;

  @override
  List<Object?> get props => [response];
}


class AddMoneyPinRequired extends AddMoneyState {
  const AddMoneyPinRequired({
    required this.billRefNo,
    this.requiresOtp = false,
  });
  final String billRefNo;
  final bool requiresOtp;

  @override
  List<Object?> get props => [billRefNo, requiresOtp];
}

class AddMoneyPinSubmitting extends AddMoneyState {
  const AddMoneyPinSubmitting();
}

class AddMoneyPinInvalid extends AddMoneyState {
  const AddMoneyPinInvalid({
    required this.message,
    required this.billRefNo,
    required this.otp,
  });
  final String message;
  final String billRefNo;
  final String otp;

  @override
  List<Object?> get props => [message, billRefNo, otp];
}

class AddMoneyOtpSending extends AddMoneyState {
  const AddMoneyOtpSending();
}

class AddMoneyOtpSent extends AddMoneyState {
  const AddMoneyOtpSent({
    required this.message,
    required this.otpCode,
  });
  final String message;
  final int otpCode;

  @override
  List<Object?> get props => [message, otpCode];
}

class AddMoneyOtpVerifying extends AddMoneyState {
  const AddMoneyOtpVerifying();
}

class AddMoneyOtpVerified extends AddMoneyState {
  const AddMoneyOtpVerified(this.response);
  final AddMoneyResponseModel response;

  @override
  List<Object?> get props => [response];
}

class AddMoneyError extends AddMoneyState {
  const AddMoneyError(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}
