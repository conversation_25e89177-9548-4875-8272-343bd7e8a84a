import 'package:equatable/equatable.dart';

class AddMoneyResponseEntity extends Equatable {
  const AddMoneyResponseEntity({
    required this.senderId,
    required this.senderName,
    required this.transactionOwner,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.beneficiaryAccountNo,
    required this.transactionType,
    required this.billAmount,
    required this.originalCurrency,
    required this.billRefNo,
    required this.billReason,
    required this.authorizationType,
    required this.status,
    required this.createdAt,
    required this.lastModified,
    required this.id,
    required this.serviceCharge,
    required this.vat,
    required this.totalAmount,
    required this.paidAmount,
    required this.walletFtNumber,
    required this.bankName,
    this.senderPhone,
    this.senderEmail,
    this.beneficiaryPhone,
    this.beneficiaryEmail,
    this.senderAccountNo,
  });
  final String senderId;
  final String senderName;
  final String? senderPhone;
  final String? senderEmail;
  final String? senderAccountNo;

  final String transactionOwner;
  final String beneficiaryId;
  final String beneficiaryName;
  final String? beneficiaryPhone;
  final String? beneficiaryEmail;

  final String beneficiaryAccountNo;
  final String transactionType;
  final double billAmount;
  final String originalCurrency;
  final String billRefNo;
  final String billReason;
  final String authorizationType;
  final String status;
  final String createdAt;
  final String lastModified;
  final String id;
  final double serviceCharge;
  final double vat;
  final double totalAmount;
  final double paidAmount;

  final String bankName;
  final String walletFtNumber;

  @override
  List<Object?> get props => [
        senderId,
        senderName,
        senderPhone,
        senderEmail,
        transactionOwner,
        beneficiaryId,
        beneficiaryName,
        beneficiaryAccountNo,
        transactionType,
        billAmount,
        originalCurrency,
        billRefNo,
        billReason,
        authorizationType,
        status,
        createdAt,
        lastModified,
        id,
        serviceCharge,
        vat,
        totalAmount,
        paidAmount,
   
      ];
}
