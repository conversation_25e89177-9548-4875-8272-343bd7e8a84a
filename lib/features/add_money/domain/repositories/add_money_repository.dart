import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/data/models/add_money_response_model.dart';
import 'package:cbrs/features/add_money/domain/entities/add_money_response.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/send_money/data/models/otp_resend_response.dart';

abstract class AddMoneyRepository {
  ResultFuture<PaginatedLinkedAccounts> getLinkedAccounts({
    required int page,
    required int limit,
    String status = 'LINKED',
  });

  ResultFuture<LinkedAccount> checkAccountBalance({
    required String bankId,
    required String accountNumber,
  });

  ResultFuture<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
      required String bankId,
    required String accountNumber,
  });

  ResultFuture<AddMoneyResponseEntity> addMoney({
    required String accountNumber,
    required String bankId,
    required double amount,
    required String currency,
    required String senderName,
  });

  ResultFuture<AddMoneyResponseModel> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  });

  ResultFuture<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  });

  ResultFuture<AddMoneyResponseModel> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  });
}
