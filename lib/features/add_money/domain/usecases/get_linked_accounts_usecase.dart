import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:equatable/equatable.dart';

class GetLinkedAccounts extends UsecaseWithParams<PaginatedLinkedAccounts, GetLinkedAccountsParams> {
  final AddMoneyRepository _repository;

  const GetLinkedAccounts(this._repository);

  @override
  ResultFuture<PaginatedLinkedAccounts> call(GetLinkedAccountsParams params) async {
    return _repository.getLinkedAccounts(
      page: params.page,
      limit: params.limit,
      status: params.status,
    );
  }
}

class GetLinkedAccountsParams extends Equatable {
  final int page;
  final int limit;
  final String status;

  const GetLinkedAccountsParams({
    required this.page,
    required this.limit,
    this.status = 'LINKED',
  });

  @override
  List<Object?> get props => [page, limit, status];
}