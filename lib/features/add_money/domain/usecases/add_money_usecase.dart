import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/data/models/add_money_response_model.dart';
import 'package:cbrs/features/add_money/domain/entities/add_money_response.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:equatable/equatable.dart';

class AddMoney
    extends UsecaseWithParams<AddMoneyResponseEntity, AddMoneyParams> {
  final AddMoneyRepository _repository;

  const AddMoney(this._repository);

  @override
  ResultFuture<AddMoneyResponseEntity> call(AddMoneyParams params) async {
    return _repository.addMoney(
      accountNumber: params.accountNumber,
      bankId: params.bankId,
      amount: params.amount,
      currency: params.currency,
      senderName: params.senderName,
    );
  }
}

class AddMoneyParams extends Equatable {
  final String accountNumber;
  final String bankId;
  final double amount;
  final String currency;
  final String senderName;

  const AddMoneyParams({
    required this.accountNumber,
    required this.bankId,
    required this.amount,
    required this.currency,
    required this.senderName,
  });

  @override
  List<Object?> get props => [
        accountNumber,
        bankId,
        amount,
        currency,
        senderName,
      ];
}
