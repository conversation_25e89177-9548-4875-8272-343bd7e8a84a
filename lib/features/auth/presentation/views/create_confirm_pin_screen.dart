// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_password_pad.dart';
import 'package:cbrs/core/common/widgets/custom_pin_input.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/form_validation.dart';
import 'package:cbrs/features/auth/presentation/widget/show_pin.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class CreateConfirmPinScreen extends StatefulWidget {
  const CreateConfirmPinScreen({
    required this.source,
    required this.newPin,
    super.key,
  });
  final String source;
  final String newPin;

  @override
  State<CreateConfirmPinScreen> createState() => _CreateConfirmPinScreenState();
}

class _CreateConfirmPinScreenState extends State<CreateConfirmPinScreen> {
  final TextEditingController _controller = TextEditingController();

  bool _isObscured = true;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Confirm PIN')),
      body: LayoutBuilder(
        builder: (context, constraints) => SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: IntrinsicHeight(
              child: BlocListener<AuthBloc, AuthState>(
                listener: (context, state) {
                  if (state is AuthError) {
                    context.pushReplacementNamed(
                      AppRouteName.createPin,
                      extra: {'source': widget.source},
                    );
                    CustomToastification(context, message: state.message);
                    setState(() {
                      _isLoading = false;
                    });
                  } else if (state is LoggedInWithPinState) {
                    context.go(AppRouteName.home);
                    setState(() {
                      _isLoading = false;
                    });
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(2),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Spacer(),
                      CustomPagePadding(
                        child: Column(
                          children: [
                            Image.asset(
                              MediaRes.connectBirrMainLogo,
                              height: 100.h,
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            const CustomBuildText(
                              text: 'Confirm PIN',
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              caseType: '',
                            ),
                            const SizedBox(
                              height: 8,
                            ),
                            const CustomBuildText(
                              text:
                                  'Please re-enter your PIN to confirm and complete the setup. Ensure it is correct.',
                              fontSize: 12,
                              color: Color(0xFFAAAAAA),
                              textAlign: TextAlign.center,
                              caseType: '',
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            // color: Colors.red,
                            CustomPinInput(
                              controller: _controller,
                              isObscured: _isObscured,
                            ),

                            const SizedBox(
                              height: 36,
                            ),
                            ShowPinText(
                              onTap: () {
                                setState(() {
                                  _isObscured = !_isObscured;
                                });
                              },
                              isObscured: _isObscured,
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            const CustomBuildText(
                              text:
                                  'A 6-digit PIN that is secure and easy to remember',
                              fontSize: 12,
                              color: Color(0xFFAAAAAA),
                              textAlign: TextAlign.center,
                              caseType: '',
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      CustomPasswordPad(
                        controller: _controller,
                        onChanged: _handleKeyPressed,
                        isLoading: _isLoading,
                        onSubmitted: _handleOnSubmitted,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleKeyPressed(String keys, bool isKey) async {
    setState(() {
      if (!isKey) {
        _controller.text = _controller.text.isNotEmpty
            ? _controller.text.substring(0, _controller.text.length - 1)
            : '';
      } else {
        _controller.text = "${_controller.text}$keys";
      }
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    });
  }

  Future<void> _handleOnSubmitted(String enteredPin) async {
    debugPrint('You can submit pin now $enteredPin');

    setState(() {
      _isLoading = true;
    });

    final value = FormValidation.validateConfirmPIN(
      enteredPin,
      widget.newPin,
    );

    if (value != null) {
      CustomToastification(context, message: value);
      setState(() {
        _isLoading = false;
      });
      return;
    }

    await Future.delayed(const Duration(seconds: 1));
    try {
      // Store PIN for biometric login
      final deviceController = Get.find<DeviceCheckController>();
      await deviceController.storePin(widget.newPin);

      // Dispatch the event to create the PIN
      context.read<AuthBloc>().add(
            CreatePinEvent(
              pin: widget.newPin,
              source: widget.source,
            ),
          );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Error setting up biometric login',
      );
    }

    // context.read<AuthBloc>().add(LoginWithPinEvent(pin: enteredPin));
  }
}
