import 'dart:async';
import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_password_pad.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pinput/pinput.dart';

class TokenDeviceLoginScreen extends StatefulWidget {
  const TokenDeviceLoginScreen({super.key});

  @override
  State<TokenDeviceLoginScreen> createState() => _TokenDeviceLoginScreenState();
}

class _TokenDeviceLoginScreenState extends State<TokenDeviceLoginScreen> {
  final _pinController = TextEditingController();
  String _enteredPin = '';
  bool _isLoading = false;
  bool _isObscured = true;
  bool _isBiometricAuthInProgress = false;
  bool _hasBiometricSupport = false;
  BiometricType? _preferredBiometric;

  @override
  void initState() {
    super.initState();

    // Initialize auth state and check biometric support
    GlobalVariable.currentlySelectedWallet = 'USD';
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      context.read<AuthBloc>().add(const InitializeAuthEvent());
      await _checkBiometricSupport();
    });
  }

  Future<void> _checkBiometricSupport() async {
    try {
      final authLocalDataSource = context.read<AuthLocalDataSource>();
      final hasBiometricSupport =
          await authLocalDataSource.isBiometricAvailable();
      final preferredBiometric = hasBiometricSupport
          ? await authLocalDataSource.getPreferredBiometric()
          : null;

      if (mounted) {
        setState(() {
          _hasBiometricSupport = hasBiometricSupport;
          _preferredBiometric = preferredBiometric;
        });
      }
    } catch (e) {
      debugPrint('Error checking biometric support: $e');
      if (mounted) {
        setState(() {
          _hasBiometricSupport = false;
          _preferredBiometric = null;
        });
      }
    }
  }

  Future<void> _handleKeyPressed(String keys, bool isKey) async {
    setState(() {
      if (!isKey) {
        _pinController.text = _pinController.text.isNotEmpty
            ? _pinController.text.substring(0, _pinController.text.length - 1)
            : '';
      } else {
        _pinController.text = "${_pinController.text}$keys";
      }
      _pinController.selection = TextSelection.fromPosition(
        TextPosition(offset: _pinController.text.length),
      );
    });
  }

  Future<void> _handleOnSubmitted(String enteredPin) async {
    debugPrint('You can submit pin now $enteredPin');
    setState(() {
      _isLoading = true;
    });
    _savePinForBiometrics(enteredPin);

    context.read<AuthBloc>().add(LoginWithPinEvent(pin: enteredPin));
  }

  Future<void> _handlePinSubmit() async {
    if (_enteredPin.length != 6) {
      CustomToastification(
        context,
        message: 'Please enter a 6 digits PIN',
      );
      return;
    }

    setState(() => _isLoading = true);

    // Save PIN for biometric login - remove await since function is void
    _savePinForBiometrics(_enteredPin);

    context.read<AuthBloc>().add(LoginWithPinEvent(pin: _enteredPin));
  }

  Future<void> _savePinForBiometrics(String pin) async {
    final deviceController = Get.find<DeviceCheckController>();
    await deviceController.storePin(pin);
  }

  Future<void> _handleBiometricAuth() async {
    if (_isBiometricAuthInProgress) return;

    try {
      setState(() => _isBiometricAuthInProgress = true);

      // Check if we have a stored PIN first
      final deviceController = Get.find<DeviceCheckController>();
      final storedPin = await deviceController.getStoredPin();

      if (storedPin == null) {
        setState(() => _isBiometricAuthInProgress = false);
        CustomToastification(
          context,
          message: 'Please set up PIN first before using biometric login',
        );
        return;
      }

      // Trigger biometric check
      context.read<AuthBloc>().add(const CheckBiometricEvent());
    } catch (e) {
      setState(() => _isBiometricAuthInProgress = false);
      CustomToastification(
        context,
        message: 'Biometric authentication failed: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listenWhen: (previous, current) =>
          current is LoggedInWithPinState ||
          current is AuthError ||
          current is TokenExpiredState ||
          current is BiometricAuthenticatedState,
      listener: (context, state) {
        debugPrint('Token Device Login State: ${state.runtimeType}');
        if (state is LoggedInWithPinState) {
          debugPrint('Navigating to home after successful login');
          context.go(AppRouteName.home);
        } else if (state is BiometricAuthenticatedState) {
          // Get stored PIN and login
          final deviceController = Get.find<DeviceCheckController>();
          deviceController.getStoredPin().then((storedPin) {
            if (storedPin != null) {
              context.read<AuthBloc>().add(LoginWithPinEvent(pin: storedPin));
            } else {
              setState(() => _isBiometricAuthInProgress = false);
              CustomToastification(
                context,
                message: 'No stored PIN found. Please login with PIN first',
              );
            }
          });
        } else if (state is AuthError) {
          _pinController.clear();
          _enteredPin = '';
          if (!state.message.contains('Session expired')) {
            CustomToastification(
              context,
              message: state.message,
            );
          }
          setState(() {
            _isLoading = false;
            _isBiometricAuthInProgress = false;
          });
        } else if (state is TokenExpiredState) {
          setState(() {
            _isLoading = false;
            _isBiometricAuthInProgress = false;
          });
        }
      },
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        child: Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final screenHeight = MediaQuery.sizeOf(context).height;
                return SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: constraints.maxHeight,
                    ),
                    child: IntrinsicHeight(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const SizedBox(
                                    height: 12,
                                  ),
                                  _buildHeader(Theme.of(context)),
                                  SizedBox(
                                    height: screenHeight * 0.02,
                                  ),
                                  _buildPinInput(Theme.of(context)),
                                  SizedBox(
                                    height: screenHeight * 0.02,
                                  ),
                                  _buildShowPinButton(Theme.of(context)),
                                  SizedBox(
                                    height: screenHeight * 0.022,
                                  ),
                                  _buildBiometricButton(Theme.of(context)),
                                  SizedBox(
                                    height: screenHeight * 0.016,
                                  ),
                                  _buildForgotPinButton(Theme.of(context)),
                                ],
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(
                              bottom: Platform.isIOS ? 24.h : 0,
                            ),
                            child: CustomPasswordPad(
                              controller: _pinController,
                              onChanged: _handleKeyPressed,
                              onSubmitted: _handleOnSubmitted,
                              isLoading: _isLoading,
                            ),
                            //_buildNumericKeypad(Theme.of(context)),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return BlocBuilder<AuthBloc, AuthState>(
      buildWhen: (previous, current) =>
          previous is AuthInitial || current is LoggedInWithPinState,
      builder: (context, state) {
        var userName = '';

        // First try to get name from auth state
        if (state is LoggedInWithPinState) {
          userName = state.user.fullName;
        }

        // If empty, try to get from device check data
        if (userName.isEmpty) {
          final deviceController = Get.find<DeviceCheckController>();
          final deviceData = deviceController.deviceData;
          if (deviceData != null) {
            userName = deviceData.fullName;
          }
        }

        return Column(
          children: [
            Image.asset(
              MediaRes.connectBirrMainLogo,
              height: 110.h,
            ),
            SizedBox(
              height: 16,
            ),
            Text(
              '👋 Welcome back!',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.normal,
                color: Colors.black.withOpacity(0.4),
              ),
            ),
            if (userName.isNotEmpty) ...[
              SizedBox(height: 8.h),
              CustomBuildText(
                text: userName,
                style: GoogleFonts.outfit(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
            SizedBox(height: 8.h),
            Text(
              'Enter your PIN and sign in to your Connect account.',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: Colors.black.withOpacity(0.4),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }

  Widget _buildPinInput(ThemeData theme) {
    final defaultTheme = PinTheme(
      width: 56.w,
      height: 56.h,
      textStyle: GoogleFonts.outfit(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: theme.primaryColor,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFF2C2B34).withOpacity(0.04),
        borderRadius: BorderRadius.circular(12.r),
      ),
    );
    return Center(
      child: Pinput(
        length: 6,
        controller: _pinController,
        obscureText: _isObscured,
        obscuringCharacter: '*',
        obscuringWidget: Padding(
          padding: EdgeInsets.only(top: 2.h),
          child: Text(
            '*',
            style: GoogleFonts.roboto(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Colors.black,
            ),
          ),
        ),
        readOnly: true,
        defaultPinTheme: defaultTheme,
        focusedPinTheme: defaultTheme,
        submittedPinTheme: defaultTheme,
        separatorBuilder: (index) => SizedBox(width: 8.w),
      ),
    );
  }

  Widget _buildShowPinButton(ThemeData theme) {
    return InkWell(
      onTap: () {
        setState(() {
          _isObscured = !_isObscured;
        });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _isObscured ? 'Show PIN' : 'Hide PIN',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          Image.asset(
            _isObscured ? MediaRes.eyeOpen : MediaRes.eyeClose,
            color: Theme.of(context).primaryColor,
            width: 20.h,
            height: 20.h,
          ),
        ],
      ),
    );
  }

  Widget _buildForgotPinButton(ThemeData theme) {
    return InkWell(
      onTap: () {
        context.pushNamed(AppRouteName.forgotPin);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Forgot PIN',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricButton(ThemeData theme) {
    // Only show if biometrics are supported and available
    if (!_hasBiometricSupport || _preferredBiometric == null) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: _isBiometricAuthInProgress ? null : _handleBiometricAuth,
      child: Container(
        width: 72.w,
        height: 72.h,
        padding: EdgeInsets.symmetric(horizontal: 13.w, vertical: 11.h),
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Color(0xFFF8F8F8),
        ),
        child: Image.asset(
          _preferredBiometric == BiometricType.face
              ? MediaRes.faceIdIcon
              : MediaRes.fingerPrintIcon,
          width: 45.w,
          height: 48.h,
          color: _isBiometricAuthInProgress
              ? theme.primaryColor.withOpacity(0.5)
              : theme.primaryColor,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }
}
