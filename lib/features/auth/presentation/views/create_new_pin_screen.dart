import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_password_pad.dart';
import 'package:cbrs/core/common/widgets/custom_pin_input.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/form_validation.dart';
import 'package:cbrs/features/auth/presentation/widget/show_pin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class CreateNewPinScreen extends StatefulWidget {
  const CreateNewPinScreen({required this.source, super.key});
  final String source;
  @override
  State<CreateNewPinScreen> createState() => _CreateNewPinScreenState();
}

class _CreateNewPinScreenState extends State<CreateNewPinScreen> {
  final TextEditingController _controller = TextEditingController();

  bool _isObscured = true;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create PIN'),),
      body: LayoutBuilder(
        builder: (context, constraints) => SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: IntrinsicHeight(
              child: Container(
                padding: const EdgeInsets.all(2),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Spacer(),
                    CustomPagePadding(
                      child: Column(
                        children: [
                          Image.asset(
                            MediaRes.connectBirrMainLogo,
                            height: 100.h,
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          const CustomBuildText(
                            text: 'Create PIN',
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            caseType: '',
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          const CustomBuildText(
                            text:
                                'Create a new secure PIN and confirm it. Make sure to use a strong PIN.',
                            fontSize: 12,
                            color: Color(0xFFAAAAAA),
                            textAlign: TextAlign.center,
                            caseType: '',
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          // color: Colors.red,
                          CustomPinInput(
                            controller: _controller,
                            isObscured: _isObscured,
                          ),

                          const SizedBox(
                            height: 36,
                          ),
                          ShowPinText(
                            onTap: () {
                              setState(() {
                                _isObscured = !_isObscured;
                              });
                            },
                            isObscured: _isObscured,
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          const CustomBuildText(
                            text:
                                'A 6-digit PIN that is secure and easy to remember',
                            fontSize: 12,
                            color: Color(0xFFAAAAAA),
                            textAlign: TextAlign.center,
                            caseType: '',
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    CustomPasswordPad(
                      controller: _controller,
                      onChanged: _handleKeyPressed,
                      isLoading: _isLoading,
                      onSubmitted: _handleOnSubmitted,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleKeyPressed(String keys, bool isKey) async {
    setState(() {
      if (!isKey) {
        _controller.text = _controller.text.isNotEmpty
            ? _controller.text.substring(0, _controller.text.length - 1)
            : '';
      } else {
        _controller.text = "${_controller.text}$keys";
      }
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    });
  }

  Future<void> _handleOnSubmitted(String enteredPin) async {
    debugPrint('You can submit pin now $enteredPin');

    setState(() {
      _isLoading = true;
    });

    final value = FormValidation.validatePin(enteredPin, 'New Pin');

    if (value != null) {
      CustomToastification(context, message: value);
      setState(() {
        _isLoading = false;
      });

      return;
    }

    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      _isLoading = false;
    });

    context.pushNamed(AppRouteName.confirmPin,
        extra: {'source': widget.source, 'newPin': enteredPin});
        
  }
}
