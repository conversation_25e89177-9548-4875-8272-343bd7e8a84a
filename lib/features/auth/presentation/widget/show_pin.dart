// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ShowPinText extends StatelessWidget {
  const ShowPinText({
    Key? key,
    this.onTap,
    required this.isObscured,
  }) : super(key: key);

  final VoidCallback? onTap;
  final bool isObscured;

  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,

      //  () {
      //   setState(() {
      //     _isObscured = !_isObscured;
      //   });
      // },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomBuildText(
          text:  isObscured ? 'Show PIN' : 'Hide PIN',
              fontSize: 14.sp,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w600,
          ),
          const SizedBox(
            width: 8,
          ),
          Image.asset(
            isObscured ? MediaRes.eyeOpen : MediaRes.eyeClose,
            color: Theme.of(context).primaryColor,
            width: 20.h,
            height: 20.h,
          ),
        ],
      ),
    );
  }
}
