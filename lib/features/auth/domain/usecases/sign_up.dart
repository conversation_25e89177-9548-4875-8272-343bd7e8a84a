import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/data/models/sign_up_response.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';

class SignUp extends UsecaseWithParams<SignUpResponse, SignUpParams> {
  const SignUp(this._repository);
  
  final AuthRepository _repository;
  
  @override
  ResultFuture<SignUpResponse> call(SignUpParams params) => _repository.signUp(
    fullName: params.fullName,
    email: params.email,
    phoneNumber: params.phoneNumber,
    city: params.city,
    country: params.country,
    gender: params.gender,
    dateOfBirth: params.dateOfBirth
  );
}