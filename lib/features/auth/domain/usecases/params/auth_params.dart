import 'package:equatable/equatable.dart';

class EmailParams extends Equatable {
  const EmailParams({required this.email});
  final String email;

  @override
  List<Object?> get props => [email];
}

class PhoneParams extends Equatable {
  const PhoneParams({required this.phoneNumber});
  final String phoneNumber;

  @override
  List<Object?> get props => [phoneNumber];
}

class OtpParams extends Equatable {
  const OtpParams({
    required this.phoneNumber,
    required this.otp,
    required this.source,
  });

  final String phoneNumber;
  final String otp;
  final String source;

  @override
  List<Object?> get props => [phoneNumber, otp, source];
}

class EmailVerificationParams extends Equatable {
  const EmailVerificationParams({
    required this.email,
    required this.otp,
    required this.source,
  });

  final String email;
  final String otp;
  final String source;

  @override
  List<Object?> get props => [email, otp, source];
}

class SignUpParams extends Equatable {
  const SignUpParams({
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    required this.city,
    required this.country,
    required this.dateOfBirth,
    required this.gender,
  });

  final String fullName;
  final String email;
  final String phoneNumber;
  final String city;
  final String country;
  final String dateOfBirth;
  final String gender;

  @override
  List<Object?> get props =>
      [fullName, email, phoneNumber, city, country, dateOfBirth, gender];
}

class PinParams extends Equatable {
  final String pin;
  final String? email;
  final String? source;

  const PinParams({
    required this.pin,
    this.email,
    this.source,
  });

  @override
  List<Object?> get props => [pin, email, source];
}

class ResetPinParams extends Equatable {
  const ResetPinParams({
    required this.newPin,
    required this.confirmPin,
  });

  final String newPin;
  final String confirmPin;

  @override
  List<Object?> get props => [newPin, confirmPin];
}

class DeviceParams extends Equatable {
  const DeviceParams({this.email, this.phoneNumber});

  final String? email;
  final String? phoneNumber;

  @override
  List<Object?> get props => [email, phoneNumber];
}
