import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';
import 'package:dartz/dartz.dart';

class SignInWithEmail extends UsecaseWithParams<void, EmailParams> {
  const SignInWithEmail(this._repository);
  
  final AuthRepository _repository;
  
  @override
  Future<Either<Failure, ResponseHandler>>call(EmailParams params) => 
      _repository.signInWithEmail(email: params.email);
}