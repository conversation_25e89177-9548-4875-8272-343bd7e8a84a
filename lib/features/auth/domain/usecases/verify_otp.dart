import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';

class VerifyOtp extends UsecaseWithParams<void, OtpParams> {
  const VerifyOtp(this._repository);
  
  final AuthRepository _repository;
  
  @override
  ResultFuture<ResponseHandler> call(OtpParams params) => _repository.verifyOtp(
    phoneNumber: params.phoneNumber,
    otp: params.otp,
    source: params.source,
  );
}