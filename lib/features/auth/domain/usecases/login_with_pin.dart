import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';

class LoginWithPin extends UsecaseWithParams<LocalUser, PinParams> {
  const LoginWithPin(this._repository);
  
  final AuthRepository _repository;
  
  @override
  ResultFuture<LocalUser> call(PinParams params) => 
      _repository.loginWithPin(pin: params.pin);
}