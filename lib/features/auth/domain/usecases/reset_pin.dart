import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';

class ResetPin extends UsecaseWithParams<void, ResetPinParams> {
  const ResetPin(this._repository);
  
  final AuthRepository _repository;
  
  @override
  ResultFuture<ResponseHandler> call(ResetPinParams params) => _repository.resetPin(
    newPin: params.newPin,
    confirmPin: params.confirmPin,
  );
}