import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';
import 'package:dartz/dartz.dart';

class ForgotPinWithPhone extends UsecaseWithParams<void, PhoneParams> {
  const ForgotPinWithPhone(this._repository);
  
  final AuthRepository _repository;
  
  @override
   Future<Either<Failure, ResponseHandler>> call(PhoneParams params) => 
      _repository.forgotPinWithPhone(phoneNumber: params.phoneNumber);
}