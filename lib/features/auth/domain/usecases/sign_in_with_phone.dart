import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';

class SignInWithPhone extends UsecaseWithParams<void, PhoneParams> {
  const SignInWithPhone(this._repository);
  
  final AuthRepository _repository;
  
  @override
  ResultFuture<ResponseHandler> call(PhoneParams params) => 
      _repository.signInWithPhone(phoneNumber: params.phoneNumber);
}
