import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import 'package:cbrs/core/extensions/string_extensions.dart';

@immutable
class LocalUser extends Equatable {
  const LocalUser({
    required this.id,
    required this.memberCode,
    required this.firstName,
    required this.lastName,
    required this.address,
    required this.city,
    required this.country,
    this.phoneNumber,
    this.email,
    this.realm,
    this.token,
    required this.status,
    this.agoraUsername,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.dateJoined,
    required this.lastModified,
    required this.createdAt,
    required this.deviceUUID,
    this.avatar = '',
    required this.dateOfBirth,
    required this.gender,
    required this.memberLevel,
  });
  final String id;
  final String memberCode;
  final String firstName;
  final String lastName;
  final String address;
  final String city;
  final String country;
  final String? phoneNumber;
  final String? email;
  final String? realm;
  final String? token;
  final String status;
  final String? agoraUsername;
  final bool isEmailVerified;
  final bool isPhoneVerified;

  final String dateJoined;
  final String lastModified;
  final String createdAt;

  final String deviceUUID;
  final String avatar;
  final String dateOfBirth;
  final String gender;
  final MemberLevel memberLevel;

  LocalUser copyWith({
    String? id,
    String? memberCode,
    String? firstName,
    String? lastName,
    String? address,
    String? city,
    String? country,
    String? phoneNumber,
    String? email,
    String? realm,
    String? token,
    String? agoraUsername,
    bool? enabled,
    bool? isDeleted,
    bool? isVerified,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    String? dateJoined,
    String? lastModified,
    String? createdAt,
    String? deviceUUID,
    String? avatar,
    String? dateOfBirth,
    String? gender,
    MemberLevel? memberLevel,
  }) {
    return LocalUser(
      id: id ?? this.id,
      memberCode: memberCode ?? this.memberCode,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      realm: realm ?? this.realm,
      token: token ?? this.token,
      status: status ?? this.status,
      agoraUsername: agoraUsername ?? this.agoraUsername,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      dateJoined: dateJoined ?? this.dateJoined,
      lastModified: lastModified ?? this.lastModified,
      createdAt: createdAt ?? this.createdAt,
      deviceUUID: deviceUUID ?? this.deviceUUID,
      avatar: avatar ?? this.avatar,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      memberLevel: memberLevel ?? this.memberLevel,
    );
  }

  // Helper methods
  String get fullName => '$firstName $lastName'.trim().capitalizeFirstofEach;
  bool get isEmpty => id.isEmpty;
  bool get isNotEmpty => id.isNotEmpty;

  @override
  List<Object?> get props => [
        id,
        memberCode,
        firstName,
        lastName,
        address,
        city,
        country,
        phoneNumber,
        email,
        realm,
        token,
        isEmailVerified,
        isPhoneVerified,
        dateJoined,
        lastModified,
        deviceUUID,
        avatar,
        dateOfBirth,
        gender,
        memberLevel,
        agoraUsername,
      ];

  // Empty user singleton
  static const empty = LocalUser(
    id: '',
    memberCode: '',
    firstName: '',
    lastName: '',
    address: '',
    city: '',
    country: '',
    phoneNumber: '',
    email: '',
    realm: '',
    isEmailVerified: false,
    isPhoneVerified: false,
    dateJoined: '',
    lastModified: '',
    createdAt: '',
    status: 'ACTIVE',
    agoraUsername: '',
    deviceUUID: '',
    dateOfBirth: '',
    gender: '',
    memberLevel: MemberLevel(level: 'LEVEL_ONE', levelStatus: 'PENDING'),
  );
}

class MemberLevel extends Equatable {
  const MemberLevel({required this.level, required this.levelStatus});

  MemberLevel copyWith({
    String? level,
    String? levelStatus,
  }) {
    return MemberLevel(
      level: level ?? this.level,
      levelStatus: levelStatus ?? this.levelStatus,
    );
  }

  final String level;
  final String levelStatus;

  Map<String, dynamic> toJson() => {
        'level': level,
        'levelStatus': levelStatus,
      };
  @override
  List<Object?> get props => [level, levelStatus];
}
