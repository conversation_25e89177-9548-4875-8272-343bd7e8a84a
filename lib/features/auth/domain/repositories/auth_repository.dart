import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/auth/data/models/sign_up_response.dart';

abstract class AuthRepository {
ResultFuture<ResponseHandler> signInWithEmail({
    required String email,
  });

ResultFuture<ResponseHandler>  signInWithPhone({
    required String phoneNumber,
  });

ResultFuture<ResponseHandler> verifyOtp({
    required String phoneNumber,
    required String otp,
    required String source,
  });

    ResultFuture<ResponseHandler> verifyEmail({
    required String email,
    required String otp,
    required String source,
  });

  ResultFuture<ResponseHandler> resendOtp({
    required String phoneNumber,
  });

ResultFuture<ResponseHandler>  resendEmailVerification({
    required String email,
  });

  ResultFuture<SignUpResponse> signUp({
    required String fullName,
    required String email,
    required String phoneNumber,
    required String city,
    required String country,
    required String dateOfBirth,
    required String gender,

  });

 ResultFuture<ResponseHandler> createPin({
    required String pin,
    required String source,
  });

  ResultFuture<LocalUser> loginWithPin({
    required String pin,
  });

ResultFuture<ResponseHandler>forgotPin({
    required String email,
  });

  ResultFuture<ResponseHandler> forgotPinWithPhone({
    required String phoneNumber,
  });

ResultFuture<ResponseHandler> resetPin({
    required String newPin,
    required String confirmPin,
  });

  ResultFuture<ResponseHandler> unlinkDevice({
   String? email,
   String? phoneNumber,
  });

  ResultVoid logout();
}
