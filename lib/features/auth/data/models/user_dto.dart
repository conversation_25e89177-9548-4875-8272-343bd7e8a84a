import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:flutter/foundation.dart';

@immutable
class LocalUserDTO extends LocalUser {
  const LocalUserDTO({
    required super.id,
    required super.memberCode,
    required super.firstName,
    required super.lastName,
    required super.address,
    required super.city,
    required super.country,
    required super.realm,
    required super.isEmailVerified,
    required super.isPhoneVerified,
    required super.dateJoined,
    required super.lastModified,
    required super.createdAt,
    required super.status,
    required super.deviceUUID,
    required super.dateOfBirth,
    required super.gender,
    required super.memberLevel,
    super.phoneNumber,
    super.email,
    super.token,
    super.avatar,
    super.agoraUsername,
  });

  factory LocalUserDTO.fromJson(Map<String, dynamic> json) {
    final userData =
        json.containsKey('data') ? json['data'] as Map<String, dynamic> : json;

    debugPrint("mmmber keR ${userData['memberLevel']}");
    debugPrint(
        "Agora credentials in userData: username=${userData['agoraUsername']}");
    return LocalUserDTO(
      id: safeString(userData['_id']),
      memberCode: safeString(userData['memberCode']),
      firstName: safeString(userData['firstName']),
      lastName: safeString(userData['lastName']),
      address: safeString(userData['address']),
      city: safeString(userData['city']),
      country: safeString(userData['country']),
      phoneNumber: safeString(userData['phoneNumber']),
      email: safeString(userData['email']),
      realm: safeString(userData['realm']),
      token: safeString(userData['token']),
      agoraUsername: safeString(userData['agoraUsername']),

      status: safeString(userData['status']),

      isEmailVerified: safeBool(userData['isEmailVerified']),
      isPhoneVerified: safeBool(userData['isPhoneVerified']),
      dateJoined: safeString(userData['createdAt']),
      lastModified: safeString(userData['lastModified']),
      createdAt: safeString(userData['createdAt']),

      deviceUUID: safeString(userData['deviceUUID']),
      avatar: safeString(userData['avatar']),
      dateOfBirth: safeString(userData['dateOfBirth']),
      gender: safeString(userData['gender'] as String?),
      memberLevel: userData['memberLevel'] != null
          ? MemberLevelDTO.fromJson(
              userData['memberLevel'] as Map<String, dynamic>,
            )
          : const MemberLevelDTO(level: 'Level_ONE', levelStatus: 'PENDING'),
      //safeString(userData['memberLevel'] as String?),
    );
  }

  Map<String, dynamic> toJson() => {
        'data': {
          '_id': id,
          'memberCode': memberCode,
          'firstName': firstName,
          'lastName': lastName,
          'address': address,
          'city': city,
          'country': country,
          'phoneNumber': phoneNumber,
          'email': email,
          'realm': realm,
          'status': status,
          'agoraUsername': agoraUsername,
          'isEmailVerified': isEmailVerified,
          'isPhoneVerified': isPhoneVerified,
          'dateJoined': dateJoined,
          'lastModified': lastModified,
          'createdAt': createdAt,
          'deviceUUID': deviceUUID,
          'avatar': avatar,
          'dateOfBirth': dateOfBirth,
          'gender': gender,
          'memberLevel': memberLevel.toJson(),
        },
        'accessToken': token,
      };

  @override
  LocalUserDTO copyWith({
    String? id,
    String? memberCode,
    String? firstName,
    String? lastName,
    String? address,
    String? city,
    String? country,
    String? phoneNumber,
    String? email,
    String? realm,
    String? token,
    String? agoraUsername,
    bool? enabled,
    bool? isDeleted,
    bool? isVerified,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    String? dateJoined,
    String? lastModified,
    String? createdAt,
    String? deviceUUID,
    String? avatar,
    String? dateOfBirth,
    String? gender,
    MemberLevel? memberLevel,
    String? agoraToken,
  }) {
    return LocalUserDTO(
      id: id ?? this.id,
      memberCode: memberCode ?? this.memberCode,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      realm: realm ?? this.realm,
      token: token ?? this.token,
      agoraUsername: agoraUsername ?? this.agoraUsername,
      status: status ?? status,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      dateJoined: dateJoined ?? this.dateJoined,
      lastModified: lastModified ?? this.lastModified,
      createdAt: createdAt ?? this.createdAt,
      deviceUUID: deviceUUID ?? this.deviceUUID,
      avatar: avatar ?? this.avatar,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      memberLevel: memberLevel ?? this.memberLevel,
    );
  }

  static LocalUserDTO fromLocalUser(LocalUser user) {
    return LocalUserDTO(
      id: user.id,
      memberCode: user.memberCode,
      firstName: user.firstName,
      lastName: user.lastName,
      address: user.address,
      city: user.city,
      country: user.country,
      phoneNumber: user.phoneNumber,
      email: user.email,
      realm: user.realm,
      token: user.token,
      agoraUsername: user.agoraUsername,
      status: user.status,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      dateJoined: user.dateJoined,
      lastModified: user.lastModified,
      createdAt: user.createdAt,
      deviceUUID: user.deviceUUID,
      avatar: user.avatar,
      dateOfBirth: user.dateOfBirth,
      gender: user.gender,
      memberLevel: user.memberLevel,
    );
  }
}

class MemberLevelDTO extends MemberLevel {
  const MemberLevelDTO({
    required super.level,
    required super.levelStatus,
  });

  factory MemberLevelDTO.fromJson(Map<String, dynamic> json) {
    return MemberLevelDTO(
      level: safeString(json['level']),
      levelStatus: safeString(json['levelStatus']),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        'level': level,
        'levelStatus': levelStatus,
      };

  @override
  MemberLevelDTO copyWith({
    String? level,
    String? levelStatus,
  }) {
    return MemberLevelDTO(
      level: level ?? this.level,
      levelStatus: levelStatus ?? this.levelStatus,
    );
  }
}

String safeString(dynamic value) {
  if (value == null || (value is String && value.trim().isEmpty)) {
    return '';
  }
  return value.toString();
}

bool safeBool(dynamic value) {
  if (value == null || value is! bool) {
    return false;
  }
  return value;
}
