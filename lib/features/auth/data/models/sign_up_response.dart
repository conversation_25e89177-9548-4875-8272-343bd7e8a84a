class SignUpResponse {
  final String token;
  final int otpCode;
  final String message;
  final bool success;

  const SignUpResponse({
    required this.token,
    required this.otpCode,
    required this.message,
    this.success = true,
  });

  factory SignUpResponse.fromJson(Map<String, dynamic> json) {
    return SignUpResponse(
      success: true,
      message: safeString(json['message']),
      token: safeString(json['token']),
      otpCode: safeInt(json['otpCode']),
    );
  }

  static bool safeBool(dynamic value) {
    if (value == null || value is! bool) {
      return false;
    }
    return value;
  }

  static String safeString(dynamic value) {
    if (value == null || (value is String && value.trim().isEmpty)) {
      return '';
    }
    return value.toString();
  }

  static int safeInt(dynamic value) {
    if (value == null) {
      return 0;
    }
    if (value is int) {
      return value;
    }
    if (value is String) {
      return int.tryParse(value.trim()) ?? 0;
    }
    return 0;
  }
}
