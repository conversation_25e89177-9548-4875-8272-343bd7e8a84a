import 'package:equatable/equatable.dart';

class DeviceCheckResponse extends Equatable {
  final bool success;
  final DeviceCheckData? data;

  const DeviceCheckResponse({
    required this.success,
    this.data,
  });

  factory DeviceCheckResponse.fromJson(Map<String, dynamic> json) {
    return DeviceCheckResponse(
      success: json['success'] as bool? ?? false,
      data: json['data'] != null ? DeviceCheckData.fromJson(json['data'] as Map<String, dynamic>) : null,
    );
  }

  @override
  List<Object?> get props => [success, data];
}

class DeviceCheckData extends Equatable {
  final String? email;
  final String? phoneNumber;
  final String? firstName;
  final String? lastName;
  final String? avatar;

 String get fullName => '$firstName $lastName';

  const DeviceCheckData({
    this.email,
    this.phoneNumber,
    this.firstName,
    this.lastName,
    this.avatar,
  });

  factory DeviceCheckData.fromJson(Map<String, dynamic> json) {
    return DeviceCheckData(
      email: json['email'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      avatar: json['avatar'] as String?,
    );
  }

  @override
  List<Object?> get props => [
    email,
    phoneNumber,
    firstName,
    lastName,
    avatar,
  ];
}
