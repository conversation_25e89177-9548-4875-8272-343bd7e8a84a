import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/profile/domain/entities/email_add_response.dart';
import 'package:cbrs/features/profile/domain/entities/pin_response.dart';

class ApiResponseModel extends ResponseHandler {
  const ApiResponseModel({
    required bool success,
    required String message,
   
  }) : super(success: success, message: message, );

  factory ApiResponseModel.fromJson(Map<String, dynamic> json) {
    return ApiResponseModel(
      success: safeBool(json['success']),
      message: safeString(json['message']),
   
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
    };
  }

  static bool safeBool(dynamic value) {
    if (value == null || value is! bool) {
      return false;
    }
    return value;
  }

  static String safeString(dynamic value) {
    if (value == null || (value is String && value.trim().isEmpty)) {
      return '';
    }
    return value.toString();
  }
}
