import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/services/device/device_service.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/data/models/device_check_response.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class DeviceCheckRepository {
  DeviceCheckRepository({
    required ApiService apiService,
    required DeviceService deviceService,
    required AuthLocalDataSource authLocalDataSource,
  })  : _apiService = apiService,
        _deviceService = deviceService,
        _authLocalDataSource = authLocalDataSource;
  final ApiService _apiService;
  final DeviceService _deviceService;
  final AuthLocalDataSource _authLocalDataSource;

  Future<bool> checkDevice() async {
    try {
      final deviceId = await _deviceService.getDeviceId();

      // Continue with device check
      debugPrint('device check api call');
      return await _performDeviceCheck(deviceId);
    } catch (e) {
      debugPrint('Device check error: $e');
      rethrow;
    }
  }

  Future<bool> _performDeviceCheck(String deviceId) async {
    debugPrint('  GlobalVariable.fcmToken ${GlobalVariable.fcmToken}');
    debugPrint('device iddd $deviceId ');
    try {
      final postData = {
        'device_uuid': deviceId,
        'fcmToken': GlobalVariable.fcmToken,
      };

      final result = await _apiService.post(
        ApiEndpoints.deviceCheck,
        data: postData,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        (data) {
          debugPrint('Succes response for Device Check');
          GlobalVariable.isDeviceRegisteredOnConnect = true;

          GlobalVariable.deviceCheckNetworkError = false;
          return true;

          final responseData = data['data'] as Map<String, dynamic>;

          //  return DeviceCheckResponse.fromJson(responseData);
        },
        (error) 
        {
          final statusCode = error.statusCode ?? 500;
          final message = error.message;

          if ('$statusCode'.startsWith('4')) {
            GlobalVariable.deviceCheckNetworkError = false;
            GlobalVariable.isDeviceRegisteredOnConnect = false;

            throw ApiException(message: message, statusCode: statusCode);
          }
          GlobalVariable.deviceCheckNetworkError = true;

          GlobalVariable.isDeviceRegisteredOnConnect = false;
          throw ApiException(
            message: error.message,
            statusCode: error.statusCode ?? 500,
          );
        },
      );
    } on ApiException catch (err) {
      rethrow;
    } catch (err) {
      throw const ApiException(
        message:
            'Un expceted error happened. Please reach out our customer supports.',
        statusCode: 500,
      );
    }

/*
      final response = await _dio.post<Map<String, dynamic>>(
        ApiEndpoints.deviceCheck,
        data: {
          'device_uuid': deviceId,
          'fcmToken': GlobalVariable.fcmToken,
        },
      );

      debugPrint('Resoons from the server for device chesk $response');
      if (response.statusCode != 200) {
        GlobalVariable.isDeviceRegisteredOnConnect = false;
        throw ApiException(
          message: 'This device is not registered on connect',
          statusCode: response.statusCode ?? 400,
        );
      }

      if (response.data == null) {
        GlobalVariable.isDeviceRegisteredOnConnect = false;

        throw ApiException(
          message: 'This device is not registered on connect',
          statusCode: response.statusCode ?? 400,
        );
      }
      debugPrint('device checked from the app $response');
      // GlobalVariable.isDeviceRegisteredOnConnect = true;

      if (response.statusCode == 200)
        GlobalVariable.isDeviceRegisteredOnConnect = true;

      GlobalVariable.deviceCheckNetworkError = false;

      return DeviceCheckResponse.fromJson(response.data!);
    } on ApiException catch (err) {
      rethrow;
    } catch (err) {
      GlobalVariable.deviceCheckNetworkError = true;

      debugPrint('Error  from the server for devic echeck $err');
      throw const ApiException(
        message: 'Failed to check device.',
        statusCode: 404,
      );
    }
*/
  }

  bool _isTimeoutError(DioExceptionType type) {
    return type == DioExceptionType.connectionTimeout ||
        type == DioExceptionType.sendTimeout ||
        type == DioExceptionType.receiveTimeout;
  }
}
