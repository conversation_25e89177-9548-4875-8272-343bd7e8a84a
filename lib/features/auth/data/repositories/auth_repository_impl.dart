import 'package:cbrs/core/services/firebase_notification_service/fcm_service.dart';
import 'package:cbrs/features/auth/data/models/api_response_model.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/auth/data/datasources/auth_remote_datasource.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/auth/domain/repositories/auth_repository.dart';
import 'package:cbrs/features/auth/data/models/sign_up_response.dart';

class AuthRepositoryImpl implements AuthRepository {
  const AuthRepositoryImpl({
    required AuthRemoteDataSource remoteDataSource,
    required AuthLocalDataSource localDataSource,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource;

  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  @override
  ResultFuture<ResponseHandler> signInWithEmail({required String email}) async {
    try {
      final returnData = await _remoteDataSource.signInWithEmail(email);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> signInWithPhone({
    required String phoneNumber,
  }) async {
    try {
      final returnData = await _remoteDataSource.signInWithPhone(phoneNumber);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> verifyOtp({
    required String phoneNumber,
    required String otp,
    required String source,
  }) async {
    try {
      final returnData =
          await _remoteDataSource.verifyOtp(phoneNumber, otp, source);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> verifyEmail({
    required String email,
    required String otp,
    required String source,
  }) async {
    try {
      final returnData =
          await _remoteDataSource.verifyEmail(email, otp, source);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> resendOtp({required String phoneNumber}) async {
    try {
      final returnData = await _remoteDataSource.signInWithPhone(phoneNumber);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> resendEmailVerification({
    required String email,
  }) async {
    try {
      final returnData = await _remoteDataSource.signInWithEmail(email);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<SignUpResponse> signUp({
    required String fullName,
    required String email,
    required String phoneNumber,
    required String city,
    required String country,
    required String dateOfBirth,
    required String gender,
  }) async {
    try {
      final response = await _remoteDataSource.signUp(
        fullName: fullName,
        email: email,
        phoneNumber: phoneNumber,
        city: city,
        country: country,
        dateOfBirth: dateOfBirth,
        gender: gender,
      );

      // Save the auth token if present
      if (response.token.isNotEmpty) {
        await _localDataSource.saveAuthToken(response.token);
      }

      return Right(response);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> createPin({
    required String pin,
    required String source,
  }) async {
    try {
      final returnData = await _remoteDataSource.createPin(pin, source);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<LocalUser> loginWithPin({required String pin}) async {
    try {
      final userDto = await _remoteDataSource.loginWithPin(pin);

      await Future.wait([
        _localDataSource.saveAuthToken(userDto.token!),
        _localDataSource.cacheUserData(userDto),
      ]);
      return Right(userDto);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> forgotPin({required String email}) async {
    try {
      final returnData = await _remoteDataSource.forgotPin(email);
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(AuthFailure(message: e.message));
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<ResponseHandler> forgotPinWithPhone({
    required String phoneNumber,
  }) async {
    try {
      final returnData =
          await _remoteDataSource.forgotPinWithPhone(phoneNumber);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> resetPin({
    required String newPin,
    required String confirmPin,
  }) async {
    try {
      final returnData = await _remoteDataSource.resetPin(newPin, confirmPin);
      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultFuture<ResponseHandler> unlinkDevice({
    String? email,
    String? phoneNumber,
  }) async {
    try {
      final returnData = await _remoteDataSource.unlinkDevice(
        email: email,
        phoneNumber: phoneNumber,
      );

      await updateUserStatus(isOnline: false);

      return Right(returnData);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }

  @override
  ResultVoid logout() async {
    try {
      await _remoteDataSource.logout();
      // await _localDataSource.clearAuthToken();
      return const Right(null);
    } on ApiException catch (err) {
      return Left(ServerFailure(message: err.message));
    } catch (err) {
      return Left(ServerFailure(message: err.toString()));
    }
  }
}
