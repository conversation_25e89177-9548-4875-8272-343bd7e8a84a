part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class SignInWithEmailEvent extends AuthEvent {
  const SignInWithEmailEvent({required this.email});

  final String email;

  @override
  List<Object?> get props => [email];
}

class SignInWithPhoneEvent extends AuthEvent {
  const SignInWithPhoneEvent({required this.phoneNumber});

  final String phoneNumber;

  @override
  List<Object?> get props => [phoneNumber];
}

class VerifyOtpEvent extends AuthEvent {
  const VerifyOtpEvent({
    required this.phoneNumber,
    required this.otp,
    required this.source,
  });

  final String phoneNumber;
  final String otp;
  final String source;

  @override
  List<Object?> get props => [phoneNumber, otp, source];
}

class VerifyEmailEvent extends AuthEvent {
  const VerifyEmailEvent({
    required this.email,
    required this.otp,
    required this.source,
  });

  final String email;
  final String otp;
  final String source;

  @override
  List<Object?> get props => [email, otp, source];
}

class ResendOtpEvent extends AuthEvent {
  const ResendOtpEvent({required this.phoneNumber});

  final String phoneNumber;

  @override
  List<Object?> get props => [phoneNumber];
}

class ResendEmailVerificationEvent extends AuthEvent {
  const ResendEmailVerificationEvent({required this.email});

  final String email;

  @override
  List<Object?> get props => [email];
}

class SignUpEvent extends AuthEvent {
  const SignUpEvent({
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    required this.city,
    required this.country,
    required this.dateOfBirth,
    required this.gender,
  });

  final String fullName;
  final String email;
  final String phoneNumber;
  final String city;
  final String country;
  final String dateOfBirth;
  final String gender;

  @override
  List<Object?> get props => [fullName, email, phoneNumber, city, country, dateOfBirth, gender];
}

class CreatePinEvent extends AuthEvent {
  final String pin;
  final String source;

  const CreatePinEvent({
    required this.pin,
    required this.source,
  });

  @override
  List<Object?> get props => [pin, source];
}

class LoginWithPinEvent extends AuthEvent {
  const LoginWithPinEvent({required this.pin});

  final String pin;

  @override
  List<Object?> get props => [pin];
}

class ForgotPinEvent extends AuthEvent {
  const ForgotPinEvent({required this.email});

  final String email;

  @override
  List<Object?> get props => [email];
}

class ForgotPinWithPhoneEvent extends AuthEvent {
  const ForgotPinWithPhoneEvent({required this.phoneNumber});

  final String phoneNumber;

  @override
  List<Object?> get props => [phoneNumber];
}

class ResetPinEvent extends AuthEvent {
  const ResetPinEvent({
    required this.newPin,
    required this.confirmPin,
  });

  final String newPin;
  final String confirmPin;

  @override
  List<Object?> get props => [newPin, confirmPin];
}

class UnlinkDeviceEvent extends AuthEvent {
  const UnlinkDeviceEvent();

  @override
  List<Object?> get props => [];
}

class LogoutEvent extends AuthEvent {
  const LogoutEvent();
}

class CheckBiometricEvent extends AuthEvent {
  const CheckBiometricEvent();
}

class InitializeAuthEvent extends AuthEvent {
  const InitializeAuthEvent();
}
