// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';

import 'package:cbrs/core/common/widgets/custom_transaction_card.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/my_connect/applications/bloc/my_connect_bloc.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_wallet/quick_wallet_recipients_bottom_sheet.dart';
import 'package:cbrs/features/my_connect/presentation/views/connect_requested_page.dart';
import 'package:cbrs/features/my_connect/presentation/widgets/my_connect_btn_card.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/presentation/widgets/transaction_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';

class MyConnectDetailPage extends StatefulWidget {
  const MyConnectDetailPage({
    super.key,
    required this.connection,
  });

  final ConnectionRequestEntity connection;

  @override
  State<MyConnectDetailPage> createState() => _MyConnectDetailPageState();
}

class _MyConnectDetailPageState extends State<MyConnectDetailPage> {
  // Helper method to get the connected person (not the current user)
  Future<Map<String, String>> _getConnectedPersonInfo() async {
    try {
      final currentUserId = await sl<AuthLocalDataSource>().getUserId();

      debugPrint('🔍 Detail Page - Current User ID: $currentUserId');
      debugPrint(
          '🔍 Detail Page - Requester ID: ${widget.connection.requester.id}');
      debugPrint(
          '🔍 Detail Page - Recipient ID: ${widget.connection.recipient?.id}');

      // Handle null values properly and compare strings
      if (currentUserId != null &&
          widget.connection.requester.id != null &&
          currentUserId == widget.connection.requester.id) {
        // Current user is the requester, so show recipient
        debugPrint(
            '✅ Detail Page - Current user is requester, showing recipient');
        return {
          'name': widget.connection.recipient?.fullName ?? '',
          'phone': widget.connection.recipient?.phoneNumber ?? '',
          'avatar': widget.connection.recipient?.avatar ??
              widget.connection.recipient?.firstName ??
              '',
          'id': widget.connection.recipient?.id ?? '',
          'firstName': widget.connection.recipient?.firstName ?? '',
          'lastName': widget.connection.recipient?.lastName ?? '',
        };
      } else if (currentUserId != null &&
          widget.connection.recipient?.id != null &&
          currentUserId == widget.connection.recipient?.id) {
        // Current user is the recipient, so show requester
        debugPrint(
            '✅ Detail Page - Current user is recipient, showing requester');
        return {
          'name': widget.connection.requester.fullName.trim(),
          'phone': widget.connection.requester.phoneNumber ?? '',
          'avatar': widget.connection.requester.avatar ??
              widget.connection.requester.firstName ??
              '',
          'id': widget.connection.requester.id ?? '',
          'firstName': widget.connection.requester.firstName ?? '',
          'lastName': widget.connection.requester.lastName ?? '',
        };
      } else {
        // Cannot determine current user role, default to showing requester
        debugPrint(
            '⚠️ Detail Page - Cannot determine user role, defaulting to showing requester');
        return {
          'name': widget.connection.requester.fullName.trim(),
          'phone': widget.connection.requester.phoneNumber ?? '',
          'avatar': widget.connection.requester.avatar ??
              widget.connection.requester.firstName ??
              '',
          'id': widget.connection.requester.id ?? '',
          'firstName': widget.connection.requester.firstName ?? '',
          'lastName': widget.connection.requester.lastName ?? '',
        };
      }
    } catch (e) {
      debugPrint('❌ Detail Page - Error in _getConnectedPersonInfo: $e');
      // Fallback to showing requester if there's an error
      return {
        'name': widget.connection.requester.fullName.trim(),
        'phone': widget.connection.requester.phoneNumber ?? '',
        'avatar': widget.connection.requester.avatar ??
            widget.connection.requester.firstName ??
            '',
        'id': widget.connection.requester.id ?? '',
        'firstName': widget.connection.requester.firstName ?? '',
        'lastName': widget.connection.requester.lastName ?? '',
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    final blocValue = sl<HomeBloc>();
    final name = widget.connection.recipient?.fullName ?? '';
    final phoneNumber = widget.connection.recipient?.phoneNumber ?? '';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Connect Details'),
      ),
      body: SafeArea(
        child: BlocProvider.value(
          value: blocValue,
          child: BlocListener<MyConnectBloc, MyConnectState>(
            listener: (context, state) {
              if (state is MyConnectDeletedState) {
                CustomToastification(
                  context,
                  message: 'Connection deleted successfully!',
                  isError: false,
                );
                // Navigate back to the previous screen and indicate data changed
                // This will trigger didPopNext in the parent page
                Navigator.of(context).pop(true);
              } else if (state is MyConnectErrorState) {
                CustomToastification(
                  context,
                  message: state.message,
                  isError: true,
                );
              }
            },
            child: FutureBuilder<Map<String, String>>(
              future: _getConnectedPersonInfo(),
              builder: (context, connectedPersonSnapshot) {
                if (!connectedPersonSnapshot.hasData) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(50.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final connectedPerson = connectedPersonSnapshot.data!;

                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _MyConnectDetailHeader(
                        connection: widget.connection,
                        connectedPersonInfo: connectedPerson,
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      _MyConnectTransactions(
                        connection: widget.connection,
                        connectedPersonInfo: connectedPerson,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _MyConnectDetailHeader extends StatelessWidget {
  const _MyConnectDetailHeader({
    required this.connection,
    required this.connectedPersonInfo,
    super.key,
  });

  final ConnectionRequestEntity connection;
  final Map<String, String> connectedPersonInfo;

  // Helper method to convert connected person data to MemberLookupEntity
  MemberLookupEntity _convertToMemberLookupEntity() {
    return MemberLookupEntity(
      id: connectedPersonInfo['id'] ?? '',
      avatar: connectedPersonInfo['avatar'] ?? '',
      lastName: connectedPersonInfo['lastName'] ?? '',
      firstName: connectedPersonInfo['firstName'] ?? '',
      middleName: '', // Not available
      phoneNumber: connectedPersonInfo['phone'] ?? '',
      emailAddress: '', // Not available
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            color: Color(0xFFF6F6F6),
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        shadows: const [
          BoxShadow(
            color: Color(0x0F000000),
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: MyConnectPersonDetail(
                  name: connectedPersonInfo['name'] ?? '',
                  phoneOrEmail: connectedPersonInfo['phone'] ?? '',
                  avatar: connectedPersonInfo['avatar'] ?? '',
                ),
              ),
              GestureDetector(
                onTap: () {
                  showDeleteDialog(context);
                },
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(color: const Color(0xFFF1F1F1)),
                  ),
                  child: const Icon(Icons.delete, color: Colors.red),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          Row(
            children: [
              Expanded(
                child: MyConnectBtnCard(
                  onTap: () {
                    // Navigate to SendMoneyRequestAddMoneyScreen
                    final memberLookupEntity = _convertToMemberLookupEntity();
                    context.pushNamed(
                      AppRouteName.sendMoneyRequestAddMoneyScreen,
                      extra: memberLookupEntity,
                    );
                  },
                  child: const CustomBuildText(
                    text: 'Request Money',
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: MyConnectBtnCard(
                  onTap: () {
                    // Navigate to WalletTransferAddAmount with connected person data
                    context.pushNamed(
                      AppRouteName.walletTransferAddAmount,
                      pathParameters: {
                        'currency': 'etb', // Default to ETB
                      },
                      extra: {
                        'dashenExchangeAmount': 0.0, // Default exchange amount
                        'isFromQuick': true,
                        'recipent': RecentRecipient(
                          id: connectedPersonInfo['id'] ?? '',
                          email: '', // Email not available
                          phone: connectedPersonInfo['phone'] ?? '',
                          name: connectedPersonInfo['name'] ?? '',
                          date: DateTime.now(),
                        ),
                      },
                    );
                  },
                  hasBorder: false,
                  bgColor: Theme.of(context).primaryColor,
                  child: const CustomBuildText(
                    text: 'Send Money',
                    textAlign: TextAlign.center,
                    caseType: '',
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _menuCards({
    required IconData icons,
    required String title,
    required String description,
    required VoidCallback onTap,
    required Color titleColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Icon(
              icons,
              color: titleColor,
            ),
            const SizedBox(
              width: 6,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBuildText(
                    text: title,
                    fontWeight: FontWeight.w500,
                    color: titleColor,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: CustomBuildText(
                          text: description,
                          // 'Edit the the connects information and Save changes.',
                          color: Colors.black.withOpacity(0.4),
                          softWrap: true,
                          fontSize: 12,
                          caseType: '',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Image.asset(
              MediaRes.navigationArrowRight,
              width: 20,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> showDeleteDialog(BuildContext context) async {
    await showDialog<bool>(
      context: context,
      builder: (_) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomPagePadding(
              bottom: 16,
              decoration: const BoxDecoration(
                color: Color(0xFFF3F3F3),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const CustomBuildText(text: 'Delete Connection'),
                  const Spacer(),
                  InkWell(
                    onTap: () {
                      Navigator.of(context, rootNavigator: true).pop(false);
                    },
                    child: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            CustomPagePadding(
              child: Column(
                children: [
                  const CustomBuildText(
                    text:
                        'Are you sure you want to delete this connect? This action can’t be un done.',
                  ),
                  const SizedBox(
                    height: 36,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: MyConnectBtnCard(
                          onTap: () {
                            Navigator.of(context, rootNavigator: true)
                                .pop(true);
                          },
                          borderColor: Theme.of(context).primaryColor,
                          child: CustomBuildText(
                            text: 'Back',
                            textAlign: TextAlign.center,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        child: MyConnectBtnCard(
                          hasBorder: false,
                          bgColor: Colors.red,
                          onTap: () {
                            // Dispatch delete connection event
                            context.read<MyConnectBloc>().add(
                                  DeleteConnectionRequestEvent(
                                    requestId: connection.id,
                                  ),
                                );
                            Navigator.of(context, rootNavigator: true)
                                .pop(true);
                          },
                          child: const CustomBuildText(
                            text: 'Delete',
                            textAlign: TextAlign.center,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}

class _MyConnectTransactions extends StatelessWidget {
  const _MyConnectTransactions({
    required this.connection,
    required this.connectedPersonInfo,
    super.key,
  });

  final ConnectionRequestEntity connection;
  final Map<String, String> connectedPersonInfo;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            color: Color(0xFFF6F6F6),
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        shadows: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        children: [
          // Connection Information
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CustomBuildText(
                  text: 'Connection Details',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 12),
                _buildInfoRow('Status', connection.status.toUpperCase()),
                const SizedBox(height: 8),
                _buildInfoRow(
                  'Connected Since',
                  _formatDate(connection.sentAt),
                ),
                if (connection.connectedAt != null) ...[
                  const SizedBox(height: 8),
                  // _buildInfoRow(
                  //   'Connected On',
                  //   _formatDate(connection.connectedAt!),
                  // ),
                ],
                const SizedBox(height: 16),
                const CustomBuildText(
                  text: 'Transaction History',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                const SizedBox(height: 12),
                // Transaction history between current user and connected person
                BlocProvider(
                  create: (context) => sl<TransactionBloc>()
                    ..add(const FetchTransactionsEvent(page: 1, perPage: 100, status: 'Completed', currency: 'USD')),
                  child: BlocBuilder<TransactionBloc, TransactionState>(
                    builder: (context, state) {
                      if (state is TransactionLoading) {
                        return Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      if (state is TransactionError) {
                        return Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Column(
                              children: [
                                const Icon(
                                  Icons.error_outline,
                                  size: 48,
                                  color: Colors.red,
                                ),
                                const SizedBox(height: 8),
                                CustomBuildText(
                                  text: 'Error loading transactions',
                                  color: Colors.red,
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 4),
                                CustomBuildText(
                                  text: state.message,
                                  fontSize: 12,
                                  color: Colors.red,
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      if (state is TransactionLoaded) {
                        // Filter transactions between current user and connected person
                        final connectedPersonId =
                            connectedPersonInfo['id'] ?? '';

                        final filteredTransactions =
                            state.allTransactions.where((transaction) {
                          // Show transactions where:
                          // 1. Current user sent money to connected person
                          // 2. Connected person sent money to current user
                          return (transaction.beneficiaryId ==
                                      connectedPersonId ||
                                  transaction.senderId == connectedPersonId) &&
                              transaction.transactionType == 'wallet_transfer';
                        }).toList();

                        if (filteredTransactions.isEmpty) {
                          return Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.receipt_long_outlined,
                                    size: 48,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(height: 8),
                                  CustomBuildText(
                                    text: 'No transactions yet',
                                    color: Colors.grey,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: 4),
                                  CustomBuildText(
                                    text: 'Start sending or requesting money '
                                        'to see transaction history',
                                    fontSize: 12,
                                    color: Colors.grey,
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        return FutureBuilder<String?>(
                          future: sl<AuthLocalDataSource>().getUserId(),
                          builder: (context, userSnapshot) {
                            if (!userSnapshot.hasData) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            }

                            final currentUserId = userSnapshot.data!;

                            return Column(
                              children: [
                                ...filteredTransactions.map((transaction) {
                                  final isSender =
                                      transaction.senderId == currentUserId;
                                  final otherPersonName = isSender
                                      ? transaction.beneficiaryName ?? 'Unknown'
                                      : transaction.senderName;

                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 8),
                                    child: TransactionCard(
                                      transaction: transaction,
                                      title: otherPersonName,
                                      transactionType:
                                          transaction.transactionType,
                                      amount: transaction.paidAmount.toString(),
                                      date: AppMapper.safeFormattedDate(
                                          transaction.createdAt,
                                          hasTimeStamp: false),
                                      currentUserId: currentUserId,
                                      senderId: transaction.senderId,
                                      beneficiaryId:
                                          transaction.beneficiaryId ?? '',
                                      onTap: () {
                                        // Navigate to transaction details
                                        context.pushNamed(
                                          AppRouteName.transactionDetails,
                                          pathParameters: {
                                            'id': transaction.id,
                                          },
                                        );
                                      },
                                    ),
                                  );
                                }),
                              ],
                            );
                          },
                        );
                      }

                      return Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Center(
                          child: Column(
                            children: [
                              Icon(
                                Icons.receipt_long_outlined,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              CustomBuildText(
                                text: 'No transactions yet',
                                color: Colors.grey,
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: 4),
                              CustomBuildText(
                                text: 'Start sending or requesting money '
                                    'to see transaction history',
                                fontSize: 12,
                                color: Colors.grey,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomBuildText(
          text: label,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF757575),
        ),
        CustomBuildText(
          text: value,
          fontWeight: FontWeight.w600,
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
