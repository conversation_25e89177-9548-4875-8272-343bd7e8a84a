import 'dart:math';

import 'package:cbrs/features/chat/domain/entities/chat_contact.dart';
import 'package:cbrs/features/chat/domain/entities/conversation.dart';
import 'package:cbrs/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:cbrs/features/chat/presentation/cubit/conversation_metadata_cubit.dart';
import 'package:cbrs/features/chat/presentation/cubit/conversation_selection_cubit.dart';
import 'package:cbrs/features/chat/presentation/cubit/presence_cubit.dart';
import 'package:cbrs/features/chat/presentation/widgets/message_status_indicator.dart';
import 'package:cbrs/features/chat/presentation/widgets/presence_status_indicator.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Widget for displaying a conversation in the list
class ConversationListItem extends StatelessWidget {
  const ConversationListItem({
    required this.conversation,
    required this.onTap,
    required this.chatBloc,
    super.key,
    this.onLongPress,
    this.contacts = const [],
  });

  final ConversationEntity conversation;
  final VoidCallback onTap;
  final VoidCallback? onLongPress;
  final List<ChatContactEntity>
      contacts; // List of contacts to get additional info
  final ChatBloc chatBloc;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<ConversationSelectionCubit, ConversationSelectionState>(
      builder: (context, selectionState) {
        final isSelected = selectionState.isSelectionMode &&
            selectionState.selectedConversations
                .any((c) => c.id == conversation.id);
        final isSelectionMode = selectionState.isSelectionMode;

        return Container(
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : Colors.white,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withValues(alpha: 0.2),
                width: 0.5,
              ),
            ),
          ),
          child: InkWell(
            onTap: () => _handleTap(context, selectionState),
            onLongPress: () => _handleLongPress(context, selectionState),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // Selection indicator or avatar
                  if (isSelectionMode)
                    _buildSelectionIndicator(theme, isSelected)
                  else
                    _buildAvatar(theme),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                _getDisplayName(),
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: conversation.hasUnreadMessages
                                      ? FontWeight.bold
                                      : FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _buildStatusIndicators(theme),
                                const SizedBox(width: 4),
                                Text(
                                  _getFormattedTime(),
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: conversation.hasUnreadMessages
                                        ? theme.colorScheme.primary
                                        : theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Expanded(
                              child: BlocBuilder<PresenceCubit, PresenceState>(
                                builder: (context, presenceState) => Text(
                                  _getSubtitle(context),
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: conversation.hasUnreadMessages
                                        ? theme.colorScheme.onSurface
                                        : theme.colorScheme.onSurfaceVariant,
                                    fontWeight: conversation.hasUnreadMessages
                                        ? FontWeight.w500
                                        : null,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            if (conversation.hasUnreadMessages) ...[
                              const SizedBox(width: 8),
                              UnreadCountBadge(count: conversation.unreadCount),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleTap(
    BuildContext context,
    ConversationSelectionState selectionState,
  ) {
    if (selectionState.isSelectionMode) {
      // In selection mode, toggle selection
      context
          .read<ConversationSelectionCubit>()
          .toggleConversationSelection(conversation);
    } else {
      // Normal tap behavior
      onTap();
    }
  }

  void _handleLongPress(
    BuildContext context,
    ConversationSelectionState selectionState,
  ) {
    if (!selectionState.isSelectionMode) {
      // Enter selection mode
      context
          .read<ConversationSelectionCubit>()
          .enterSelectionMode(conversation);
    } else {
      // Already in selection mode, call original long press if provided
      onLongPress?.call();
    }
  }

  Widget _buildSelectionIndicator(ThemeData theme, bool isSelected) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isSelected
            ? theme.colorScheme.primary
            : Colors.grey.withValues(alpha: 0.3),
        border: Border.all(
          color: isSelected
              ? theme.colorScheme.primary
              : Colors.grey.withValues(alpha: 0.5),
          width: 2,
        ),
      ),
      child: Icon(
        isSelected ? Icons.check : null,
        color: Colors.white,
        size: 24,
      ),
    );
  }

  Widget _buildAvatar(ThemeData theme) {
    return Stack(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.15),
              width: 1.5,
            ),
          ),
          child: ClipOval(
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
              ),
              child: CustomPaint(
                painter: AvatarPatternPainter(
                  color: theme.colorScheme.primary,
                ),
                child: (_getAvatarUrl()?.isNotEmpty == true)
                    ? Image.network(
                        _getAvatarUrl()!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) =>
                            _buildFallbackIcon(theme),
                      )
                    : _buildFallbackIcon(theme),
              ),
            ),
          ),
        ),
        if (conversation.isDirectMessage)
          Positioned(
            bottom: 0,
            right: 0,
            child: BlocBuilder<PresenceCubit, PresenceState>(
              builder: (context, presenceState) => ChatListPresenceIndicator(
                status: _getContactStatus(context),
                size: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFallbackIcon(ThemeData theme) {
    return Center(
      child: Text(
        _getInitials(),
        style: theme.textTheme.titleMedium?.copyWith(
          color: theme.colorScheme.primary.withValues(alpha: 0.7),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildStatusIndicators(ThemeData theme) {
    return BlocBuilder<ConversationMetadataCubit, ConversationMetadataState>(
      builder: (context, metadataState) {
        final isPinned =
            metadataState.pinnedConversations.contains(conversation.id);
        final isMuted =
            metadataState.mutedConversations.contains(conversation.id);

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isPinned)
              Icon(
                FluentIcons.pin_24_regular,
                size: 14,
                color: theme.colorScheme.primary.withValues(alpha: 0.7),
              ),
            if (isMuted) ...[
              if (isPinned) const SizedBox(width: 4),
              Icon(
                FluentIcons.speaker_mute_24_regular,
                size: 14,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ],
        );
      },
    );
  }

  String _getFormattedTime() {
    // Use last message timestamp if available, otherwise use conversation updatedAt
    final timestamp =
        conversation.lastMessage?.timestamp ?? conversation.updatedAt;
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 7) {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _getInitials() {
    if (conversation.isDirectMessage) {
      final contact = _getContactInfo();
      if (contact != null) {
        return contact.initials;
      }
    }

    final title = conversation.title ?? conversation.id;
    final words =
        title.trim().split(' ').where((word) => word.isNotEmpty).toList();
    if (words.length >= 2 && words[0].isNotEmpty && words[1].isNotEmpty) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty && words[0].isNotEmpty) {
      return words[0][0].toUpperCase();
    }
    return '?';
  }

  /// Get avatar URL for the conversation
  String? _getAvatarUrl() {
    if (conversation.isDirectMessage) {
      final contact = _getContactInfo();
      if (contact != null && contact.avatarUrl != null) {
        return contact.avatarUrl;
      }
    }
    return conversation.avatarUrl;
  }

  ChatContactEntity? _getContactInfo() {
    final contact = chatBloc.getContactForConversation(conversation);

    return contact;
  }

  ContactStatus _getContactStatus(BuildContext context) {
    if (!conversation.isDirectMessage) {
      return ContactStatus.offline;
    }

    // Get the other participant's ID for direct messages
    final currentUserId = chatBloc.currentUserId;
    if (currentUserId == null) {
      return ContactStatus.offline;
    }

    String? otherParticipantId;
    try {
      otherParticipantId = conversation.getOtherParticipantId(currentUserId);
    } catch (e) {
      // If we can't get the other participant, use conversation ID
      otherParticipantId = conversation.id;
    }

    if (otherParticipantId == null || otherParticipantId.isEmpty) {
      return ContactStatus.offline;
    }

    // Get status from PresenceCubit
    final presenceCubit = context.read<PresenceCubit>();
    return presenceCubit.getContactStatusFromCache(otherParticipantId);
  }

  String _getDisplayName() {
    final displayName = chatBloc.getConversationDisplayName(conversation);
    return displayName;
  }

  String _getSubtitle(BuildContext context) {
    // Always prioritize showing the last message content if available
    if (conversation.lastMessagePreview.isNotEmpty &&
        conversation.lastMessagePreview != 'No messages yet') {
      return conversation.lastMessagePreview;
    }

    // For direct messages without messages, show contact status
    if (conversation.isDirectMessage) {
      final status = _getContactStatus(context);
      if (status == ContactStatus.online) {
        return 'Online';
      } else {
        final contact = _getContactInfo();
        if (contact?.lastSeen != null) {
          return contact!.formattedLastSeen;
        }
      }
      return 'Tap to start conversation';
    }

    // For group messages without messages
    return 'No messages yet';
  }
}

class AvatarPatternPainter extends CustomPainter {
  AvatarPatternPainter({required this.color});
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.08)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5
      ..strokeCap = StrokeCap.round;

    // Smaller hexagon parameters for avatar
    const hexRadius = 8.0;
    const horizontalSpacing = hexRadius * 1.73205;
    const verticalSpacing = hexRadius * 1.5;

    // Calculate number of hexagons needed
    final columns = (size.width / horizontalSpacing).ceil() + 1;
    final rows = (size.height / verticalSpacing).ceil() + 1;

    // Draw hexagonal grid
    for (var row = 0; row < rows; row++) {
      for (var col = 0; col < columns; col++) {
        final xOffset =
            col * horizontalSpacing + (row.isOdd ? horizontalSpacing / 2 : 0);
        final yOffset = row * verticalSpacing;

        _drawHexagon(
          canvas,
          paint,
          Offset(xOffset, yOffset),
          hexRadius,
        );
      }
    }

    // Add subtle accent dots
    final dotPaint = Paint()
      ..color = color.withValues(alpha: 0.12)
      ..style = PaintingStyle.fill;

    for (var row = 0; row < rows; row += 2) {
      for (var col = 0; col < columns; col += 2) {
        final xOffset =
            col * horizontalSpacing + (row.isOdd ? horizontalSpacing / 2 : 0);
        final yOffset = row * verticalSpacing;

        canvas.drawCircle(
          Offset(xOffset, yOffset),
          0.8,
          dotPaint,
        );
      }
    }
  }

  void _drawHexagon(Canvas canvas, Paint paint, Offset center, double radius) {
    final path = Path();
    final angles = List.generate(6, (index) => index * 60 * pi / 180);

    path.moveTo(
      center.dx + radius * cos(angles[0]),
      center.dy + radius * sin(angles[0]),
    );

    for (var i = 1; i < 6; i++) {
      path.lineTo(
        center.dx + radius * cos(angles[i]),
        center.dy + radius * sin(angles[i]),
      );
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
