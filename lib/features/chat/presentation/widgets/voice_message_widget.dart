import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:cbrs/core/extensions/context_extension.dart';
import 'package:cbrs/features/chat/domain/entities/chat_message.dart';
import 'package:flutter/material.dart';

/// Widget for displaying and playing voice messages
class VoiceMessageWidget extends StatefulWidget {
  const VoiceMessageWidget({
    required this.message,
    required this.isFromMe,
    super.key,
  });

  final ChatMessageEntity message;
  final bool isFromMe;

  @override
  State<VoiceMessageWidget> createState() => _VoiceMessageWidgetState();
}

class _VoiceMessageWidgetState extends State<VoiceMessageWidget>
    with TickerProviderStateMixin {
  late AudioPlayer _audioPlayer;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  Duration?
      _originalDuration; // Store original duration to persist after playback
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<PlayerState>? _playerStateSubscription;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _setupAnimations();
  }

  void _initializePlayer() {
    _audioPlayer = AudioPlayer();

    _durationSubscription = _audioPlayer.onDurationChanged.listen((duration) {
      if (mounted) {
        setState(() {
          _duration = duration;
          // Store original duration to persist after playback completes
          if (_originalDuration == null && duration > Duration.zero) {
            _originalDuration = duration;
          }
        });
      }
    });

    _positionSubscription = _audioPlayer.onPositionChanged.listen((position) {
      if (mounted) {
        setState(() {
          _position = position;
        });
      }
    });

    _playerStateSubscription =
        _audioPlayer.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
          _isLoading =
              state == PlayerState.playing && _position == Duration.zero;

          // When playback completes, reset position to show full duration
          if (state == PlayerState.completed) {
            _position = Duration.zero;
            // Ensure we show the original duration even if player resets it
            if (_originalDuration != null) {
              _duration = _originalDuration!;
            }
          }
        });

        if (state == PlayerState.playing) {
          _waveController.repeat();
        } else {
          _waveController.stop();
        }
      }
    });
  }

  void _setupAnimations() {
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _waveAnimation = Tween<double>(
      begin: 0.5,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _waveController,
        curve: Curves.easeInOut,
      ),
    );
  }

  Future<void> _togglePlayback() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        final audioUrl = widget.message.attachmentUrl;
        if (audioUrl != null) {
          if (audioUrl.startsWith('http')) {
            // Remote URL
            await _audioPlayer.play(UrlSource(audioUrl));
          } else {
            // Local file
            await _audioPlayer.play(DeviceFileSource(audioUrl));
          }
        }
      }
    } catch (e) {
      debugPrint('Error playing voice message: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to play voice message'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get the duration to display - prefer stored duration from message
  Duration _getDisplayDuration() {
    // First, try to use duration from the message entity (stored from Agora)
    if (widget.message.duration != null && widget.message.duration! > 0) {
      return Duration(seconds: widget.message.duration!);
    }
    // If we have the original duration from player, use that
    if (_originalDuration != null && _originalDuration! > Duration.zero) {
      return _originalDuration!;
    }
    // Otherwise fall back to current duration
    return _duration;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      constraints: const BoxConstraints(
        minWidth: 200,
        maxWidth: 280,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Play/Pause button
          GestureDetector(
            onTap: _togglePlayback,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: widget.isFromMe
                    ? Colors.white.withOpacity(0.2)
                    : context.theme.primaryColor,
                shape: BoxShape.circle,
              ),
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.isFromMe ? Colors.white : Colors.white,
                        ),
                      ),
                    )
                  : Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: widget.isFromMe ? Colors.white : Colors.white,
                      size: 20,
                    ),
            ),
          ),

          const SizedBox(width: 12),

          // Waveform and duration
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Waveform visualization
                SizedBox(
                  height: 30,
                  child: Row(
                    children: List.generate(20, (index) {
                      return AnimatedBuilder(
                        animation: _waveAnimation,
                        builder: (context, child) {
                          final height = _isPlaying
                              ? (10 +
                                  (20 *
                                      _waveAnimation.value *
                                      (0.5 + 0.5 * (index % 3 + 1) / 3)))
                              : (8 + (index % 4) * 3).toDouble();

                          return Container(
                            width: 3,
                            height: height,
                            margin: const EdgeInsets.symmetric(horizontal: 1),
                            decoration: BoxDecoration(
                              color: widget.isFromMe
                                  ? Colors.white.withOpacity(0.7)
                                  : Colors.grey[600],
                              borderRadius: BorderRadius.circular(1.5),
                            ),
                          );
                        },
                      );
                    }),
                  ),
                ),

                const SizedBox(height: 4),

                // Duration and progress
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(_position),
                      style: TextStyle(
                        color:
                            widget.isFromMe ? Colors.white70 : Colors.grey[600],
                        fontSize: 11,
                      ),
                    ),
                    Text(
                      _formatDuration(_getDisplayDuration()),
                      style: TextStyle(
                        color:
                            widget.isFromMe ? Colors.white70 : Colors.grey[600],
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _waveController.dispose();
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _playerStateSubscription?.cancel();
    super.dispose();
  }
}
