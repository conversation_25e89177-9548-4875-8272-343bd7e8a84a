import 'package:cbrs/features/chat/domain/entities/chat_message.dart';
import 'package:flutter/material.dart';

/// Widget to display message status indicators (checkmarks)
class MessageStatusIndicator extends StatelessWidget {
  const MessageStatusIndicator({
    required this.status,
    required this.isFromMe,
    super.key,
    this.size = 16.0,
    this.color,
  });

  final ChatMessageStatus status;
  final bool isFromMe;
  final double size;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    // Only show status indicators for sent messages
    if (!isFromMe) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final indicatorColor =
        color ?? theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7);

    switch (status) {
      case ChatMessageStatus.sending:
        return SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 1.5,
            valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
          ),
        );

      case ChatMessageStatus.sent:
        return Icon(
          Icons.check,
          size: size,
          color: indicatorColor,
        );

      case ChatMessageStatus.delivered:
        // Use done_all icon which is the standard double checkmark
        return Icon(
          Icons.done_all,
          size: size,
          color: indicatorColor,
        );

      case ChatMessageStatus.read:
        // Use done_all icon with primary color for read status (always primary, ignore passed color)
        return Icon(
          Icons.done_all,
          size: size,
          color: Colors.amber,
        );

      case ChatMessageStatus.failed:
        return Icon(
          Icons.error_outline,
          size: size,
          color: theme.colorScheme.error,
        );
    }
  }
}

/// Widget to display message timestamp with status
class MessageTimeAndStatus extends StatelessWidget {
  const MessageTimeAndStatus({
    required this.timestamp,
    required this.status,
    required this.isFromMe,
    super.key,
    this.textStyle,
  });

  final DateTime timestamp;
  final ChatMessageStatus status;
  final bool isFromMe;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultTextStyle = theme.textTheme.bodySmall?.copyWith(
      color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
      fontSize: 11,
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _formatTime(timestamp),
          style: textStyle ?? defaultTextStyle,
        ),
        if (isFromMe) ...[
          const SizedBox(width: 4),
          MessageStatusIndicator(
            status: status,
            isFromMe: isFromMe,
            size: 12,
          ),
        ],
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${dateTime.day}/${dateTime.month}';
    } else {
      final hour = dateTime.hour.toString().padLeft(2, '0');
      final minute = dateTime.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }
  }
}

/// Widget to display unread message count badge
class UnreadCountBadge extends StatelessWidget {
  const UnreadCountBadge({
    required this.count,
    super.key,
    this.backgroundColor,
    this.textColor,
    this.size = 20.0,
  });

  final int count;
  final Color? backgroundColor;
  final Color? textColor;
  final double size;

  @override
  Widget build(BuildContext context) {
    if (count <= 0) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.colorScheme.primary;
    final txtColor = textColor ?? theme.colorScheme.onPrimary;

    return Container(
      constraints: BoxConstraints(
        minWidth: size,
        minHeight: size,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Center(
        child: Text(
          count > 99 ? '99+' : count.toString(),
          style: theme.textTheme.labelSmall?.copyWith(
            color: txtColor,
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

/// Widget to display online/offline status indicator
class OnlineStatusIndicator extends StatelessWidget {
  const OnlineStatusIndicator({
    required this.isOnline,
    super.key,
    this.size = 12.0,
    this.showBorder = true,
  });

  final bool isOnline;
  final double size;
  final bool showBorder;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: isOnline ? Colors.green : Colors.grey,
        shape: BoxShape.circle,
        border: showBorder
            ? Border.all(
                color: Colors.white,
                width: 2,
              )
            : null,
      ),
    );
  }
}
