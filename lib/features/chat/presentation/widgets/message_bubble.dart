import 'package:cbrs/core/extensions/context_extension.dart';
import 'package:cbrs/features/chat/domain/entities/chat_message.dart';
import 'package:cbrs/features/chat/presentation/widgets/message_status_indicator.dart';
import 'package:flutter/material.dart';

class MessageBubble extends StatelessWidget {
  const MessageBubble({
    required this.message,
    required this.currentUserId,
    super.key,
    this.showAvatar = false,
    this.onTap,
    this.onLongPress,
    this.isSelectionMode = false,
    this.isSelected = false,
    this.onSelectionToggle,
  });

  final ChatMessageEntity message;
  final String currentUserId;
  final bool showAvatar;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelectionMode;
  final bool isSelected;
  final VoidCallback? onSelectionToggle;

  @override
  Widget build(BuildContext context) {
    final isFromMe = message.isFromMe(currentUserId);

    return GestureDetector(
      onTap: isSelectionMode ? onSelectionToggle : onTap,
      onLongPress: onLongPress,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.symmetric(vertical: 1, horizontal: 16),
        decoration: isSelected
            ? BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.03),
                borderRadius: BorderRadius.circular(0),
              )
            : null,
        child: Row(
          mainAxisAlignment:
              isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (isSelectionMode && !isFromMe) _buildSelectionCheckbox(context),
            Flexible(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                  minWidth: 60,
                ),
                margin: EdgeInsets.only(
                  left: isFromMe ? 60 : 0,
                  right: isFromMe ? 0 : 60,
                ),
                child: Column(
                  crossAxisAlignment: isFromMe
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: isFromMe
                            ? const Color(0xFF0D451B)
                            : const Color(0xFFFFFFFF),
                        borderRadius: _getMessengerBorderRadius(isFromMe),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.04),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildMessageContent(context, isFromMe),
                          const SizedBox(height: 2),
                          _buildMessageMetadata(context, isFromMe),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (isSelectionMode && isFromMe) _buildSelectionCheckbox(context),
          ],
        ),
      ),
    );
  }

  BorderRadius _getMessengerBorderRadius(bool isFromMe) {
    // Messenger-style rounded bubbles - more uniform and modern
    if (isFromMe) {
      return const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(6), // Subtle tail effect
      );
    } else {
      // Received messages: slightly less rounded on bottom left
      return const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(6), // Subtle tail effect
        bottomRight: Radius.circular(20),
      );
    }
  }

  Widget _buildMessageContent(BuildContext context, bool isFromMe) {
    return Text(
      message.content,
      style: context.textTheme.bodyMedium?.copyWith(
        color: isFromMe ? Colors.white : const Color(0xFF1C1E21),
        fontSize: 15,
        height: 1.4,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  Widget _buildMessageMetadata(BuildContext context, bool isFromMe) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment:
          isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: [
        // Timestamp
        Text(
          message.formattedTime,
          style: context.textTheme.bodySmall?.copyWith(
            color: isFromMe
                ? Colors.white.withValues(alpha: 0.9)
                : Colors.grey[500],
            fontSize: 10,
            fontWeight: FontWeight.w400,
          ),
        ),

        // Message status for sent messages
        if (isFromMe) ...[
          const SizedBox(width: 3),
          MessageStatusIndicator(
            status: message.status,
            isFromMe: isFromMe,
            size: 12,
            color: Colors.white.withValues(alpha: 0.9),
          ),
        ],
      ],
    );
  }

  Widget _buildSelectionCheckbox(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.grey.shade400,
            width: 2,
          ),
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
        ),
        child: isSelected
            ? Icon(
                Icons.check,
                size: 16,
                color: Theme.of(context).colorScheme.onPrimary,
              )
            : null,
      ),
    );
  }
}
