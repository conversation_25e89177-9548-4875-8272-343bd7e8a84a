import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';

import 'package:cbrs/core/common/widgets/custom_app_bar.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/presence_service.dart';
import 'package:cbrs/features/chat/domain/entities/chat_contact.dart';
import 'package:cbrs/features/chat/presentation/widgets/presence_status_indicator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Custom app bar for conversations page
class ConversationsAppBar extends StatefulWidget
    implements PreferredSizeWidget {
  const ConversationsAppBar({
    required this.onSearchPressed,
    required this.onNewConversationPressed,
    super.key,
  });

  final VoidCallback onSearchPressed;
  final VoidCallback onNewConversationPressed;

  @override
  State<ConversationsAppBar> createState() => _ConversationsAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _ConversationsAppBarState extends State<ConversationsAppBar> {
  late PresenceService _presenceService;
  ContactStatus _currentStatus = ContactStatus.offline;

  @override
  void initState() {
    super.initState();
    _presenceService = sl<PresenceService>();
    _currentStatus = _presenceService.currentStatus;

    // Listen to status changes
    _presenceService.statusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      context: context,
      titleWidget: Row(
        children: [
          Text(
            'Messages',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 20.sp,
                ),
          ),
          const SizedBox(width: 12),
          _buildStatusIndicator(),
        ],
      ),
      automaticallyImplyLeading: false,
      actions: [
        IconButton(
          icon: const Icon(FluentIcons.search_24_regular),
          onPressed: widget.onSearchPressed,
          tooltip: 'Search conversations',
        ),
        IconButton(
          icon: const Icon(FluentIcons.add_24_regular),
          onPressed: widget.onNewConversationPressed,
          tooltip: 'New conversation',
        ),
      ],
    );
  }

  Widget _buildStatusIndicator() {
    return GestureDetector(
      onTap: _showStatusSelector,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            PresenceStatusIndicator(
              status: _currentStatus,
              size: 12,
            ),
            const SizedBox(width: 6),
            Text(
              _getStatusText(_currentStatus),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  void _showStatusSelector() {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 40,
                height: 2,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 12),

            Text(
              'Set your status',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w400,
                    fontSize: 18.sp,
                  ),
            ),
            const SizedBox(height: 16),

            // Status options
            ...ContactStatus.values.map(_buildStatusOption),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusOption(ContactStatus status) {
    final isSelected = status == _currentStatus;

    return InkWell(
      onTap: () async {
        Navigator.pop(context);
        await _changeStatus(status);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : null,
        ),
        child: Row(
          children: [
            PresenceStatusIndicator(
              status: status,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _getStatusText(status),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : null,
                    ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _changeStatus(ContactStatus status) async {
    switch (status) {
      case ContactStatus.online:
        await _presenceService.setOnline();
      case ContactStatus.away:
        await _presenceService.setAway();
      case ContactStatus.busy:
        await _presenceService.setBusy();
      case ContactStatus.offline:
        await _presenceService.setOffline();
    }
  }

  String _getStatusText(ContactStatus status) {
    switch (status) {
      case ContactStatus.online:
        return 'Online';
      case ContactStatus.away:
        return 'Away';
      case ContactStatus.busy:
        return 'Busy';
      case ContactStatus.offline:
        return 'Offline';
    }
  }
}
