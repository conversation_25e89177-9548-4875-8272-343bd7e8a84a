import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/extensions/context_extension.dart';
import 'package:cbrs/features/chat/domain/entities/chat_message.dart';
import 'package:cbrs/features/chat/domain/entities/download_progress.dart';
import 'package:cbrs/features/chat/presentation/widgets/download_progress_indicator.dart';
import 'package:cbrs/features/chat/presentation/widgets/forwarded_message_indicator.dart';
import 'package:cbrs/features/chat/presentation/widgets/message_status_indicator.dart';
import 'package:cbrs/features/chat/presentation/widgets/voice_message_widget.dart';
import 'package:cbrs/features/chat/presentation/widgets/video_message_widget.dart';
import 'package:flutter/material.dart';

/// WhatsApp-style multimedia message bubble widget
class MultimediaMessageBubble extends StatelessWidget {
  const MultimediaMessageBubble({
    required this.message,
    required this.currentUserId,
    super.key,
    this.showAvatar = false,
    this.downloadProgress,
    this.onTap,
    this.onLongPress,
    this.onImageTap,
    this.onFileTap,
    this.onLocationTap,
    this.onDownload,
    this.onCancelDownload,
    this.onRetryDownload,
    this.onPauseDownload,
    this.onResumeDownload,
  });

  final ChatMessageEntity message;
  final String currentUserId;
  final bool showAvatar;
  final DownloadProgressEntity? downloadProgress;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onImageTap;
  final VoidCallback? onFileTap;
  final VoidCallback? onLocationTap;
  final VoidCallback? onDownload;
  final VoidCallback? onCancelDownload;
  final VoidCallback? onRetryDownload;
  final VoidCallback? onPauseDownload;
  final VoidCallback? onResumeDownload;

  // Colors matching message_bubble.dart
  static const Color appGreen = Color(0xFF0D451B); // USD theme color
  static const Color lightGreen = Color(0xFFDCF8C6);
  static const Color lightGray = Color(0xFFFFFFFF);

  @override
  Widget build(BuildContext context) {
    final isFromMe = message.isFromMe(currentUserId);

    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 1, horizontal: 16),
        child: Row(
          mainAxisAlignment:
              isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Flexible(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                  minWidth: 60,
                ),
                margin: EdgeInsets.only(
                  left: isFromMe ? 60 : 0,
                  right: isFromMe ? 0 : 60,
                ),
                child: Column(
                  crossAxisAlignment: isFromMe
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: isFromMe
                            ? appGreen // Same green as message_bubble.dart
                            : lightGray, // Light gray for received
                        borderRadius: _getWhatsAppBorderRadius(isFromMe),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.03),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Forwarded message indicator
                          if (message.isForwarded)
                            Padding(
                              padding: const EdgeInsets.only(
                                left: 16,
                                right: 16,
                                top: 12,
                              ),
                              child:
                                  ForwardedMessageIndicator(message: message),
                            ),

                          // Message content with forwarded styling
                          ForwardedMessageContent(
                            message: message,
                            child: _buildMessageContent(context, isFromMe),
                          ),

                          _buildMessageInfo(context, isFromMe),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  BorderRadius _getWhatsAppBorderRadius(bool isFromMe) {
    // WhatsApp-style rounded bubbles
    if (isFromMe) {
      // Sent messages: rounded with tail on right
      return const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(20),
        bottomRight: Radius.circular(6), // Subtle tail effect
      );
    } else {
      // Received messages: rounded with tail on left
      return const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
        bottomLeft: Radius.circular(6), // Subtle tail effect
        bottomRight: Radius.circular(20),
      );
    }
  }

  Widget _buildMessageContent(BuildContext context, bool isFromMe) {
    switch (message.messageType) {
      case ChatMessageType.text:
        return _buildTextContent(context, isFromMe);
      case ChatMessageType.image:
        return _buildImageContent(context, isFromMe);
      case ChatMessageType.file:
        return _buildFileContent(context, isFromMe);
      case ChatMessageType.audio:
        return _buildVoiceContent(context, isFromMe);
      case ChatMessageType.video:
        return _buildVideoContent(context, isFromMe);
      case ChatMessageType.location:
        return _buildLocationContent(context, isFromMe);
      default:
        return _buildTextContent(context, isFromMe);
    }
  }

  Widget _buildTextContent(BuildContext context, bool isFromMe) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Text(
        message.content,
        style: context.textTheme.bodyMedium?.copyWith(
          color: isFromMe ? Colors.white : const Color(0xFF1C1E21),
          fontSize: 15,
          height: 1.4,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildImageContent(BuildContext context, bool isFromMe) {
    // Get available width for responsive sizing
    final availableWidth = MediaQuery.of(context).size.width * 0.65;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            GestureDetector(
              onTap: onImageTap,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: message.content.isEmpty
                      ? (isFromMe
                          ? const Radius.circular(20)
                          : const Radius.circular(6))
                      : const Radius.circular(8),
                  bottomRight: message.content.isEmpty
                      ? (isFromMe
                          ? const Radius.circular(6)
                          : const Radius.circular(20))
                      : const Radius.circular(8),
                ),
                child: _buildImageWidget(availableWidth),
              ),
            ),

            // Download overlay for images (show during download and briefly after
            // completion)
            if (downloadProgress != null &&
                (downloadProgress!.isDownloading ||
                    downloadProgress!.status == DownloadStatus.queued ||
                    downloadProgress!.isFailed ||
                    downloadProgress!.isPaused))
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(20),
                      topRight: const Radius.circular(20),
                      bottomLeft: message.content.isEmpty
                          ? (isFromMe
                              ? const Radius.circular(20)
                              : const Radius.circular(6))
                          : const Radius.circular(8),
                      bottomRight: message.content.isEmpty
                          ? (isFromMe
                              ? const Radius.circular(6)
                              : const Radius.circular(20))
                          : const Radius.circular(8),
                    ),
                  ),
                  child: Center(
                    child: DownloadOverlay(
                      progress: downloadProgress!,
                      onCancel: onCancelDownload,
                      onRetry: onRetryDownload,
                    ),
                  ),
                ),
              ),

            // Download button for images (when not downloading and not available
            // locally)
            if (downloadProgress == null && !_isImageAvailableLocally())
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: onDownload,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.file_download_outlined,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
          ],
        ),
        if (message.content.isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              message.content,
              style: context.textTheme.bodyMedium?.copyWith(
                color: isFromMe ? Colors.white : const Color(0xFF1C1E21),
                fontSize: 15,
                height: 1.4,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageWidget(double maxWidth) {
    // Calculate responsive dimensions
    final imageWidth = maxWidth;
    final imageHeight = maxWidth * 0.75; // 4:3 aspect ratio by default

    debugPrint('Building image widget for message: ${message.id}');
    debugPrint('localFilePath: ${message.localFilePath}');
    debugPrint('attachmentUrl: ${message.attachmentUrl}');

    if (message.localFilePath != null && message.localFilePath!.isNotEmpty) {
      final localFile = File(message.localFilePath!);
      if (localFile.existsSync()) {
        debugPrint('Using local downloaded file: ${message.localFilePath}');
        return Image.file(
          localFile,
          width: imageWidth,
          height: imageHeight,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) =>
              _buildErrorWidget(imageWidth, imageHeight),
        );
      } else {
        debugPrint('Local file does not exist: ${message.localFilePath}');
      }
    }

    // Check if attachmentUrl is a local file path
    if (message.attachmentUrl != null &&
        !message.attachmentUrl!.startsWith('http') &&
        message.attachmentUrl!.isNotEmpty) {
      final localFile = File(message.attachmentUrl!);
      if (localFile.existsSync()) {
        return Image.file(
          localFile,
          width: imageWidth,
          height: imageHeight,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) =>
              _buildErrorWidget(imageWidth, imageHeight),
        );
      }
    }

    // Check if it's a network URL
    if (message.attachmentUrl?.startsWith('http') == true) {
      debugPrint('Using network URL: ${message.attachmentUrl}');
      return CachedNetworkImage(
        imageUrl: message.attachmentUrl!,
        width: imageWidth,
        height: imageHeight,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          width: imageWidth,
          height: imageHeight,
          color: Colors.white,
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(appGreen),
            ),
          ),
        ),
        errorWidget: (context, url, error) =>
            _buildErrorWidget(imageWidth, imageHeight),
      );
    }

    // No valid image source found
    return _buildNoImageWidget(imageWidth, imageHeight);
  }

  Widget _buildErrorWidget(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 32, color: appGreen),
          SizedBox(height: 8),
          Text(
            'Image not available',
            style: TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoImageWidget(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.white,
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.image, size: 32, color: appGreen),
          SizedBox(height: 8),
          Text(
            'No image available',
            style: TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Check if image is available locally (either downloaded or local file)
  bool _isImageAvailableLocally() {
    debugPrint(
      'Checking if image is available locally for message: ${message.id}',
    );
    debugPrint('Message localFilePath: ${message.localFilePath}');
    debugPrint('Message attachmentUrl: ${message.attachmentUrl}');
    debugPrint('Message isDownloaded: ${message.isDownloaded}');

    // Check if we have a downloaded local file
    if (message.localFilePath != null && message.localFilePath!.isNotEmpty) {
      final localFile = File(message.localFilePath!);
      final exists = localFile.existsSync();
      debugPrint(
        'Local file exists: $exists at path: ${message.localFilePath}',
      );
      if (exists) {
        return true;
      }
    }

    // Check if attachmentUrl is a local file path
    if (message.attachmentUrl != null &&
        !message.attachmentUrl!.startsWith('http') &&
        message.attachmentUrl!.isNotEmpty) {
      final localFile = File(message.attachmentUrl!);
      final exists = localFile.existsSync();
      debugPrint(
        'Attachment URL file exists: $exists at path: ${message.attachmentUrl}',
      );
      if (exists) {
        return true;
      }
    }

    debugPrint('Image not available locally');
    return false;
  }

  Widget _buildFileContent(BuildContext context, bool isFromMe) {
    return GestureDetector(
      onTap: onFileTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: isFromMe
                    ? Colors.white.withValues(alpha: 0.3)
                    : Colors.white.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                _getFileIcon(),
                color: appGreen,
                size: 28,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getFileName(),
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: isFromMe ? Colors.white : const Color(0xFF1C1E21),
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      if (message.formattedFileSize.isNotEmpty)
                        Text(
                          message.formattedFileSize,
                          style: context.textTheme.bodySmall?.copyWith(
                            color: isFromMe
                                ? Colors.white.withValues(alpha: 0.8)
                                : const Color(0xFF707070),
                            fontSize: 12,
                          ),
                        ),
                      if (downloadProgress != null &&
                          downloadProgress!.isDownloading) ...[
                        if (message.formattedFileSize.isNotEmpty)
                          Text(
                            ' • ',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: isFromMe
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : const Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        Text(
                          '${downloadProgress!.progressPercentage}%',
                          style: context.textTheme.bodySmall?.copyWith(
                            color: isFromMe
                                ? Colors.white.withValues(alpha: 0.8)
                                : const Color(0xFF707070),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            _buildDownloadButton(isFromMe),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationContent(BuildContext context, bool isFromMe) {
    return GestureDetector(
      onTap: onLocationTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 140,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Icon(
                  Icons.location_on,
                  size: 48,
                  color: appGreen,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              message.content.isNotEmpty ? message.content : 'Location',
              style: context.textTheme.bodyMedium?.copyWith(
                color: isFromMe ? Colors.white : const Color(0xFF1C1E21),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Tap to view in maps',
              style: context.textTheme.bodySmall?.copyWith(
                color: isFromMe
                    ? Colors.white.withValues(alpha: 0.8)
                    : const Color(0xFF707070),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceContent(BuildContext context, bool isFromMe) {
    return VoiceMessageWidget(
      message: message,
      isFromMe: isFromMe,
    );
  }

  Widget _buildVideoContent(BuildContext context, bool isFromMe) {
    return VideoMessageWidget(
      message: message,
      isFromMe: isFromMe,
      onTap: onImageTap, // Reuse image tap handler for video tap
    );
  }

  Widget _buildMessageInfo(BuildContext context, bool isFromMe) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 6, top: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            message.formattedTime,
            style: context.textTheme.bodySmall?.copyWith(
              color: isFromMe
                  ? Colors.white.withValues(alpha: 0.9)
                  : Colors.grey[500],
              fontSize: 10,
              fontWeight: FontWeight.w400,
            ),
          ),
          if (isFromMe) ...[
            const SizedBox(width: 3),
            MessageStatusIndicator(
              status: message.status,
              isFromMe: isFromMe,
              size: 14,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getFileIcon() {
    final extension = _getFileExtension().toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'zip':
      case 'rar':
        return Icons.archive;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _getFileName() {
    if (message.attachmentUrl != null) {
      return message.attachmentUrl!.split('/').last;
    }
    return 'Unknown file';
  }

  String _getFileExtension() {
    final fileName = _getFileName();
    final lastDot = fileName.lastIndexOf('.');
    if (lastDot != -1) {
      return fileName.substring(lastDot + 1);
    }
    return '';
  }

  /// Build download button based on current state
  Widget _buildDownloadButton(bool isFromMe) {
    // If file is already downloaded locally, show open icon
    if (message.isDownloaded) {
      return GestureDetector(
        onTap: onFileTap,
        child: Icon(
          Icons.open_in_new,
          color: isFromMe ? Colors.white.withValues(alpha: 0.9) : appGreen,
          size: 22,
        ),
      );
    }

    // If download is in progress, show progress indicator
    if (downloadProgress != null) {
      return DownloadProgressIndicator(
        progress: downloadProgress!,
        size: 24,
        strokeWidth: 2,
        onCancel: onCancelDownload,
        onRetry: onRetryDownload,
        onPause: downloadProgress!.isDownloading ? onPauseDownload : null,
        onResume: downloadProgress!.isPaused ? onResumeDownload : null,
      );
    }

    // Default download button
    return GestureDetector(
      onTap: onDownload,
      child: Icon(
        Icons.file_download_outlined,
        color: isFromMe ? Colors.white.withValues(alpha: 0.9) : appGreen,
        size: 22,
      ),
    );
  }
}
