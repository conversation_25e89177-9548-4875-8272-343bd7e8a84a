import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/features/balance_check/presentation/widgets/balance_transaction_shimmer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';

class LinkedCardShimmer extends StatelessWidget {
  const LinkedCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        children: [
          SizedBox(
            height: 250,
            child: _buildCard(),
          ),
          SizedBox(height: 16.h),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            itemBuilder: (context, index) {
              return const BalanceTransactionShimmer();
            },
            separatorBuilder: (context, index) => Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              height: 1,
              child: CustomPaint(
                painter: Dot<PERSON><PERSON>inePainter(
                  color: Colors.grey.withOpacity(0.2),
                ),
                size: const Size(double.infinity, 1),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCard() {
    return Container(
      // width: 370.w,
      height: 264.h,

      padding: EdgeInsets.only(top: 18.w),
      // margin: EdgeInsets.symmetric(horizontal: 8.w),
      decoration: BoxDecoration(
        image: const DecorationImage(
          image: AssetImage('assets/images/Bank Card.png'),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 3.96,
            offset: const Offset(0, 3.96),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Shimmer.fromColors(
              baseColor: Colors.white.withOpacity(0.3),
              highlightColor: Colors.white.withOpacity(0.5),
              child: Container(
                width: 63.w,
                height: 63.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          SizedBox(height: 10.h),
          Center(
            child: Text(
              'Account Balance',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: 10.h),
          Center(
            child: Shimmer.fromColors(
              baseColor: Colors.white.withOpacity(0.3),
              highlightColor: Colors.white.withOpacity(0.5),
              child: Container(
                width: 132.w,
                height: 28.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          SizedBox(height: 0.h),
          const Spacer(),
          Container(
            
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
            width: double.infinity,
            decoration: ShapeDecoration(
              color: Colors.white.withOpacity(0.16),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(15.84),
                  bottomRight: Radius.circular(15.84),
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // TODO - assets should be defined in res/media_values.dart -- i will fix later
                _buildCardIcons(
                  iconLocation: 'assets/vectors/bank_icon.png',
                  label: 'Account Number:',
                  value: 'accountNumber',
                ),
                _buildCardIcons(
                  iconLocation: 'assets/images/calander1.png',
                  label: 'Linked on:',
                  value: 'linkedDate',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardIcons({
    required String iconLocation,
    required String label,
    required String value,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // leading icon
          IntrinsicHeight(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 7.w, vertical: 7.h),
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                color: Colors.white.withOpacity(0.12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Image.asset(
                iconLocation,
                width: 24.w,
                height: 24.h,
                color: const Color(0xFFFFFFFF),
              ),
            ),
          ),
          SizedBox(width: 8.w),

          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                // 'Account Number:',
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w300,
                ),
              ),
              // SizedBox(height: 2.h),
              Center(
                child: Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: 72.w,
                    height: 28.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
