import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_transactions.dart';
import 'package:cbrs/features/balance_check/presentation/widgets/balance_transaction_shimmer.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:shimmer/shimmer.dart';

// import 'package:cbrs/features/home/<USER>/widgets/transactions/dotted_line_painter.dart';

class BalanceHistoryWidget extends StatelessWidget {
  const BalanceHistoryWidget({
    required this.transactions,
    required this.hasNextPage,
    super.key,
  });

  final List<Transaction> transactions;
  final bool hasNextPage;
  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.h),
          Text(
            'Recent Add Moneys',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black.withOpacity(0.5),
            ),
          ),
          SizedBox(height: 16.h),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: transactions.length > 3 ? 3 : transactions.length,
              itemBuilder: (context, index) {
                final data = transactions[index];
                return _buildTransactionItem(
                  context,
                  transaction: data,
                );
              },
              separatorBuilder: (context, index) => Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                height: 1,
                child: CustomPaint(
                  painter: DottedLinePainter(
                    color: Colors.grey.withOpacity(0.2),
                  ),
                  size: const Size(double.infinity, 1),
                ),
              ),
            ),
          ),
          SizedBox(height: 32.h),
          if (transactions.length > 3) ...[
            Text(
              'All Add Money Transactions ',
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black.withOpacity(0.5),
              ),
            ),
            SizedBox(height: 16.h),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: transactions.sublist(4).length,
                itemBuilder: (context, index) {
                  final data = transactions[index + 4];

                  return hasNextPage &&
                          index == (transactions.sublist(2).length - 1)
                      ? const BalanceTransactionShimmer()
                      : _buildTransactionItem(
                          context,
                          transaction: data,
                        );
                },
                separatorBuilder: (context, index) => Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  height: 1,
                  child: CustomPaint(
                    painter: DottedLinePainter(
                      color: Colors.grey.withOpacity(0.2),
                    ),
                    size: const Size(double.infinity, 1),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTransactionItem(
    BuildContext context, {
    required Transaction transaction,
  }) {
    return InkWell(
      onTap: () => context.pushNamed(
        AppRouteName.transactionDetail,
        extra: {
          'transaction': transaction,
        },
      ),
      child:


      Padding(
        padding: EdgeInsets.only(top: 16.h, bottom: 12.h),
        child: Row(
          children: [
            CachedNetworkImage(
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 44.w,
                  height: 44.h,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
                      imageUrl: transaction.bankLogo?.trim() ?? '',
              width: 44.w,
              height: 44.h,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                        transaction.bankName ?? '',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  Text(
                    transaction.beneficiaryAccountNo ?? '',
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: const Color(0xFFAAAAAA),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '- ${transaction.totalAmount} ETB',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Text(
                  AppMapper.safeFormattedDate(transaction.createdAt),
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    color: const Color(0xFFAAAAAA),
                  ),
                ),
              ],
            ),
            SizedBox(width: 8.w),
            Image.asset(
              MediaRes.forwardIcon,
              width: 20.w,
              color: Colors.black.withOpacity(0.4),
            ),
          ],
        ),
      ),
   
   
   /*   
      
       Row(
        children: [
          CachedNetworkImage(
            placeholder: (context, url) => Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: 48.w,
                height: 48.h,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
            ),
            imageUrl: transaction.bankLogo?.trim() ?? '',
            width: 48.w,
            height: 48.h,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.bankName ?? '',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Text(
                  transaction.beneficiaryAccountNo ?? '',
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '- ${transaction.totalAmount} ETB',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              Text(
                AppMapper.safeFormattedDate(
                  transaction.createdAt ?? '',
                ),
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          SizedBox(width: 8.w),
          Icon(
            Icons.chevron_right,
            color: Colors.grey,
            size: 24.w,
          ),
        ],
      ),
  
  */
    );
  }


}
