import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class BalanceTransactionShimmer extends StatelessWidget {
  const BalanceTransactionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(14),
                ),
              ),
              child: Row(
                children: [
                  _buildBankLogoShimmer(context),
                  const SizedBox(
                    width: 12,
                  ),
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              // bank

                              Row(
                                children: [
                                  Expanded(
                                    child: _buildShimmerBox(),
                                  ),
                                  const Expanded(child: SizedBox()),
                                  const SizedBox(
                                    width: 32,
                                  ),
                                  _buildShimmerBox(width: 84),
                                ],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              //acc
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildShimmerBox(),
                                  ),
                                  const Expanded(child: SizedBox()),
                                  _buildShimmerBox(),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // Expanded(
                        //   child: Column(
                        //     children: [
                        //       Row(
                        //         children: [
                        //           Expanded(
                        //             child: _buildShimmerBox(
                        //               height: 12,
                        //             ),
                        //           ),
                        //         ],
                        //       ),
                        //       const SizedBox(
                        //         height: 8,
                        //       ),
                        //       Row(
                        //         children: [
                        //           Expanded(
                        //             child: _buildShimmerBox(
                        //               width: 64,
                        //               height: 12,
                        //             ),
                        //           ),
                        //         ],
                        //       ),
                        //     ],
                        //   ),
                        // ),
                        // Expanded(
                        //   child: Container(),
                        // ),
                        // Column(
                        //   children: [
                        //     _buildShimmerBox(
                        //       width: 64,
                        //       height: 12,
                        //     ), // Bank Name
                        //     _buildShimmerBox(
                        //       width: 64,
                        //       height: 12,
                        //     ), // Bank Name
                        //   ],
                        // ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerBox({double width = 64, double height = 16}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  Widget _buildShimmerCircle({required double size}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: size,
        height: size,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  @override
  Widget _buildBankLogoShimmer(BuildContext context) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25), // Ensure circular shape
          child: SizedBox(
            height: 48,
            width: 48,
            child: _buildShimmerBox(
              width: 48,
              height: 48,
            ),
          ),
        ),
      ),
    );
  }
}
