import 'package:cbrs/features/transactions/presentation/widgets/empty_state/transactions_empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/balance_check/presentation/widgets/balance_history_widget.dart';

import 'package:cbrs/features/balance_check/presentation/widgets/balance_transaction_shimmer.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';

import 'package:cbrs/features/balance_check/application/bloc/balance_check_bloc.dart';
import 'package:cbrs/features/balance_check/application/bloc/recent_linked_transaction_bloc.dart';
import 'package:cbrs/features/balance_check/presentation/widgets/date_range_picker.dart';

class BalanceHistoryPage extends StatefulWidget {
  const BalanceHistoryPage({
    super.key,
  });
  @override
  State<BalanceHistoryPage> createState() => _BalanceHistoryPageState();
}

class _BalanceHistoryPageState extends State<BalanceHistoryPage> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    _handleRefresh();
  }

  Future<void> _handleRefresh() async {
    context
        .read<RecentLinkedTransactionBloc>()
        .add(const LinkedTransactionEvent());

    await Future.delayed(const Duration(seconds: 2));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Add Money Transactions',
        ),
        actions: [
          GestureDetector(
            onTap: () async {
              final dateRange = await showCustomDateRangePicker(context);
              if (dateRange != null) {
                // Show loading indicator
                setState(() => _isLoading = true);

                try {
                  context.read<RecentLinkedTransactionBloc>().add(
                        LinkedTransactionEvent(
                          startDate: dateRange.start.toIso8601String(),
                          endDate: dateRange.end.toIso8601String(),
                          isDatePicker: true,
                        ),
                      );

                  await Future.delayed(const Duration(milliseconds: 500));
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to load transactions: $e'),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                } finally {
                  setState(() => _isLoading = false);
                }
              }
            },
            child: Container(
              width: 30.w,
              height: 30.h,
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                color: Theme.of(context).secondaryHeaderColor,
                borderRadius: BorderRadius.circular(32.r),
              ),
              margin: EdgeInsets.symmetric(horizontal: 10.w),
              child: Center(
                child: Image.asset(
                  'assets/vectors/solar_calendar_broken.png',
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: BlocConsumer<RecentLinkedTransactionBloc, BalanceCheckState>(
            listener: (context, state) {},
            builder: (context, state) {
              debugPrint('Stat is check 😉 $state');
              if (state is TransactionLoadingState) {
                return Center(
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: 10,
                    itemBuilder: (context, index) {
                      return const BalanceTransactionShimmer();
                    },
                    separatorBuilder: (context, index) => Container(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      height: 1,
                      child: CustomPaint(
                        painter: DottedLinePainter(
                          color: Colors.grey.withOpacity(0.2),
                        ),
                        size: const Size(double.infinity, 1),
                      ),
                    ),
                  ),
                  // BalanceTransactionShimmer(),
                );
              } else if (state is LinkedTransactionLoadedState) {
                if (state.transactions.isEmpty) {
                  return const TransactionsEmptyState();
                } else {
                  return NotificationListener<ScrollNotification>(
                    onNotification: (ScrollNotification scrollInfo) {
                      debugPrint(
                        'Scroll position: ${scrollInfo.metrics.pixels} / ${scrollInfo.metrics.maxScrollExtent}',
                      );

                      if (scrollInfo is ScrollEndNotification &&
                          scrollInfo.metrics.pixels >=
                              scrollInfo.metrics.maxScrollExtent * 0.9 &&
                          state.hasNextPage) {
                        debugPrint('scrollinggg');
                        context
                            .read<RecentLinkedTransactionBloc>()
                            .add(const LinkedTransactionEvent());
                      }
                      return true;
                    },
                    child: RefreshIndicator(
                      onRefresh: () async {
                        _handleRefresh();
                      },
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: 1,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          return BalanceHistoryWidget(
                            transactions: state.transactions,
                            hasNextPage: state.hasNextPage,
                          );
                        },
                      ),
                    ),
                  );
                }
              } else {
                return Container();
              }
            },
          ),
        ),
      ),
    );
  }
}
