import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
// import 'package:cbrs/features/home/<USER>/widgets/transactions/dotted_line_painter.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cbrs/core/common/widgets/build_view_all.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_error_retry.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/balance_check/application/bloc/balance_check_bloc.dart';
import 'package:cbrs/features/balance_check/application/bloc/recent_linked_transaction_bloc.dart';
import 'package:cbrs/features/balance_check/domain/entity/linked_account.dart';
import 'package:cbrs/features/balance_check/presentation/widgets/empty_linked_account.dart';
import 'package:cbrs/features/balance_check/presentation/widgets/linked_card_shimmer.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/presentation/widgets/empty_state/transactions_empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class BalanceCheckPage extends StatefulWidget {
  const BalanceCheckPage({super.key});

  @override
  State<BalanceCheckPage> createState() => _BalanceCheckPageState();
}

class _BalanceCheckPageState extends State<BalanceCheckPage> {
  bool _isBalanceVisible = false;
  int _currentIndex = 0;
  late CarouselSliderController _carouselController;

  @override
  void initState() {
    super.initState();
    _carouselController = CarouselSliderController();

    context.read<BalanceCheckBloc>().add(const GetLinkedAccountsEvent());
    context
        .read<RecentLinkedTransactionBloc>()
        .add(const GetRecentTransactionsEvent());
  }

  void _handleFetchBalance({
    required String bank,
    required String accountNumber,
  }) {
    debugPrint('bank $bank accountnumber $accountNumber');

    setState(() {
      _isBalanceVisible = !_isBalanceVisible;
    });

/*
    context.read<BalanceCheckBloc>().add(
          GetAccountBalanceEvent(
            bank: bank,
            accountNumber: accountNumber,
          ),
        );
        */
  }

  void _handleRefresh() {
    context.read<BalanceCheckBloc>().add(const GetLinkedAccountsEvent());
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      appBar: AppBar(
        // This line reduces the space between the icon and title
        title: const Text(
          'Balance Check',
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocConsumer<BalanceCheckBloc, BalanceCheckState>(
        listener: (context, state) {},
        builder: (context, state) {
          debugPrint('State is changed $state');

          if (state is BalanceCheckLoading || state is BalanceCheckInitial) {
            return const LinkedCardShimmer();
          }
          if (state is BalanceCheckError) {
            return Center(
              child: CustomErrorRetry(
                onTap: () {
                  // _handleRefresh(loanType);
                  debugPrint('exchange rate retry tapped.');
                },
              ),
            );

            // const Text('Something went wrong. please try again later'));
          }

          return Column(
            children: [
              if (state is LinkedAccountsLoaded)
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
                      child: Column(
                        children: [
                          _buildBalanceCard(state.accounts),
                          SizedBox(height: 24.h),
                          Container(
                            padding: EdgeInsets.only(
                              left: 16.w,
                              right: 16.w,
                              bottom: 16.h,
                            ),
                            child: BlocBuilder<RecentLinkedTransactionBloc,
                                BalanceCheckState>(
                              builder: (context, state) {
                                debugPrint('Stat is  😉 $state');
                                if (state is TransactionLoadingState) {
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                } else if (state is RecentTransactionsLoaded) {
                                  if (state.transactions.isEmpty) {
                                    return const TransactionsEmptyState();
                                    // return Container(
                                    //   child: const CustomBuildText(
                                    //     text:
                                    //         'No add money transaction history',
                                    //   ),
                                    // );
                                  }
                                  return _buildRecentTransactions(
                                    context: context,
                                    transactions: state.transactions,
                                    hasMoreData: state.hasMoreData,
                                  );
                                }
                                if (state is TransactionErrorState) {
                                  return Center(
                                    child: CustomErrorRetry(
                                      onTap: () {
                                        _handleRefresh();
                                        debugPrint(
                                          'exchange rate retry tapped.',
                                        );
                                      },
                                    ),
                                  );
                                } else {
                                  return Container();
                                }
                              },
                            ),

                            // ,
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              else if (state is EmptyBalanceCard)
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(
                      top: 20.h,
                      bottom: 16.h,
                      left: 16.w,
                      right: 16.w,
                    ),
                    child: BlocBuilder<RecentLinkedTransactionBloc,
                        BalanceCheckState>(
                      builder: (context, state) {
                        if (state is TransactionLoadingState) {
                          return const Center(
                            child: Column(
                              children: [
                                EmptyLinkedAccount(),
                                SizedBox(
                                  height: 10,
                                ),
                                CircularProgressIndicator(),
                              ],
                            ),
                          );
                        }
                        if (state is TransactionErrorState) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const EmptyLinkedAccount(),
                                const SizedBox(
                                  height: 16,
                                ),
                                CustomErrorRetry(
                                  onTap: () {
                                    _handleRefresh();
                                    debugPrint(
                                      'exchange rate retry tapped.',
                                    );
                                  },
                                  errorMessage:
                                      'Something went wrong, while fetching previous transactions',
                                  hasDefaultMargin: false,
                                ),
                              ],
                            ),
                          );
                        } else if (state is RecentTransactionsLoaded) {
                          if (state.transactions.isEmpty) {
                            return Center(
                              child: Container(
                                child: const Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    EmptyLinkedAccount(),
                                  ],
                                ),
                              ),
                            );
                          } else {
                            return SingleChildScrollView(
                              child: Column(
                                children: [
                                  // _buildBalanceCard(state.accounts),
                                  const EmptyLinkedAccount(),
                                  const SizedBox(
                                    height: 16,
                                  ),
                                  _buildRecentTransactions(
                                    isPrevious: true,
                                    context: context,
                                    transactions: state.transactions,
                                    hasMoreData: state.hasMoreData,
                                  ),
                                ],
                              ),
                            );
                          }
                        }

                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                )
              else
                Expanded(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/Bank_icon.png',
                            width: 90,
                            height: 90,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'No Linked Account',
                            style: GoogleFonts.outfit(
                              fontWeight: FontWeight.bold,
                              fontSize: 18.sp,
                            ),
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'You haven’t linked any bank accounts yet. To link an account, request a linking code and visit your nearest bank branch.',
                            textAlign: TextAlign.center,
                            style: GoogleFonts.outfit(
                              fontSize: 14.sp,
                              color: Colors.black.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              Padding(
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                  bottom: 20.h,
                ),
                child: _buildActionButtons(
                  theme,
                  hasLinkedAccount: state is LinkedAccountsLoaded,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyBalanceCard() {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: const Color(0xFF737373),
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              "You didn't link one of your bank account. Please link or visit nearest bank branch.",
              textAlign: TextAlign.center,
              style: GoogleFonts.outfit(
                fontWeight: FontWeight.w500,
                fontSize: 16.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard(List<LinkedAccount> accounts) {
    return Column(
      children: [
        CarouselSlider(
          items: accounts
              .map(
                (account) =>
                    // Container(decoration: BoxDecoration(color: Colors.red),

                    // child: Column())

                    _buildCard(
                        bankLogo: account.bank.logo,
                        accountBalance: account.accountBalance.toString(),
                        accountNumber: account.accountNumber,
                        linkedDate: AppMapper.safeFormattedDate(
                            account.updatedAt,
                            hasTimeStamp: false),
                        bank: account.bank),
              )
              .toList(),
          options: CarouselOptions(
            height: MediaQuery.sizeOf(context).height * 0.3,
            viewportFraction: 0.9,
            enableInfiniteScroll: false,
            enlargeCenterPage: true,
            enlargeFactor: 0.2,
            onPageChanged: (index, reason) {
              setState(() {
                _currentIndex = index;
                _isBalanceVisible = false;
              });
            },
          ),
          carouselController: _carouselController,
        ),
        SizedBox(height: 8.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            accounts.length,
            (index) => GestureDetector(
              onTap: () {
                _carouselController.jumpToPage(index);
              },
              child: Container(
                width: _currentIndex == index ? 9.w : 7.w,
                height: _currentIndex == index ? 9.h : 7.h,
                margin: EdgeInsets.symmetric(horizontal: 1.w),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentIndex == index
                      ? const Color(0xFF559948)
                      : const Color(0xFFC0E4D6),
                  // color:
                  //     const Color(0xFF065234).withOpacity(index == 0 ? 1 : 0.3),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCard({
    required String bankLogo,
    required String accountBalance,
    required String accountNumber,
    required String linkedDate,
    required Bank bank,
  }) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: CachedNetworkImageProvider(
              // MediaRes.linkedBankCard,
              bank.card),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 6,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                    margin: EdgeInsets.only(top: 24.h),
                    height: 63,
                    width: 63,
                    padding: EdgeInsets.all(2),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(32)),
                    child: CustomCachedImage(
                      url: bankLogo,
                      boxFit: BoxFit.cover,
                      borderRadius: 32,
                    )

                    // Image.network(bankLogo)),

                    ),
              ),
              Center(
                child: Text(
                  'Account Balance',
                  style: GoogleFonts.outfit(
                    fontSize: 13.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              // SizedBox(height: 7.h),
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _isBalanceVisible
                          ? '${AppMapper.safeFormattedNumberWithDecimal(accountBalance)} ETB'
                          : '* * * * * *',
                      style: _isBalanceVisible
                          ? GoogleFonts.outfit(
                              fontSize: 28.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            )
                          : GoogleFonts.roboto(
                              fontSize: 28.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                    ),
                    SizedBox(width: 8.w),
                    GestureDetector(
                      onTap: () => _handleFetchBalance(
                        bank: bank.id,
                        accountNumber: accountNumber,
                      ),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 4.12.w,
                          vertical: 5.06.h,
                        ),
                        width: 24.7,
                        height: 22.h,
                        clipBehavior: Clip.antiAlias,
                        decoration: ShapeDecoration(
                          color: Colors.white.withOpacity(0.3),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4.12),
                          ),
                        ),
                        child: Image.asset(
                          _isBalanceVisible
                              ? MediaRes.eyeClose
                              : MediaRes.eyeOpen,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // SizedBox(height: 27.h),

          Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 15.w, top: 8.h, right: 15.w),
                width: double.infinity,
                decoration: ShapeDecoration(
                  // color: Colors.red,
                  color: Colors.white.withOpacity(0.16),
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                ),
                child: ClipRRect(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // TODO - assets should be defined in res/media_values.dart -- i will fix later
                      _buildCardIcons(
                        iconLocation: 'assets/vectors/bank_icon.png',
                        label: 'Account Number:',
                        value: accountNumber,
                      ),
                      _buildCardIcons(
                        iconLocation: 'assets/images/calander1.png',
                        label: 'Linked on:',
                        value: linkedDate,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardIcons({
    required String iconLocation,
    required String label,
    required String value,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // leading icon
          IntrinsicHeight(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 7.w, vertical: 7.h),
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                color: Colors.white.withOpacity(0.12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Image.asset(
                iconLocation,
                width: 24.w,
                height: 24.h,
                color: const Color(0xFFFFFFFF),
              ),
            ),
          ),
          SizedBox(width: 8.w),

          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                // 'Account Number:',
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w300,
                ),
              ),
              // SizedBox(height: 2.h),
              Text(
                value,
                // accountNumber,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.normal,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions({
    required BuildContext context,
    required List<Transaction> transactions,
    required bool hasMoreData,
    bool isPrevious = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isPrevious
              ? 'Previous Add Money Transactions'
              : 'Recent Add Money Transactions',
          style: GoogleFonts.outfit(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black.withOpacity(0.4),
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                blurRadius: 24,
                color: Colors.black.withOpacity(0.06),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                child: ListView.separated(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: transactions.length > 3 ? 3 : transactions.length,
                  separatorBuilder: (context, index) => Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    height: 1,
                    child: CustomPaint(
                      painter: DottedLinePainter(
                        color: Colors.grey.withOpacity(0.2),
                      ),
                      size: const Size(double.infinity, 1),
                    ),
                  ),
                  itemBuilder: (context, index) {
                    // final isEven = index % 2 == 0;
                    final item = transactions[index];
                    return Container(
                      // color: index % 2 == 0 ? Colors.green : Colors.yellow,
                      padding: EdgeInsets.only(bottom: 16.h, top: 12.h),
                      child: _buildTransactionItem(
                        context,
                        bankName: item.bankName ?? '',
                        accountNumber: item.beneficiaryAccountNo ?? '',
                        amount: item.totalAmount.toString(),
                        date: AppMapper.safeFormattedDate(item.createdAt),
                        logoPath: item.bankLogo ?? '',
                        transactionID: item.id,
                        transaction: item,
                      ),
                    );
                  },
                ),
              ),
              if (hasMoreData) ...[
                SizedBox(height: 16.h),
                Center(
                  child: BuildViewAll(
                    onTap: () => context.pushNamed(
                      AppRouteName.balanceHistory,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(
    ThemeData theme, {
    required bool hasLinkedAccount,
  }) {
    return SafeArea(
      child: Column(
        children: [
          if (hasLinkedAccount)
            _buildButton(
              theme,
              text: 'Add Money',
              isPrimary: true,
              routeName: AppRouteName.addMoney,
            ),
          SizedBox(height: 12.h),
          _buildButton(
            theme,
            text: hasLinkedAccount ? 'Link Another Account' : 'Link Account',
            isPrimary: !hasLinkedAccount,
            routeName: AppRouteName.linkAccountMenu,
          ),
        ],
      ),
    );
  }

  Widget _buildButton(
    ThemeData theme, {
    required String text,
    required bool isPrimary,
    required String routeName,
  }) {
    return CustomRoundedBtn(
      btnText: text,
      isLoading: false,
      bgColor: isPrimary ? theme.primaryColor : Colors.white,
      textColor: isPrimary ? Colors.white : theme.primaryColor,
      borderSide:
          isPrimary ? BorderSide.none : BorderSide(color: theme.primaryColor),
      onTap: () async {
        if (routeName == AppRouteName.addMoney && _currentIndex >= 0) {
          // Get the current selected bank from the carousel
          final state = context.read<BalanceCheckBloc>().state;
          if (state is LinkedAccountsLoaded && state.accounts.isNotEmpty) {
            final selectedAccount = state.accounts[_currentIndex];

            debugPrint('selectedAccount ${selectedAccount.bank.id}');

            // Navigate to add money with the selected bank ID
            await context.pushNamed(
              routeName,
              queryParameters: {
                'bankId': selectedAccount.bank.id,
                'accountNumber': selectedAccount.accountNumber,
              },
            );
            return;
          }
        }

        // Default navigation without parameters
        await context.pushNamed(
          routeName,
        );
      },
    );
  }

  Widget _buildTransactionItem(
    BuildContext context, {
    required String bankName,
    required String accountNumber,
    required String amount,
    required String date,
    required String logoPath,
    required String transactionID,
    required Transaction transaction,
  }) {
    debugPrint('hello logo$logoPath how');
    return InkWell(
      onTap: () => context.pushNamed(
        AppRouteName.transactionDetail,
        extra: {
          'transaction': transaction,
        },
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 0.h),
        child: Row(
          children: [
            CachedNetworkImage(
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 44.w,
                  height: 44.h,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              imageUrl: logoPath.trim(),
              width: 44.w,
              height: 44.h,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    bankName,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  Text(
                    accountNumber,
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: const Color(0xFFAAAAAA),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '- $amount ETB',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Text(
                  date,
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    color: const Color(0xFFAAAAAA),
                  ),
                ),
              ],
            ),
            SizedBox(width: 8.w),
            Image.asset(
              MediaRes.forwardIcon,
              width: 20.w,
              color: Colors.black.withOpacity(0.4),
            ),
          ],
        ),
      ),
    );
  }
}
