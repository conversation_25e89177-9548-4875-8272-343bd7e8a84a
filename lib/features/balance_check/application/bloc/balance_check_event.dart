part of 'balance_check_bloc.dart';

abstract class BalanceCheckEvent extends Equatable {
  const BalanceCheckEvent();

  @override
  List<Object?> get props => [];
}

class GetAccountBalanceEvent extends BalanceCheckEvent {
  const GetAccountBalanceEvent({
    required this.bank,
    required this.accountNumber,
  });
  final String bank;
  final String accountNumber;

  @override
  List<Object?> get props => [bank, accountNumber];
}

class GetLinkedAccountsEvent extends BalanceCheckEvent {
  const GetLinkedAccountsEvent();
}

class GetRecentTransactionsEvent extends BalanceCheckEvent {
  const GetRecentTransactionsEvent({
    this.limit = 3,
    this.offset = 0,
    this.transactionType,
    this.startDate,
    this.endDate,
  });
  final int limit;
  final int offset;
  final String? transactionType;
  final DateTime? startDate;
  final DateTime? endDate;

  @override
  List<Object?> get props => [
        limit,
        offset,
        transactionType,
        startDate,
        endDate,
      ];
}

class GetAccountTransactionsEvent extends BalanceCheckEvent {
  const GetAccountTransactionsEvent({
    required this.accountId,
    this.limit = 10,
    this.offset = 0,
  });
  final String accountId;
  final int limit;
  final int offset;

  @override
  List<Object?> get props => [accountId, limit, offset];
}

class RefreshBalanceEvent extends BalanceCheckEvent {
  const RefreshBalanceEvent({
    required this.bank,
  });
  final String bank;

  @override
  List<Object?> get props => [bank];
}

class LinkedTransactionEvent extends BalanceCheckEvent {
  const LinkedTransactionEvent({
    this.startDate,
    this.endDate,
    this.isDatePicker = false,
  });
  final String? startDate;
  final String? endDate;
  final bool isDatePicker;

  @override
  List<Object?> get props => [startDate, endDate];
}
