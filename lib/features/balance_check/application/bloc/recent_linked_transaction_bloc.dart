import 'package:bloc/bloc.dart';
import 'package:cbrs/features/balance_check/application/bloc/balance_check_bloc.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_check.dart';
import 'package:cbrs/features/balance_check/domain/entity/linked_account.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_account_transactions_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_all_transactions.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_linked_accounts_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_recent_transactions_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_wallet_balance_usecase.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:flutter/foundation.dart';
import 'package:equatable/equatable.dart';

class RecentLinkedTransactionBloc
    extends Bloc<BalanceCheckEvent, BalanceCheckState> {
  RecentLinkedTransactionBloc({
    required GetRecentTransactions getRecentTransactions,
    required GetAccountTransactions getAccountTransactions,
    required GetAllTransactionsUseCase getAllTransactionsUseCase,
  })  : _getRecentTransactions = getRecentTransactions,
        _getAccountTransactions = getAccountTransactions,
        _getAllTransactionsUseCase = getAllTransactionsUseCase,
        super(const BalanceCheckInitial()) {
    on<GetRecentTransactionsEvent>(_handleGetRecentTransactions);
    on<GetAccountTransactionsEvent>(_handleGetAccountTransactions);
    on<RefreshBalanceEvent>(_handleRefreshBalance);
    on<LinkedTransactionEvent>(_handlegetAllTransactions);
  }

  final GetRecentTransactions _getRecentTransactions;
  final GetAccountTransactions _getAccountTransactions;
  final GetAllTransactionsUseCase _getAllTransactionsUseCase;
  Future<void> _handleGetRecentTransactions(
    GetRecentTransactionsEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    emit(const TransactionLoadingState());

    final result = await _getRecentTransactions(
      GetRecentTransactionsParams(
        limit: event.limit,
        offset: event.offset,
        transactionType: event.transactionType,
        startDate: event.startDate,
        endDate: event.endDate,
      ),
    );

    result.fold(
      (failure) => emit(TransactionErrorState(failure.message)),
      (transactions) =>
          //  emit(const TransactionErrorState('failure.message'),
          emit(
        RecentTransactionsLoaded(
          transactions: transactions,
          hasMoreData: transactions.length >= event.limit,
        ),
      ),
    );
  }

  Future<void> _handleGetAccountTransactions(
    GetAccountTransactionsEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    emit(const BalanceCheckLoading());

    final result = await _getAccountTransactions(
      GetAccountTransactionsParams(
        accountId: event.accountId,
        limit: event.limit,
        offset: event.offset,
      ),
    );

    result.fold(
      (failure) => emit(BalanceCheckError(failure.message)),
      (transactions) => emit(
        AccountTransactionsLoaded(
          transactions: transactions,
          accountId: event.accountId,
          hasMoreData: transactions.length >= event.limit,
        ),
      ),
    );
  }

  Future<void> _handleRefreshBalance(
    RefreshBalanceEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    // Get current state
    final currentState = state;

    // If we have a loaded balance state, refresh it
    if (currentState is AccountBalanceLoaded) {
      add(
        GetAccountBalanceEvent(
          bank: event.bank,
          accountNumber: currentState.balance.accountNumber,
        ),
      );
    }

    // Refresh linked accounts
    add(const GetLinkedAccountsEvent());
  }

  Future<void> _handlegetAllTransactions(
    LinkedTransactionEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    try {
      final currentState = state;
      final currentPage = currentState is LinkedTransactionLoadedState
          ? currentState.currentPage
          : 1;

      if (currentState is! LinkedTransactionLoadedState) {
        emit(const TransactionLoadingState());
      }

      if (event.isDatePicker) {
        debugPrint("date picker true e🥡");
        emit(const TransactionLoadingState());
      }

      final result = await _getAllTransactionsUseCase(
        GetAllTransactionsParams(
          page: currentPage,
          startDate: event.startDate,
          endDate: event.endDate,
        ),
      );

      result.fold(
        (failure) =>
            emit(const TransactionErrorState('Failed to fetch transactions')),
        (data) {
          debugPrint('data hh $data ');
          debugPrint(
            'data hh lkoy ${data.balanceTransactionData.transactions} ',
          );

          const TransactionErrorState('Failed to fetch transactions');
          final newTransactions = data.balanceTransactionData.transactions;
          final pagination = data.balanceTransactionData.paginate;

          if (currentState is LinkedTransactionLoadedState &&
              !event.isDatePicker) {
            final updatedTransactions = [
              ...currentState.transactions,
              ...newTransactions,
            ];
            emit(
              currentState.copyWith(
                transactions: updatedTransactions,
                currentPage: currentState.currentPage + 1,
                hasNextPage: pagination.hasNextPage ?? false,
              ),
            );
          } else {
            debugPrint('Is not date picker');
            emit(
              LinkedTransactionLoadedState(
                transactions: newTransactions,
                currentPage: 1,
                hasNextPage: pagination.hasNextPage ?? false,
              ),
            );
          }
        },
      );
    } catch (e) {
      debugPrint('Error in fetching transactions: $e');
      emit(const TransactionErrorState('Failed to fetch transactions'));
    }
  }
}
