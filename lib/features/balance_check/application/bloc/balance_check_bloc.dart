import 'package:bloc/bloc.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_check.dart';
import 'package:cbrs/features/balance_check/domain/entity/linked_account.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_account_transactions_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_linked_accounts_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_recent_transactions_usecase.dart';
import 'package:cbrs/features/balance_check/domain/usecases/get_wallet_balance_usecase.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:flutter/foundation.dart';
import 'package:equatable/equatable.dart';

part 'balance_check_event.dart';
part 'balance_check_state.dart';

class BalanceCheckBloc extends Bloc<BalanceCheckEvent, BalanceCheckState> {
  BalanceCheckBloc({
    required GetAccountBalance getAccountBalance,
    required GetLinkedAccounts getLinkedAccounts,
    required GetRecentTransactions getRecentTransactions,
    required GetAccountTransactions getAccountTransactions,
  })  : _getAccountBalance = getAccountBalance,
        _getLinkedAccounts = getLinkedAccounts,
        _getRecentTransactions = getRecentTransactions,
        _getAccountTransactions = getAccountTransactions,
        super(const BalanceCheckInitial()) {
    on<GetAccountBalanceEvent>(_handleGetAccountBalance);
    on<GetLinkedAccountsEvent>(_handleGetLinkedAccounts);
    on<GetRecentTransactionsEvent>(_handleGetRecentTransactions);
    on<GetAccountTransactionsEvent>(_handleGetAccountTransactions);
    on<RefreshBalanceEvent>(_handleRefreshBalance);
  }
  final GetAccountBalance _getAccountBalance;
  final GetLinkedAccounts _getLinkedAccounts;
  final GetRecentTransactions _getRecentTransactions;
  final GetAccountTransactions _getAccountTransactions;

  Future<void> _handleGetAccountBalance(
    GetAccountBalanceEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    emit(const BalanceCheckLoading());

    final result = await _getAccountBalance(
      GetAccountBalanceParams(
        bank: event.bank,
        accountNumber: event.accountNumber,
      ),
    );

    result.fold(
      (failure) => emit(BalanceCheckError(failure.message)),
      (balance) => emit(AccountBalanceLoaded(balance)),
    );
  }

  Future<void> _handleGetLinkedAccounts(
    GetLinkedAccountsEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    emit(const BalanceCheckLoading());
    try {
      final result = await _getLinkedAccounts();

      result.fold(
        (failure) {
          debugPrint(' i get errrrr');

          emit(BalanceCheckError(failure.message));
        },
        (accounts) {
          debugPrint(' i get success');

          if (accounts.isEmpty) {
            debugPrint('account is empty');
            emit(const EmptyBalanceCard());
          } else {
            debugPrint('account is not empty ${accounts.length}');
            emit(LinkedAccountsLoaded(accounts));
            // emit(const EmptyBalanceCard());
          }
        },
      );
    } catch (err) {
      debugPrint(' e🇨🇦hellow $err');
    }
  }

  Future<void> _handleGetRecentTransactions(
    GetRecentTransactionsEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    emit(const TransactionLoadingState());

    final result = await _getRecentTransactions(
      GetRecentTransactionsParams(
        limit: event.limit,
        offset: event.offset,
        transactionType: event.transactionType,
        startDate: event.startDate,
        endDate: event.endDate,
      ),
    );

    result.fold(
      (failure) => emit(TransactionErrorState(failure.message)),
      (transactions) => emit(
        RecentTransactionsLoaded(
          transactions: transactions,
          hasMoreData: transactions.length >= event.limit,
        ),
      ),
    );
  }

  Future<void> _handleGetAccountTransactions(
    GetAccountTransactionsEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    emit(const BalanceCheckLoading());

    final result = await _getAccountTransactions(
      GetAccountTransactionsParams(
        accountId: event.accountId,
        limit: event.limit,
        offset: event.offset,
      ),
    );

    result.fold(
      (failure) => emit(BalanceCheckError(failure.message)),
      (transactions) => emit(
        AccountTransactionsLoaded(
          transactions: transactions,
          accountId: event.accountId,
          hasMoreData: transactions.length >= event.limit,
        ),
      ),
    );
  }

  Future<void> _handleRefreshBalance(
    RefreshBalanceEvent event,
    Emitter<BalanceCheckState> emit,
  ) async {
    // Get current state
    final currentState = state;

    // If we have a loaded balance state, refresh it
    if (currentState is AccountBalanceLoaded) {
      add(
        GetAccountBalanceEvent(
          bank: event.bank,
          accountNumber: currentState.balance.accountNumber,
        ),
      );
    }

    // Refresh linked accounts
    add(const GetLinkedAccountsEvent());
  }
}
