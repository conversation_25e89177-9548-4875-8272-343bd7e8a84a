part of 'balance_check_bloc.dart';

abstract class Balance<PERSON>heckState extends Equatable {
  const BalanceCheckState();

  @override
  List<Object?> get props => [];
}

class BalanceCheckInitial extends BalanceCheckState {
  const BalanceCheckInitial();
}

class BalanceCheckLoading extends BalanceCheckState {
  const BalanceCheckLoading();
}

class EmptyBalanceCard extends BalanceCheckState {
  const EmptyBalanceCard();
}

class BalanceCheckError extends BalanceCheckState {
  const BalanceCheckError(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}

class AccountBalanceLoaded extends BalanceCheckState {
  const AccountBalanceLoaded(this.balance);
  final BalanceCheckResponse balance;

  @override
  List<Object?> get props => [balance];
}

class LinkedAccountsLoaded extends BalanceCheckState {
  const LinkedAccountsLoaded(this.accounts);
  final List<LinkedAccount> accounts;

  @override
  List<Object?> get props => [accounts];
}

class TransactionLoadingState extends BalanceCheckState {
  const TransactionLoadingState();
}

class TransactionErrorState extends BalanceCheckState {
  const TransactionErrorState(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}

class RecentTransactionsLoaded extends BalanceCheckState {
  const RecentTransactionsLoaded({
    required this.transactions,
    this.hasMoreData = true,
  });
  final List<Transaction> transactions;
  final bool hasMoreData;

  @override
  List<Object?> get props => [transactions, hasMoreData];
}

class AccountTransactionsLoaded extends BalanceCheckState {
  const AccountTransactionsLoaded({
    required this.transactions,
    required this.accountId,
    this.hasMoreData = true,
  });
  final List<Transaction> transactions;
  final bool hasMoreData;
  final String accountId;

  @override
  List<Object?> get props => [transactions, accountId, hasMoreData];
}

class LinkedTransactionLoadedState extends BalanceCheckState {
  const LinkedTransactionLoadedState({
    required this.transactions,
    required this.hasNextPage,
    required this.currentPage,
  });
  final List<Transaction> transactions;
  final bool hasNextPage;
  final int currentPage;

  LinkedTransactionLoadedState copyWith({
    List<Transaction>? transactions,
    bool? hasNextPage,
    int? currentPage,
  }) {
    return LinkedTransactionLoadedState(
      transactions: transactions ?? this.transactions,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      currentPage: currentPage ?? this.currentPage,
    );
  }

  @override
  List<Object?> get props => [transactions, hasNextPage, currentPage];
}
