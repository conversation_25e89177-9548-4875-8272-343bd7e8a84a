import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/balance_check/data/models/balance_check_model.dart';
import 'package:cbrs/features/balance_check/data/models/balance_transactions_model.dart';
import 'package:cbrs/features/balance_check/data/models/linked_account_response_model.dart';
import 'package:cbrs/features/transactions/data/models/transaction_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_transactions.dart';

abstract class BalanceCheckRemoteDataSource {
  Future<BalanceCheckResponseModel> getAccountBalance({
    required String bank,
    required String accountNumber,
  });

  Future<List<TransactionModel>> getRecentTransactions({
    int limit = 10,
    int offset = 0,
    String? transactionType,
  });

  Future<BalanceTransactionsModel> getAllTransactions({
    required int page,
    String? startDate,
    String? endDate,
  });
  Future<List<LinkedAccountResponseModel>> getLinkedAccounts({
    int page = 1,
    int perPage = 10,
  });
}

class BalanceCheckRemoteDataSourceImpl implements BalanceCheckRemoteDataSource {
  const BalanceCheckRemoteDataSourceImpl({
    required Dio dio,
    required AuthLocalDataSource authLocalDataSource,
  })  : _dio = dio,
        _authLocalDataSource = authLocalDataSource;
  final Dio _dio;
  final AuthLocalDataSource _authLocalDataSource;

  Future<Response<T>> _safeApiCall<T>(
    Future<Response<T>> Function() apiCall,
  ) async {
    try {
      final token = await _authLocalDataSource.getAuthToken();

      if (token == null) {
        throw const ApiException(
          message: 'Authorization token not found',
          statusCode: 401,
        );
      }

      _dio.options.headers['Authorization'] = 'Bearer $token';
      final response = await apiCall();

      if (response.statusCode == 401) {
        throw const ApiException(
          message: 'Session expired. Please login again.',
          statusCode: 401,
        );
      }

      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Exception _handleDioError(DioException e) {
    final statusCode = e.response?.statusCode;
    final errorMessage = _extractErrorMessage(e.response?.data);
    final errorDetails = _extractErrorDetails(e.response?.data);

    switch (statusCode) {
      case 400:
        return ValidationException(message: errorMessage, errors: errorDetails);
      case 401:
        return UnauthorizedException(message: errorMessage);
      case 403:
        return ForbiddenException(message: errorMessage);
      case 404:
        return NotFoundException(message: errorMessage);
      case 429:
        return RateLimitException(message: errorMessage);
      default:
        return ApiException(
          message: errorMessage,
          statusCode: statusCode ?? 500,
        );
    }
  }

  String _extractErrorMessage(dynamic responseData) {
    if (responseData is Map<String, dynamic>) {
      return responseData['message']?.toString() ??
          responseData['error']?.toString() ??
          responseData['errorMessage']?.toString() ??
          'An unexpected error occurred';
    }
    return 'An unexpected error occurred';
  }

  Map<String, dynamic>? _extractErrorDetails(dynamic responseData) {
    if (responseData is Map && responseData.containsKey('errors')) {
      return responseData['errors'] as Map<String, dynamic>;
    }
    return null;
  }

  @override
  Future<BalanceCheckResponseModel> getAccountBalance({
    required String bank,
    required String accountNumber,
  }) async {
    final response = await _safeApiCall(
      () => _dio.post<Map<String, dynamic>>(
        ApiEndpoints.checkLinkedAccountBalance,
        data: {
          'bank': bank,
          'accountNumber': accountNumber,
        },
      ),
    );

    return BalanceCheckResponseModel.fromJson(response.data!);
  }

  @override
  Future<List<TransactionModel>> getRecentTransactions({
    int limit = 10,
    int offset = 0,
    String? transactionType = 'add_money',
  }) async {
    debugPrint(
      'Recent Transactions from linked accountn transactionType $transactionType ',
    );
    final response = await _safeApiCall(
      () => _dio.get(
        ApiEndpoints.linkedAccountTransactions,
        queryParameters: {
          'limit': limit,
          'offset': offset,
          'transactionType': 'add_money',
          'status': 'COMPLETED',
        },
      ),
    );
    debugPrint(
      'response of remote recent  transactionType $transactionType transaction $response',
    );

    final transactionsJson = (response.data['data']
        as Map<String, dynamic>)['docs'] as List<dynamic>;

    return transactionsJson
        .map((json) => TransactionModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  @override
  Future<BalanceTransactionsModel> getAllTransactions({
    required int page,
    String? startDate,
    String? endDate,
  }) async {
    debugPrint(
      'Recent Transactions from linked accountn for page $page and startDate $startDate and endDate $endDate',
    );
    try {
      final response = await _safeApiCall(
        () => _dio.get(
          ApiEndpoints.linkedAccountTransactions,
          queryParameters: {
            'page': page,
            'startDate': startDate,
            'endDate': endDate,
            'transactionType': 'add_money',
            'status': 'COMPLETED',
          },
        ),
      );
      debugPrint(
          'Remote response for all transaction where page is  $page teh resposne is $response and ${response.data}');

      return BalanceTransactionsModel.fromJson(
        response.data as Map<String, dynamic>,
      );
    } catch (err) {
      // TODO
      throw ApiException(message: err.toString(), statusCode: 500);
    }
  }

  @override
  Future<List<LinkedAccountResponseModel>> getLinkedAccounts({
    int page = 1,
    int perPage = 10,
  }) async {
    final response = await _safeApiCall(
      () => _dio.get(
        ApiEndpoints.linkedAccountsPaginate,
        queryParameters: {'page': page, 'perPage': perPage, 'status': 'LINKED'},
      ),
    );

    final accountsJson = (response.data['data'] as Map<String, dynamic>)['docs']
        as List<dynamic>;

    return accountsJson
        .map(
          (json) =>
              LinkedAccountResponseModel.fromJson(json as Map<String, dynamic>),
        )
        .toList();
  }
}
