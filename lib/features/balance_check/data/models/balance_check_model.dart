import 'package:flutter/material.dart';

import '../../domain/entity/balance_check.dart';

class BalanceCheckRequestModel extends BalanceCheckRequest {
  const BalanceCheckRequestModel({
    required super.bank,
    required super.accountNumber,
  });

  factory BalanceCheckRequestModel.fromJson(Map<String, dynamic> json) {
    debugPrint("banks data ${json['bank']}");
    return BalanceCheckRequestModel(
      bank: json['bank']?.toString() ?? '',
      accountNumber: json['accountNumber']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'bank': bank,
        'accountNumber': accountNumber,
      };
}

class BalanceCheckResponseModel extends BalanceCheckResponse {
  const BalanceCheckResponseModel({
    required super.accountBalance,
    required super.linkedDate,
    required super.bankName,
    required super.logo,
    required super.accountNumber,
  });

  factory BalanceCheckResponseModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>? ?? {};

    return BalanceCheckResponseModel(
      accountBalance: (data['accountBalance'] as num?)?.toDouble() ?? 0.0,
      linkedDate: DateTime.parse(
          data['linkedDate']?.toString() ?? DateTime.now().toIso8601String()),
      bankName: data['bankName']?.toString() ?? '',
      logo: data['logo']?.toString() ?? '',
      accountNumber: data['accountNumber']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'data': {
          'accountBalance': accountBalance,
          'linkedDate': linkedDate.toIso8601String(),
          'bankName': bankName,
          'logo': logo,
          'accountNumber': accountNumber,
        },
      };
}
