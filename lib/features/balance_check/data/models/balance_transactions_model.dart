import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_transactions.dart';
import 'package:cbrs/features/transactions/data/models/transaction_model.dart';
import 'package:flutter/cupertino.dart';

class BalanceTransactionsModel extends BalanceTransactionsEntity {
  const BalanceTransactionsModel({
    required super.balanceTransactionData,
  });

  factory BalanceTransactionsModel.fromJson(Map<String, dynamic> json) {
    return BalanceTransactionsModel(
      balanceTransactionData: json['data'] != null
          ? BalanceTransactionsDataModel.fromJson(
              AppMapper.safeMap(json['data']),
            )
          : throw Exception("Missing 'data' field"),
    );
  }
}

class BalanceTransactionsDataModel extends BalanceTransactionsDataEntity {
  BalanceTransactionsDataModel({
    required super.paginate,
    required super.transactions,
  });

  factory BalanceTransactionsDataModel.fromJson(Map<String, dynamic> json) {
    return BalanceTransactionsDataModel(
      paginate: BalancePaginationModel.fromJson(AppMapper.safeMap(json)),
      transactions: AppMapper.safeList<dynamic>(json['docs'])
          .map((item) => TransactionModel.fromJson(AppMapper.safeMap(item)))
          .toList(),
    );
  }
}

class BalancePaginationModel extends BalancePaginationEntity {
  BalancePaginationModel({
    required super.hasNextPage,
    required super.total,
    required super.limit,
    required super.pages,
    required super.hasPrevPage,
  });

  factory BalancePaginationModel.fromJson(Map<String, dynamic> json) {
    debugPrint('Pagingation json ');
    return BalancePaginationModel(
      hasNextPage: json['hasNextPage'] as bool ?? false,
      hasPrevPage: json['hasPrevPage'] as bool ?? false,
      total: AppMapper.safeInt(json['total']),
      pages: AppMapper.safeInt(json['pages']),
      limit: AppMapper.safeInt(json['limit']),
    );
  }
}
