import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/balance_check/data/datasources/balance_check_remote_datasource.dart';
import 'package:cbrs/features/balance_check/data/models/balance_transactions_model.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_check.dart';
import 'package:cbrs/features/balance_check/domain/entity/linked_account.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:dartz/dartz.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_transactions.dart';

class BalanceCheckRepositoryImpl implements BalanceCheckRepository {
  final BalanceCheckRemoteDataSource _remoteDataSource;

  const BalanceCheckRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, BalanceCheckResponse>> getAccountBalance({
    required String bank,
    required String accountNumber,
  }) async {
    try {
      final response = await _remoteDataSource.getAccountBalance(
        bank: bank,
        accountNumber: accountNumber,
      );
      return Right(response);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Transaction>>> getRecentTransactions({
    required int limit,
    required int offset,
    String? transactionType,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final transactions = await _remoteDataSource.getRecentTransactions(
        limit: limit,
        offset: offset,
        transactionType: transactionType,
      );
      return Right(transactions);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, BalanceTransactionsModel>> getAllTransactions({
    required int page,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final transactions = await _remoteDataSource.getAllTransactions(
        page: page,
        startDate: startDate,
        endDate: endDate,

      );
      return Right(transactions);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<LinkedAccount>>> getLinkedAccounts() async {
    try {
      final accounts = await _remoteDataSource.getLinkedAccounts();
      return Right(accounts);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Transaction>>> getAccountTransactions({
    required String accountId,
    required int limit,
    required int offset,
  }) async {
    try {
      final transactions = await _remoteDataSource.getRecentTransactions(
        limit: limit,
        offset: offset,
        transactionType: 'add_money',
      );
      return Right(transactions);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  ResultFuture<List<Transaction>> getLinkedTransactions({
    required int nextPage,
    String? startDate,
    String? endDate,
    String? transactionType,
  }) async {
    try {
      final transactions = await _remoteDataSource.getRecentTransactions(
        limit: 10,
        offset: (nextPage - 1) * 10,
        transactionType: transactionType,
      );
      return Right(transactions);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
