import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:equatable/equatable.dart';

class GetRecentTransactions
    extends UsecaseWithParams<List<Transaction>, GetRecentTransactionsParams> {
  final BalanceCheckRepository _repository;

  const GetRecentTransactions(this._repository);

  @override
  ResultFuture<List<Transaction>> call(
      GetRecentTransactionsParams params) async {
    return _repository.getRecentTransactions(
      limit: params.limit,
      offset: params.offset,
      transactionType: params.transactionType,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

class GetRecentTransactionsParams extends Equatable {
  final int limit;
  final int offset;
  final String? transactionType;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetRecentTransactionsParams({
    required this.limit,
    required this.offset,
    this.transactionType,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [
        limit,
        offset,
        transactionType,
        startDate,
        endDate,
      ];
}
