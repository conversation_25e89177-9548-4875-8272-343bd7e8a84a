import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';
import 'package:equatable/equatable.dart';

class GetAccountTransactions
    extends UsecaseWithParams<List<Transaction>, GetAccountTransactionsParams> {
  final BalanceCheckRepository _repository;

  const GetAccountTransactions(this._repository);

  @override
  ResultFuture<List<Transaction>> call(
      GetAccountTransactionsParams params) async {
    return _repository.getAccountTransactions(
      accountId: params.accountId,
      limit: params.limit,
      offset: params.offset,
    );
  }
}

class GetAccountTransactionsParams extends Equatable {
  final String accountId;
  final int limit;
  final int offset;

  const GetAccountTransactionsParams({
    required this.accountId,
    this.limit = 10,
    this.offset = 0,
  });

  @override
  List<Object?> get props => [accountId, limit, offset];
}
