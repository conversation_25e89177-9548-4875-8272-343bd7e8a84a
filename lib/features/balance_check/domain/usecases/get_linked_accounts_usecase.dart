import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/balance_check/domain/entity/linked_account.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';

class GetLinkedAccounts extends UsecaseWithoutParams<List<LinkedAccount>> {
  final BalanceCheckRepository _repository;

  const GetLinkedAccounts(this._repository);

  @override
  ResultFuture<List<LinkedAccount>> call() async =>
      _repository.getLinkedAccounts();
}
