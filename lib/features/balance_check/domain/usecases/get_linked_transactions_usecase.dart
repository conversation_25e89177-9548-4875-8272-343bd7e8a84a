import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:equatable/equatable.dart';

class GetLinkedTransactionsUsecase
    extends UsecaseWithParams<List<Transaction>, GetLinkedTransactionParams> {
  const GetLinkedTransactionsUsecase(this._repository);
  final BalanceCheckRepository _repository;

  @override
  ResultFuture<List<Transaction>> call(
    GetLinkedTransactionParams params,
  ) async {
    return _repository.getLinkedTransactions(
      nextPage: params.nextPage,
      transactionType: params.transactionType,
    );
  }
}

class GetLinkedTransactionParams extends Equatable {
  const GetLinkedTransactionParams({
    required this.nextPage,
    this.transactionType,
  });
  final int nextPage;
  final String? transactionType;

  @override
  List<Object?> get props => [
        nextPage,
        transactionType,
      ];
}
