import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_check.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';
import 'package:equatable/equatable.dart';

class GetAccountBalance
    extends UsecaseWithParams<BalanceCheckResponse, GetAccountBalanceParams> {
  final BalanceCheckRepository _repository;

  const GetAccountBalance(this._repository);

  @override
  ResultFuture<BalanceCheckResponse> call(
      GetAccountBalanceParams params) async {
    return _repository.getAccountBalance(
      bank: params.bank,
      accountNumber: params.accountNumber,
    );
  }
}

class GetAccountBalanceParams extends Equatable {
  final String bank;
  final String accountNumber;

  const GetAccountBalanceParams({
    required this.bank,
    required this.accountNumber,
  });

  @override
  List<Object?> get props => [bank, accountNumber];
}
