import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_transactions.dart';
import 'package:cbrs/features/balance_check/domain/repositories/balance_check_repository.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:equatable/equatable.dart';

class GetAllTransactionsUseCase extends UsecaseWithParams<
    BalanceTransactionsEntity, GetAllTransactionsParams> {
  const GetAllTransactionsUseCase(this._repository);
  final BalanceCheckRepository _repository;

  @override
  ResultFuture<BalanceTransactionsEntity> call(
    GetAllTransactionsParams params,
  ) async {
    return _repository.getAllTransactions(
      page: params.page,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

class GetAllTransactionsParams extends Equatable {
  const GetAllTransactionsParams({
    required this.page,
    this.startDate,
    this.endDate,
  });
  final int page;
  final String? startDate;
  final String? endDate;

  @override
  List<Object?> get props => [page, startDate, endDate];
}
