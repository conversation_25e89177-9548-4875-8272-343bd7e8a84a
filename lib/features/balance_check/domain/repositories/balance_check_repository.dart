import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_check.dart';
import 'package:cbrs/features/balance_check/domain/entity/balance_transactions.dart';
import 'package:cbrs/features/balance_check/domain/entity/linked_account.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';

abstract class BalanceCheckRepository {
  /// Gets the balance details for a specific bank account
  ResultFuture<BalanceCheckResponse> getAccountBalance({
    required String bank,
    required String accountNumber,
  });

  /// Gets list of recent transactions with pagination and filtering
  ResultFuture<List<Transaction>> getRecentTransactions({
    required int limit,
    required int offset,
    String? transactionType,
    DateTime? startDate,
    DateTime? endDate,
  });

  ResultFuture<BalanceTransactionsEntity> getAllTransactions({
    required int page,
    String? startDate,
    String? endDate,
  });

  // get all transactions with paginate
  ResultFuture<List<Transaction>> getLinkedTransactions({
    required int nextPage,
    String? startDate,
    String? endDate,
    String? transactionType,
  });

  /// Gets list of all linked bank accounts
  ResultFuture<List<LinkedAccount>> getLinkedAccounts();

  /// Gets the transaction history for a specific linked account
  ResultFuture<List<Transaction>> getAccountTransactions({
    required String accountId,
    required int limit,
    required int offset,
  });
}
