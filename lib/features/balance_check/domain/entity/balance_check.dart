import 'package:equatable/equatable.dart';

class BalanceCheckRequest extends Equatable {
  final String bank;
  final String accountNumber;

  const BalanceCheckRequest({
    required this.bank,
    required this.accountNumber,
  });

  @override
  List<Object?> get props => [bank, accountNumber];
}

class BalanceCheckResponse extends Equatable {
  final double accountBalance;
  final DateTime linkedDate;
  final String bankName;
  final String logo;
  final String accountNumber;

  const BalanceCheckResponse({
    required this.accountBalance,
    required this.linkedDate,
    required this.bankName,
    required this.logo,
    required this.accountNumber,
  });

  @override
  List<Object?> get props => [
        accountBalance,
        linkedDate,
        bankName,
        logo,
        accountNumber,
      ];
}
