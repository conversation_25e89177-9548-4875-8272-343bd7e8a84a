import 'package:equatable/equatable.dart';

class LinkedAccount extends Equatable {
  final String id;
  final Bank bank;
  final String accountNumber;
  final Member member;
  final int linkingCode;
  final String status;
  final bool enabled;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double accountBalance;

  const LinkedAccount({
    required this.id,
    required this.bank,
    required this.accountNumber,
    required this.member,
    required this.linkingCode,
    required this.status,
    required this.enabled,
    required this.createdAt,
    required this.updatedAt,
    required this.accountBalance,
  });

  @override
  List<Object?> get props => [
        id,
        bank,
        accountNumber,
        member,
        linkingCode,
        status,
        enabled,
        createdAt,
        updatedAt,
        accountBalance,
      ];
}

class Bank extends Equatable {
  final String id;
  final String name;
  final String logo;
  final String card;


  const Bank({
    required this.id,
    required this.name,
    required this.logo,
    required this.card,

  });

  @override
  List<Object?> get props => [id, name, logo, card];
}

class Member extends Equatable {
  final String id;
  final String firstName;
  final String middleName;
  final String lastName;
  final String email;

  const Member({
    required this.id,
    required this.firstName,
    required this.middleName,
    required this.lastName,
    required this.email,
  });

  @override
  List<Object?> get props => [
        id,
        firstName,
        middleName,
        lastName,
        email,
      ];
}

class PaginatedLinkedAccounts extends Equatable {
  final List<LinkedAccount> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  const PaginatedLinkedAccounts({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  @override
  List<Object?> get props => [
        docs,
        totalDocs,
        limit,
        totalPages,
        page,
        pagingCounter,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      ];
}
