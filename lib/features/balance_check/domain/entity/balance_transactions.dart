import 'package:cbrs/features/transactions/domain/entities/transaction.dart';

class BalanceTransactionsEntity {
  final BalanceTransactionsDataEntity balanceTransactionData;

  const BalanceTransactionsEntity({
    required this.balanceTransactionData,
  });
}

class BalanceTransactionsDataEntity {
  final List<Transaction> transactions;
  final BalancePaginationEntity paginate;

  BalanceTransactionsDataEntity(
      {required this.transactions, required this.paginate});
}

class BalancePaginationEntity {
  final int? total;
  final int? page;
  final int? limit;
  final int? pages;
  final bool? hasPrevPage, hasNextPage;

  BalancePaginationEntity({
    this.total,
    this.page,
    this.limit,
    this.pages,
    this.hasPrevPage,
    this.hasNextPage,
  });
}
