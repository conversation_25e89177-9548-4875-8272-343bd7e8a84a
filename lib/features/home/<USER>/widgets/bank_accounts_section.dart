import 'dart:io';

import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:cbrs/features/home/<USER>/widgets/badge_section.dart';
import 'package:cbrs/features/home/<USER>/widgets/quick_actions_grid.dart';
import 'package:cbrs/features/home/<USER>/widgets/wallet_selector_bottom_sheet.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_bloc.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_event.dart';
import 'package:cbrs/features/notifications/application/notification_bloc/notification_state.dart';
import 'package:cbrs/features/orders/application/blocs/order_bloc.dart';
import 'package:cbrs/features/orders/application/blocs/order_event.dart';
import 'package:cbrs/features/orders/application/blocs/order_state.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';

class BankAccountsSection extends StatefulWidget {
  const BankAccountsSection({
    required this.userName,
    // required this.walletBalance,
    required this.onNotificationTap,
    required this.onOrdersTap,
    // required this.selectedWallet,
    // required this.onWalletSelected,
    // required this.onToggleAmountVisibility,
    required this.onRefreshBalances,
    required this.onProfileTap,
    super.key,
    // this.isAmountHidden = false,
    this.avatarUrl,
    // this.isBalanceLoading = false,
  });
  final String userName;
  // final double walletBalance;
  final VoidCallback onNotificationTap;
  final VoidCallback onOrdersTap;
  final VoidCallback onProfileTap;
  final VoidCallback onRefreshBalances;
  // final String selectedWallet;
  // final Function(String) onWalletSelected;
  // final bool isAmountHidden;
  // final VoidCallback onToggleAmountVisibility;
  final String? avatarUrl;
  // final bool isBalanceLoading;

  @override
  State<BankAccountsSection> createState() => _BankAccountsSectionState();
}

class _BankAccountsSectionState extends State<BankAccountsSection> {
  late AssetImage birrBg;
  late AssetImage dollarBg;

  String selectedWallet = 'USD';
  bool isAmountHidden = true;
  bool refechtBalance = false;

  void setIsAmountHidden(bool isUsdWallet) {
    setState(() {
      isAmountHidden = !isAmountHidden;
    });
    if (!isAmountHidden) {
      context.read<WalletBalanceBloc>().add(
            FetchWalletEvent(isUsdWallet: isUsdWallet),
          );
    }
  }

  @override
  void initState() {
    super.initState();
    selectedWallet = GlobalVariable.currentlySelectedWallet ?? 'USD';
    dollarBg = const AssetImage(MediaRes.usdBackground);
    birrBg = const AssetImage(MediaRes.birrBackground);
    context.read<HomeBloc>().add(const HomeProfileFetchingEvent());
    context.read<NotificationBloc>().add(const FetchUnseenCount());

    if (selectedWallet == 'USD')
      context.read<OrderBloc>().add(FetchUnredeemedCount());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    precacheImage(birrBg, context);
    precacheImage(dollarBg, context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WalletBalanceBloc, HomeState>(
      listener: (context, state) {
        setState(() {
          if (state is WalletLoadedState) {
            setState(() {
              selectedWallet = state.isUsdWallet ? 'USD' : 'ETB';
            });
          }
        });
      },
      builder: (context, state) {
        //state is WalletLoadedState && state.isUsdWallet;
        return SizedBox(
          height: MediaQuery.sizeOf(context).height * 0.44,
          child: Stack(
            // clipBehavior: Clip.none,
            children: [
              Container(
                height: MediaQuery.sizeOf(context).height * 0.393,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  image: DecorationImage(
                    image: selectedWallet == 'USD' ? dollarBg : birrBg,
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      Colors.black.withOpacity(0.1),
                      BlendMode.darken,
                    ),
                  ),
                ),
                child: Column(
                  children: [
                    Expanded(
                      child: SafeArea(
                        child: Container(
                          padding: EdgeInsets.only(
                            left: 16.w,
                            right: 16.h,
                          ),
                          child: BlocBuilder<WalletBalanceBloc, HomeState>(
                            builder: (context, state) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (Platform.isAndroid)
                                    SizedBox(height: 14.h),
                                  buildHeader(context),
                                  SizedBox(
                                    height: MediaQuery.sizeOf(context).height *
                                        0.01.h,
                                  ),
                                  Center(
                                    child: state is WalletLoadingState
                                        ? Shimmer.fromColors(
                                            baseColor:
                                                Colors.white.withOpacity(0.3),
                                            highlightColor:
                                                Colors.white.withOpacity(0.5),
                                            child: Container(
                                              width: 140.w,
                                              height: 44.h,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(32.r),
                                              ),
                                            ),
                                          )
                                        : WalletSelector
                                            .buildWalletSelectorButton(
                                            selectedWallet: selectedWallet,
                                            onTap: () async {
                                              // _showCurtainSheet(context);
                                              _showWalletSelector();
                                            },
                                          ),
                                  ),
                                  SizedBox(height: 24.h),
                                  // SizedBox(height: MediaQuery.sizeOf(context).height * 0.015.h),

                                  buildBalance(),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 0.h,
                left: 12.w,
                right: 12.w,
                child: QuickActionsGrid(
                  walletType: selectedWallet,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  int _notificationCount = 0;

  Widget buildHeader(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: GestureDetector(
                onTap: widget.onProfileTap,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 1.w,
                        ),
                      ),
                      child: state is ProfileLoadingState
                          ? Shimmer.fromColors(
                              baseColor: Colors.white.withOpacity(0.3),
                              highlightColor: Colors.white.withOpacity(0.5),
                              child: CircleAvatar(
                                backgroundColor: Colors.white,
                                radius: 24.w,
                              ),
                            )
                          : state is HomeProfileLoadedState
                              ? InkWell(
                                  onTap: () {
                                    GlobalVariable.currentTabIndex = 3;
                                  },
                                  child: CircleAvatar(
                                    backgroundColor: Colors.white,
                                    radius: 24.w,
                                    child: state.localUser.avatar.isEmpty
                                        ? Text(
                                            getInitials(
                                              state.localUser.fullName,
                                            ),
                                            style: TextStyle(
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16.sp,
                                            ),
                                          )
                                        : ClipOval(
                                            child: CustomCachedImage(
                                              url: state.localUser.avatar,
                                              width: 48.w,
                                              height: 48.h,
                                              isProfile: true,
                                              name: widget.userName,
                                            ),
                                          ),
                                  ),
                                )
                              : Container(),
                    ),
                    SizedBox(width: 6.5.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              if (state is ProfileLoadingState)
                                // if                               if( state is  ProfileLoadingState)

                                Shimmer.fromColors(
                                  baseColor: Colors.white.withOpacity(0.3),
                                  highlightColor: Colors.white.withOpacity(0.5),
                                  child: Container(
                                    width: 120.w,
                                    height: 20.h,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                  ),
                                )
                              else if (state is HomeProfileLoadedState)
                                CustomBuildText(
                                  text: 'Hi, ${state.localUser.fullName}',
                                  style: GoogleFonts.outfit(
                                    color: Colors.white,
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              if (widget.userName != 'Loading...') ...[
                                SizedBox(width: 2.w),
                                Text(
                                  '👋',
                                  style: GoogleFonts.outfit(
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          if (state is ProfileLoadingState)
                            Shimmer.fromColors(
                              baseColor: Colors.white.withOpacity(0.3),
                              highlightColor: Colors.white.withOpacity(0.5),
                              child: Container(
                                width: 150.w,
                                height: 16.h,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                              ),
                            )
                          else
                            Text(
                              'Welcome Back to Connect!',
                              style: GoogleFonts.outfit(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w300,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Row(
              children: [
                // Chat icon

                if (selectedWallet == 'USD')
                  BlocBuilder<OrderBloc, OrderState>(
                    builder: (context, state) {
                      var unredeemedCount = 0;
                      if (state is OrderCountLoaded) {
                        unredeemedCount = state.unredeemedCount;
                      }
                      return BadgeSection(
                        count: unredeemedCount,
                        iconUrl: MediaRes.ordersIcon,
                        onTap: widget.onOrdersTap,
                        width: 44,
                      );
                    },
                  ),
                SizedBox(width: 4.w),

                BadgeSection(
                  count: 0, // No badge count for chat
                  onTap: () => context.pushNamed(AppRouteName.conversations),
                  icon: FluentIcons.chat_24_regular,
                  width: 44,
                ),
                SizedBox(width: 4.w),

                BlocConsumer<NotificationBloc, NotificationState>(
                  listener: (context, state) {
                    if (state is NotificationLoaded) {
                      setState(() {
                        _notificationCount = state.unseenCount;
                      });
                    }
                  },
                  builder: (context, state) {
                    return BadgeSection(
                      count: _notificationCount,
                      iconUrl: MediaRes.notificationIcon,
                      onTap: () async {
                        final back =
                            await context.pushNamed(AppRouteName.notifications);

                        // context
                        //     .read<NotificationBloc>()
                        //     .add(const FetchUnseenCount());
                      },
                      width: 44,
                    );
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget buildBalance() {
    return BlocBuilder<WalletBalanceBloc, HomeState>(
      builder: (context, state) {
        return state is WalletLoadingState
            ? _shimmerBalance()
            : state is WalletLoadedState
                ? Column(
                    children: [
                      Container(
                        // color: Colors.red,
                        child: InkWell(
                          onTap: () => setIsAmountHidden(state.isUsdWallet),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (isAmountHidden)
                                Padding(
                                  padding: const EdgeInsets.only(
                                    left: 2,
                                    bottom: 1,
                                    top: 1,
                                  ),
                                  child: Text(
                                    '************',
                                    style: GoogleFonts.roboto(
                                      color: Colors.white,
                                      fontSize: 33,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                )
                              else
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    CustomBuildText(
                                      text:
                                          CurrencyFormatter.formatWalletBalance(
                                        state.isUsdWallet
                                            ? state.usdBalance
                                            : state.etbBalance,
                                        selectedWallet,
                                      ).split('.').first,
                                      color: Colors.white,
                                      fontSize: 34.sp,

                                      // widget.walletBalance.toString().length > 19
                                      //     ? 24.sp
                                      //     : 34.sp,
                                      fontWeight: FontWeight.w700,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8),
                                      child: CustomBuildText(
                                        text:
                                            '.${CurrencyFormatter.formatWalletBalance(
                                          state.isUsdWallet
                                              ? state.usdBalance
                                              : state.etbBalance,
                                          selectedWallet,
                                        ).split('.').last}',
                                        color: Colors.white,
                                        fontSize: 24.sp,
                                        // todo - uncomment
                                        // widget.walletBalance.toString().length >
                                        //         19
                                        //     ? 16.sp
                                        //     : widget.walletBalance.toString().length > 15
                                        //         ? 20.sp
                                        //         : 24.sp,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              SizedBox(width: 16.w),
                              Padding(
                                padding: EdgeInsets.only(
                                  bottom: isAmountHidden ? 12.h : 0.h,
                                ),
                                child: Container(
                                  child: Image.asset(
                                    isAmountHidden
                                        ? MediaRes.eyeOpen
                                        : MediaRes.eyeClose,
                                    color: Colors.white,
                                    width: 20.h,
                                    height: 20.h,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Wallet Balance',
                        style: GoogleFonts.outfit(
                          color: Colors.white,
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  )
                : 
                Center(child: Icon(Icons.refresh, size: 40, color: Colors.white,))
                ;
      },
    );
  }

  String getInitials(String name) {
    if (name.isEmpty) return '?';

    final names = name.trim().split(' ').where((n) => n.isNotEmpty).toList();

    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.length == 1) {
      return names[0][0].toUpperCase();
    }

    return '?';
  }

  Widget _shimmerBalance() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          children: [
            Shimmer.fromColors(
              baseColor: Colors.white.withOpacity(0.3),
              highlightColor: Colors.white.withOpacity(0.5),
              child: Container(
                width: 180.w,
                height: 44.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
            SizedBox(height: 8.h),
            Shimmer.fromColors(
              baseColor: Colors.white.withOpacity(0.2),
              highlightColor: Colors.white.withOpacity(0.4),
              child: Container(
                width: 100.w,
                height: 16.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showWalletSelector() {
    final homeState = context.read<HomeBloc>().state;
    final walletState = context.read<WalletBalanceBloc>().state;

    final isHomeProfile = homeState is HomeProfileLoadedState;

    //  homeState is HomeLoaded
    //     ? (homeState.data.profile.phoneNumber.isNotEmpty
    //         ? homeState.data.profile.phoneNumber
    //         : homeState.data.profile.email)
    //     : '';

    final dateFormatter = DateFormat('MMM dd, yyyy');
    if (isHomeProfile)
      debugPrint(
        'homeState.localUser.createdAt ${homeState.localUser.createdAt}',
      );

    final registrationDate = isHomeProfile
        ? homeState.localUser.createdAt.isNotEmpty
            ? dateFormatter
                .format(DateTime.parse(homeState.localUser.createdAt))
            : dateFormatter.format(DateTime.now())
        : dateFormatter.format(DateTime.now());
    final labelPhoneOrEmail = isHomeProfile
        ? (homeState.localUser.phoneNumber?.isNotEmpty ?? false)
            ? 'Phone Number'
            : 'Email'
        : '';
    final phoneOrEmail = isHomeProfile
        ? (homeState.localUser.phoneNumber?.isNotEmpty ?? false)
            ? homeState.localUser.phoneNumber
            : homeState.localUser.email
        : '';

    debugPrint('hommmee');
    if (homeState is HomeLoaded) {
      debugPrint('Created at: ${homeState.data.profile.createdAt}');
    }
    debugPrint('hommmee');
    if (homeState is HomeLoaded) {
      debugPrint('Crreated ad ${homeState.data.profile.createdAt}');
    }

    WalletSelector.show(
      context,
      selectedWallet: selectedWallet,
      onWalletSelected: (walletType) {
        debugPrint('wallet selected $walletType');

        if (walletState is WalletLoadedState) {
          ////1. isusdwallet chec
          ///2. selectew wallet value check
          /// true?
          final forceTheme = walletState.isUsdWallet
              ? (walletType == 'USD' ? false : true)
              : (walletType == 'ETB' ? false : true);

          debugPrint(
            'ffforcetheme $forceTheme cand uisud ${walletState.isUsdWallet}',
          );

          // walletState.isUsdWallet ? value == 'USD' ? false: true : false;

          // walletState.isUsdWallet ? value == 'USD'?false : value == 'ETB' ? false: false;
          context.read<WalletBalanceBloc>().add(
                WalletSwitchingEvent(
                  walletType: walletType,
                  etbBalance: walletState.etbBalance,
                  usdBalance: walletState.usdBalance,
                  forceTheme: forceTheme,
                ),
              );
        }
        // context.read<WalletBalanceBloc>().add(
        //       WalletSwitchingEvent(walletType: value),
        //     );
      },
      phoneOrEmail: phoneOrEmail ?? '',
      labelPhoneOrEmail: labelPhoneOrEmail,
      registrationDate: registrationDate,
    );
  }

  // Map<String, double?> getWalletBalances() {
  //   debugPrint('Here we Go MO');
  //   final homeState = context.read<HomeBloc>().state;
  //   if (homeState is HomeLoaded) {
  //     return {
  //       'ETB': homeState.data.etbBalance,
  //       'USD': homeState.data.usdBalance,
  //     };
  //   }
  //   return {
  //     'ETB': selectedWallet == 'ETB' ? widget.walletBalance : null,
  //     'USD': selectedWallet == 'USD' ? widget.walletBalance : null,
  //   };
  // }
}
