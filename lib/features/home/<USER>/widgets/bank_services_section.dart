import 'package:cbrs/features/home/<USER>/widgets/home_action_icons.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';

class BankServicesSection extends StatelessWidget {
  const BankServicesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const HomeSectionHeaders(
            title: 'Bank Services',
            description: 'Link and manage your bank accounts all in one place.',
          ),
          SizedBox(
            height: 16.h,
          ),
          GestureDetector(
            onTap: () {
              context.pushNamed(AppRouteName.linkAccountMenu);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 16.h),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.r),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const HomeActionIcons(
                    imageIcon: MediaRes.addNewBankAccIcon,
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  const Expanded(
                    child: HomeSectionHeaders(
                      title: 'Add New Bank Account',
                      description:
                          'Link multiple bank accounts & top up your wallet easily in-app.',
                    ),
                  ),
                  Image.asset(MediaRes.navigationArrowRight,
                      width: 20, height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /*
  Widget build(BuildContext context) {


    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeSectionHeaders(
                title: 'Bank Services',
                description:
                    'Link and manage your bank accounts all in one place.',
              ),
              SizedBox(
                height: 16.h,
              ),
              Row(
                children: [
                  Expanded(
                    child: _buildServiceCard(
                      title: 'Linked Bank Accounts',
                      description:
                          'View the balances of your linked accounts from multiple banks.',
                      icon: MediaRes.bank_three_d,
                      context: context,
                      onTap: () {
                        context.pushNamed(AppRouteName.balanceCheck);
                      },
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: _buildServiceCard(
                      title: 'Add New Bank Account',
                      description:
                          'Link multiple bank accounts & top up your wallet easily in-app.',
                      icon: MediaRes.bank_three_d,
                      context: context,
                      onTap: () {
                        context.pushNamed(AppRouteName.linkAccountMenu);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
*/
  Widget _buildServiceCard({
    required String title,
    required String description,
    required String icon,
    required BuildContext context,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          image: const DecorationImage(
            image: AssetImage(MediaRes.birrBackground),
            fit: BoxFit.cover,
          ),
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: const [
            BoxShadow(
              color: Color(0x14000000),
              blurRadius: 24,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.fromLTRB(12.w, 12.h, 12.w, 12.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              HomeActionIcons(
                imageIcon: icon,
                bgColor: const Color(0xFFF9F9F9),
              ),
              SizedBox(height: 8.h),
              Flexible(
                child: Text(
                  title,
                  textAlign: TextAlign.left,
                  style: GoogleFonts.outfit(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                  maxLines: 2,
                ),
              ),
              SizedBox(height: 4.h),
              Flexible(
                child: Text(
                  description,
                  style: GoogleFonts.outfit(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.white.withOpacity(0.8),
                  ),
                  maxLines: 2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
