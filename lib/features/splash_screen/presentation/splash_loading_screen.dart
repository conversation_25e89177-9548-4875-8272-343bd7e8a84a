import 'dart:io';

import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/constants/storage_keys.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/in_app_update/application_layer/controller/app_update.state.dart';
import 'package:cbrs/features/in_app_update/application_layer/controller/app_update_bloc.dart';
import 'package:cbrs/features/in_app_update/application_layer/controller/app_update_event.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:geolocator/geolocator.dart';

class SplashLoadingScreen extends StatefulWidget {
  const SplashLoadingScreen({super.key});

  @override
  State<SplashLoadingScreen> createState() => _SplashLoadingScreenState();
}

class _SplashLoadingScreenState extends State<SplashLoadingScreen>
    with TickerProviderStateMixin {
  final bool _isCheckingConnectivity = false;

  late HiveBoxManager _hiveBoxManager;
  late AuthLocalDataSource _authLocalDataSource;
  late AnimationController _lottieController;

  // Device securiity
  bool isJailBroken = false;
  bool isJailBrokenCustom = false;
  bool isMockLocation = false;
  bool isRealDevice = false;
  bool isOnExternalStorage = false;
  bool isSafeDevice = false;
  bool isDevelopmentModeEnable = false;
  Map<String, dynamic> jailbreakDetails = {};
  Map<String, dynamic> rootDetectionDetails = {};

  // device securtity

  @override
  void initState() {
    _hiveBoxManager = sl<HiveBoxManager>();
    _authLocalDataSource = sl<AuthLocalDataSource>();
    _lottieController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    super.initState();
    initialize();
  }

  Future<void> getAppVersionInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final version = packageInfo.version;

    final platform = Platform.isAndroid ? 'android' : 'ios';

    // final buildNumber = packageInfo.buildNumber;

    await _checkUpdate(version: version, osPlatform: platform);
  }

  Future<void> _checkUpdate({
    required String version,
    required String osPlatform,
  }) async {
    context.read<InAppUpdateBloc>().add(
          CheckAppUpdateEvent(versionNumber: version, osPlatform: osPlatform),
        );
  }

  @override
  void dispose() {
    _lottieController.dispose();
    super.dispose();
  }

  Future<void> initialize() async {
    try {
      final connectivityController = Get.find<ConnectivityController>();

      // if (!_isCheckingConnectivity) {
      //   _isCheckingConnectivity = true;
      //   if (!connectivityController.isConnected) {
      //     _isCheckingConnectivity = false;
      //     safeNavigate(AppRouteName.connectionLost);
      //   }
      //   _isCheckingConnectivity = false;
      // }

      // _hiveBoxManager.mainBox.delete(StorageKeys.firstTimer);

      //.r(StorageKeys.firstTimer, defaultValue: true) as bool;

      final deviceCheckController = Get.find<DeviceCheckController>();
      await deviceCheckController.checkDeviceOnStartup();

      GlobalVariable.isDeviceVerified = deviceCheckController.isDeviceVerified;

      // if (!connectivityController.isConnected ||
      //     deviceCheckController.noConnectivity) {
      //   safeNavigate(AppRouteName.connectionLost);
      // }

      if (GlobalVariable.isDeviceVerified ||
          GlobalVariable.isDeviceRegisteredOnConnect) {
        await _authLocalDataSource.clearAuthToken();
        await _authLocalDataSource.clearUserData();
        return safeNavigate(AppRouteName.tokenDeviceLogin);
      } else if (GlobalVariable.deviceCheckNetworkError) {
        return safeNavigate(AppRouteName.connectionLost);
      } else {
        final isFirstTimer = _hiveBoxManager.mainBox
            .get(StorageKeys.firstTimer, defaultValue: true) as bool;

        if (isFirstTimer) {
          return safeNavigate(AppRouteName.onboarding);
        }

        return safeNavigate(AppRouteName.guestHomePage);
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        await _authLocalDataSource.clearAuthToken();
        safeNavigate(AppRouteName.tokenDeviceLogin);
        return;
      }

      safeNavigate(AppRouteName.connectionLost);
    } catch (ex) {
      // error happende from below line
      safeNavigate(AppRouteName.connectionLost);
    }
  }

  void safeNavigate(String routeName) {
    if (!mounted) return;
    context.goNamed(routeName);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<InAppUpdateBloc, InAppUpdateState>(
        listener: (context, state) {
          if (state is InAppUpdateSuccess) {
            if (state.updateStatus.updateAvailable) {
              safeNavigate(AppRouteName.forceUpdate);
            } else {
              initialize();
            }
          } else if (state is InAppUpdateFailure) {
            CustomToastification(context, message: state.message);
          }
        },
        child: Center(
          child: Lottie.asset(
            'assets/json/CONNECT_BIRR_LOTTIE.json',
            controller: _lottieController,
            width: 200,
            height: 200,
            fit: BoxFit.contain,
            onLoaded: (composition) {
              _lottieController
                ..duration = composition.duration
                ..repeat();
            },
          ),
        ),
      ),
    );
  }
}
