abstract class CashInCashOutEvent {}

class SearchAgentRequested extends CashInCashOutEvent {
  final String agentId;

  SearchAgentRequested(this.agentId);
}

class ParseAgentQr extends CashInCashOutEvent {
  final String qrString;

  ParseAgentQr({required this.qrString});
}

class GenerateVoucherRequested extends CashInCashOutEvent {
  final double? amount;
  final bool isCashIn;
  final String currency;

  GenerateVoucherRequested({
    this.amount,
    required this.isCashIn,
    this.currency = 'ETB',
  });
}

class GenerateBillRequested extends CashInCashOutEvent {
  final String agentId;
  final double amount;
  final String transactionType;
  final String currency;

  GenerateBillRequested({
    required this.agentId,
    required this.amount,
    required this.transactionType,
    this.currency = 'ETB',
  });
}

class CheckTransferRulesRequested extends CashInCashOutEvent {
  final double amount;
  final String currency;

  CheckTransferRulesRequested({
    required this.amount,
    this.currency = 'ETB',
  });
}

class ConfirmPaymentRequested extends CashInCashOutEvent {
  final String pin;
  final String billRefNo;
  final String transactionType;

  ConfirmPaymentRequested({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
  });

  @override
  List<Object> get props => [pin, billRefNo, transactionType];
}

class VerifyOtpRequested extends CashInCashOutEvent {
  final String billRefNo;
  final int otpCode;

  VerifyOtpRequested({
    required this.billRefNo,
    required this.otpCode,
  });
}

class ResendOtpRequested extends CashInCashOutEvent {
  final String billRefNo;

  ResendOtpRequested({
    required this.billRefNo,
  });
}

class ResetAgentState extends CashInCashOutEvent {}
