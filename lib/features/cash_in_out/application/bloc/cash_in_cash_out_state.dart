import 'package:cbrs/features/cash_in_out/domain/entities/bill.dart';
import 'package:cbrs/features/cash_in_out/domain/entities/payment_confirmation.dart';
import 'package:cbrs/features/cash_in_out/data/models/agent_qr_response_model.dart';

import '../../domain/entities/agent.dart';
import '../../domain/entities/voucher.dart';

abstract class CashInCashOutState {
  const CashInCashOutState();
}

// Initial States
class CashInCashOutInitial extends CashInCashOutState {}

// Loading States
class CashInCashOutLoading extends CashInCashOutState {}

// Success States
class AgentSearchSuccess extends CashInCashOutState {
  final Agent agent;
  const AgentSearchSuccess(this.agent);
}

class AgentQrParsed extends CashInCashOutState {
  final AgentQrResponseModel agent;
  const AgentQrParsed(this.agent);
}

class VoucherGenerationSuccess extends CashInCashOutState {
  final Voucher voucher;
  const VoucherGenerationSuccess(this.voucher);
}

// Failure States
class CashInCashOutFailure extends CashInCashOutState {
  final String message;
  const CashInCashOutFailure(this.message);
}

class BillGenerationSuccess extends CashInCashOutState {
  final Bill bill;
  const BillGenerationSuccess(this.bill);
}

class TransferRulesCheckSuccess extends CashInCashOutState {
  final String authorizationType;
  final double amount;
  final double serviceCharge;
  final double vat;
  final double total;

  const TransferRulesCheckSuccess({
    required this.authorizationType,
    required this.amount,
    required this.serviceCharge,
    required this.vat,
    required this.total,
  });
}

class PaymentConfirmationSuccess extends CashInCashOutState {
  final PaymentConfirmation confirmation;

  const PaymentConfirmationSuccess(this.confirmation);

  @override
  List<Object> get props => [confirmation];
}

class OtpVerificationSuccess extends CashInCashOutState {
  final String billRefNo;
  final bool requiresPin;
  final String? otp;

  const OtpVerificationSuccess({
    required this.billRefNo,
    required this.requiresPin,
    this.otp,
  });

  @override
  List<Object?> get props => [billRefNo, requiresPin, otp];
}

class OtpResendSuccess extends CashInCashOutState {
  const OtpResendSuccess();
}
