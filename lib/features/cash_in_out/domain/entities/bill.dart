class Bill {
  final String id;
  final String billRefNo;
  final double billAmount;
  final String transactionType;
  final String currency;
  final String status;
  final double serviceCharge;
  final double vat;
  final double totalAmount;
  final String beneficiaryId;
  final String beneficiaryName;
  final String? senderName;
  final String? senderPhone;
  final String? senderEmail;


  final String authorizationType;
  final String createdAt;

  const Bill({
    required this.id,
    required this.billRefNo,
    required this.billAmount,
    required this.transactionType,
    required this.currency,
    required this.status,
    required this.serviceCharge,
    required this.vat,
    required this.totalAmount,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.authorizationType,
    required this.createdAt,
    this.senderName,
    this.senderPhone,
    this.senderEmail,


  });
}
