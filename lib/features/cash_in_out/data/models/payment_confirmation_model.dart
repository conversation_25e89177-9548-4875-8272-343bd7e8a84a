import 'package:cbrs/features/cash_in_out/domain/entities/payment_confirmation.dart';

class PaymentConfirmationModel extends PaymentConfirmation {
  const PaymentConfirmationModel({
    required super.id,
    required super.billRefNo,
    required super.billAmount,
    required super.status,
    required super.walletFTNumber,
    required super.paidDate,
    required super.paidAmount,
  });

  factory PaymentConfirmationModel.fromJson(Map<String, dynamic> json) {
    return PaymentConfirmationModel(
      id: json['id'] as String,
      billRefNo: json['billRefNo'] as String,
      billAmount: (json['billAmount'] as num).toDouble(),
      status: json['status'] as String,
      walletFTNumber: json['walletFTNumber'] as String? ?? '',
      paidDate: json['paidDate'] as String? ?? '',
      paidAmount: (json['paidAmount'] as num).toDouble(),
    );
  }
}
