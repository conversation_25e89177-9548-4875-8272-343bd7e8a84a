import '../../domain/entities/transfer_rules.dart';

class TransferRulesModel extends TransferRules {
  const TransferRulesModel({
    required super.authorizationType,
    required super.amount,
    required super.serviceCharge,
    required super.vat,
  });

  factory TransferRulesModel.fromJson(Map<String, dynamic> json) {
    return TransferRulesModel(
      authorizationType: json['authorization_type'] as String,
      amount: (json['amount'] as num).toDouble(),
      serviceCharge: (json['serviceCharge'] as num).toDouble(),
      vat: (json['VAT'] as num).toDouble(),
    );
  }
}
