import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/cash_in_out/domain/entities/bill.dart';

class BillModel extends Bill {
  const BillModel({
    required super.id,
    required super.billRefNo,
    required super.billAmount,
    required super.transactionType,
    required super.currency,
    required super.status,
    required super.serviceCharge,
    required super.vat,
    required super.totalAmount,
    required super.beneficiaryId,
    required super.beneficiaryName,
    required super.authorizationType,
    required super.createdAt,
  });

  factory BillModel.fromJson(Map<String, dynamic> json) {
    return BillModel(
      id: json['id'] as String,
      billRefNo: json['billRefNo'] as String,
      billAmount: (json['billAmount'] as num).toDouble(),
      transactionType: json['transactionType'] as String,
      currency: json['originalCurrency'] as String,
      status: json['status'] as String,
      serviceCharge: json['serviceCharge'] != null
          ? (json['serviceCharge'] as num).toDouble()
          : 0.0,
      vat: json['VAT'] != null ? (json['VAT'] as num).toDouble() : 0.0,
      totalAmount: json['totalAmount'] != null
          ? (json['totalAmount'] as num).toDouble()
          : 0.0,
      beneficiaryId: json['beneficiaryId'] as String,
      beneficiaryName: json['beneficiaryName'] as String,
      authorizationType: json['authorization_type'] as String,
      createdAt: AppMapper.safeFormattedDate(json['createdAt']),
    );
  }
}
