import '../../domain/entities/voucher.dart';

class VoucherModel extends Voucher {
  const VoucherModel({
    required super.voucherCode,
    super.amount,
    required super.currency,
    required super.transactionType,
    required super.member,
  });

  factory VoucherModel.fromJson(Map<String, dynamic> json) {
    return VoucherModel(
      voucherCode: json['voucherCode'].toString(),
      amount: json['amount']?.toDouble() as double?,
      currency: json['currency'] as String,
      transactionType: json['transactionType'] as String,
      member: MemberModel.fromJson(json['member'] as Map<String, dynamic>),
    );
  }
}

class MemberModel extends Member {
  const MemberModel({
    required super.fullName,
    required super.email,
    required super.phone,
    required super.id,
  });

  factory MemberModel.fromJson(Map<String, dynamic> json) {
    return MemberModel(
      fullName: json['fullName'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
       id: json['_id'] as String,
    );
  }
}
