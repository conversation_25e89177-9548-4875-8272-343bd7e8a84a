import 'package:equatable/equatable.dart';

class AgentQrResponseModel extends Equatable {
  final String bic;
  final String id;
  final String currency;
  final String countryCode;
  final String name;
  final String cityName;
  final String? agentTill;
  final String userCode;

  const AgentQrResponseModel({
    required this.bic,
    required this.id,
    required this.currency,
    required this.countryCode,
    required this.name,
    required this.cityName,
    required this.userCode,
    this.agentTill,
  });

  factory AgentQrResponseModel.fromJson(Map<String, dynamic> json) {
    return AgentQrResponseModel(
      bic: json['BIC'] as String,
      id: json['account_number'] as String,
      currency: json['currency'] as String,
      countryCode: json['country_code'] as String,
      name: json['name'] as String,
      cityName: json['cityName'] as String,
      userCode: json['userCode'] as String,
      agentTill: json['additional_information']?['merchant_till'] as String?,
    );
  }

  @override
  List<Object?> get props =>
      [bic, id, currency, countryCode, name, cityName, agentTill, userCode];
}
