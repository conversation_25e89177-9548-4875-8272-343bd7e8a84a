import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';

abstract class CashOutOtpVerificationRemoteDataSource {
  Future<bool> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  });

  Future<void> resendOtp({
    required String billRefNo,
    required String otpFor,
  });
}


class CashOutOtpVerificationRemoteDataSourceImpl implements CashOutOtpVerificationRemoteDataSource {
  final ApiService apiService;

  CashOutOtpVerificationRemoteDataSourceImpl({required this.apiService});

  @override
  Future<bool> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  }) async {
    final result = await apiService.post<bool>(
      ApiEndpoints.verifyOtp,
      data: {
        'billRefNo': billRefNo,
        'otpFor': otpFor,
        'otpCode': otpCode,
      },
      parser: (data) => data['success'] == true,
    );

    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<void> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    final result = await apiService.post<bool>(
      ApiEndpoints.resendOtp,
      data: {
        'billRefNo': billRefNo,
        'otpFor': otpFor,
      },
      parser: (data) => data['success'] == true,
    );

    result.fold(
      (success) {
        if (!success) {
          throw ApiException(message: 'Failed to resend OTP', statusCode: 400);
        }
      },
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 500,
      ),
    );

  }
}
