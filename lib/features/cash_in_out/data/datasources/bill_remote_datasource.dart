import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/features/cash_in_out/data/models/bill_model.dart';

abstract class BillRemoteDataSource {
  Future<Result<BillModel>> generateBill({
    required String agentId,
    required double amount,
    required String transactionType,
    String currency,
  });
}

class BillRemoteDataSourceImpl implements BillRemoteDataSource {

  BillRemoteDataSourceImpl({required this.apiService});
  final ApiService apiService;

  @override
  Future<Result<BillModel>> generateBill({
    required String agentId,
    required double amount,
    required String transactionType,
    String currency = 'ETB',
  }) async {
    return apiService.post<BillModel>(
      ApiEndpoints.generateBill,
      data: {
        'agentID': agentId,
        'amount': amount,
        'transactionType': transactionType,
        'currency': currency,
      },
      parser: (data) {
        final responseData = data['data'];
        return BillModel.fromJson(responseData as Map<String,dynamic>);
      },
    );
  }
}
