import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import '../models/voucher_model.dart';

abstract class VoucherRemoteDataSource {
  Future<VoucherModel> generateVoucher({
    double? amount,
    required bool isCashIn,
    String currency,
  });
}
class VoucherRemoteDataSourceImpl implements VoucherRemoteDataSource {
  final ApiService apiService;

  VoucherRemoteDataSourceImpl({required this.apiService});

  @override
  Future<VoucherModel> generateVoucher({
    double? amount,
    required bool isCashIn,
    String currency = 'ETB',
  }) async {
    final data = <String, dynamic>{
      if (amount != null) 'amount': amount,
      'transactionType': isCashIn ? 'cash_in' : 'cash_out',
      'currency': currency,
    };

    final result = await apiService.post(
      ApiEndpoints.generateVoucher,
      data: data,
      parser: (responseData) {
        if (responseData == null ||
            responseData['success'] != true ||
            responseData['data'] == null) {
          throw ServerException(
            message: responseData?['message']?.toString() ??
                'Invalid response from server',
            statusCode: 400,
          );
        }
        return VoucherModel.fromJson(
            responseData['data'] as Map<String, dynamic>);
      },
      requiresAuth: true,
    );

    return result.fold(
      (voucher) => voucher,
      (error) => throw ServerException(
        message: error.message ?? 'Failed to generate voucher',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }
}
