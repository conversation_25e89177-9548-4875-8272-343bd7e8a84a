import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/features/cash_in_out/data/models/tranfer_rules_model.dart';

abstract class CashOutTransferRulesRemoteDataSource {
  Future<Result<TransferRulesModel>> checkTransferRules({
    required double amount,
    String currency,
    String productType,
  });
}

class CashOutTransferRulesRemoteDataSourceImpl implements CashOutTransferRulesRemoteDataSource {
  final ApiService apiService;

  CashOutTransferRulesRemoteDataSourceImpl({required this.apiService});

  @override
  Future<Result<TransferRulesModel>> checkTransferRules({
    required double amount,
    String currency = 'ETB',
    String productType = 'cash_out',
  }) {
    return apiService.post<TransferRulesModel>(
      ApiEndpoints.validateTransferAmount,
      data: {
        'amount': amount,
        'currency': currency,
        'productType': productType,
      },
      parser: (data) {
        return TransferRulesModel.fromJson(data['data'] as Map<String, dynamic>);
      },
    );
  }
}
