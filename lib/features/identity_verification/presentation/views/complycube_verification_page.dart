import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/identity_verification/domain/entities/complycube_verification_result.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/complycube_bloc.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/complycube_event.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/complycube_state.dart';
import 'package:complycube/complycube.dart';
import 'package:complycube/models/events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toastification/toastification.dart';

class ComplyCubeVerificationPage extends StatefulWidget {
  const ComplyCubeVerificationPage({super.key});

  @override
  State<ComplyCubeVerificationPage> createState() =>
      _ComplyCubeVerificationPageState();
}

class _ComplyCubeVerificationPageState
    extends State<ComplyCubeVerificationPage> {
  @override
  void initState() {
    super.initState();
    // Initialize ComplyCube by generating SDK token
    context.read<ComplyCubeBloc>().add(const GenerateComplyCubeSdkTokenEvent());
  }

  void _onComplyCubeSuccess(ComplyCubeResult result) {
    if (!mounted) return;

    debugPrint('ComplyCube verification success: $result');

    // Get the current state to access token response
    final currentState = context.read<ComplyCubeBloc>().state;
    if (currentState is! ComplyCubeWidgetReady) {
      debugPrint('Error: ComplyCube widget not in ready state');
      return;
    }

    // Create verification result
    // Extract document ID from documentIds array if available
    String? documentId;
    if (result.ids?['documentIds'] is List) {
      final documentIds = result.ids!['documentIds'] as List;
      if (documentIds.isNotEmpty) {
        documentId = documentIds.first.toString();
      }
    }

    final verificationResult = ComplyCubeVerificationResult(
      clientId: currentState.tokenResponse.clientId,
      documentId: documentId,
      livePhotoId: result.livePhotoId,
      checkId: result.ids?['checkId'] as String?,
      status: 'completed',
      outcome: 'success',
      metadata: {
        'completedAt': DateTime.now().toIso8601String(),
        'sdkVersion': '0.0.8',
        'liveVideoId': result.liveVideoId,
        'allIds': result.ids,
      },
    );

    // Process verification result through BLoC
    context.read<ComplyCubeBloc>().add(
          ProcessComplyCubeVerificationEvent(
            verificationResult: verificationResult,
          ),
        );
  }

  void _onComplyCubeError(List<ComplyCubeError> errors) {
    if (!mounted) return;
    debugPrint('ComplyCube verification error: $errors');
    final errorMessage = errors.isNotEmpty
        ? errors.first.message ?? 'Unknown error'
        : 'Verification failed';

    // Send error event to BLoC
    context.read<ComplyCubeBloc>().add(
          ComplyCubeVerificationErrorEvent(
            errorMessage: errorMessage,
            errorCode:
                errors.isNotEmpty ? errors.first.errorCode?.toString() : null,
          ),
        );
  }

  void _onComplyCubeCancelled(ComplyCubeError error) {
    if (!mounted) return;
    debugPrint('ComplyCube verification cancelled: $error');

    // Send cancellation event to BLoC
    context.read<ComplyCubeBloc>().add(
          ComplyCubeVerificationCancelledEvent(
            reason: error.message ?? 'User cancelled',
          ),
        );
  }

  Future<void> _updateLocalUserStatus() async {
    try {
      final user = await sl<AuthLocalDataSource>().getCachedUserData();
      if (user != null) {
        final updatedUser = user.copyWith(
          memberLevel: MemberLevel(
            level: user.memberLevel.level,
            levelStatus: 'inReview',
          ),
        );
        await sl<AuthLocalDataSource>().saveUserData(updatedUser);
        if (mounted) {
          context.read<HomeBloc>().add(const HomeProfileFetchingEvent());
        }
      }
    } catch (e) {
      debugPrint('Failed to update local user status: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Identity Verification'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: BlocConsumer<ComplyCubeBloc, ComplyCubeState>(
        listener: (context, state) {
          if (state is ComplyCubeIdentityCheckCreated) {
            // Update local user status
            _updateLocalUserStatus();

            // Show success message
            toastification.show(
              context: context,
              title: const Text('Success'),
              description: Text(
                state.message ??
                    'Identity verification completed successfully!',
              ),
              type: ToastificationType.success,
              autoCloseDuration: const Duration(seconds: 3),
            );

            // Navigate back
            context.push(AppRouteName.home);
          } else if (state is ComplyCubeVerificationSuccess) {
            // This is the intermediate success state - don't navigate yet
            // The identity check creation will happen automatically
            debugPrint('Verification processed, creating identity check...');
          } else if (state is ComplyCubeIdentityCheckFailed) {
            // Show error message for identity check failure
            toastification.show(
              context: context,
              title: const Text('Error'),
              description:
                  Text('Failed to complete identity check: ${state.message}'),
              type: ToastificationType.error,
              autoCloseDuration: const Duration(seconds: 5),
            );
          } else if (state is ComplyCubeVerificationFailed) {
            // Show error message
            toastification.show(
              context: context,
              title: const Text('Error'),
              description:
                  Text('Failed to process verification: ${state.message}'),
              type: ToastificationType.error,
              autoCloseDuration: const Duration(seconds: 5),
            );
          } else if (state is ComplyCubeVerificationCancelled) {
            // Show cancellation message
            toastification.show(
              context: context,
              title: const Text('Cancelled'),
              description: Text(
                'Verification cancelled: ${state.reason ?? 'User cancelled'}',
              ),
              type: ToastificationType.info,
              autoCloseDuration: const Duration(seconds: 3),
            );
            context.pop();
          } else if (state is ComplyCubeVerificationError) {
            // Show error message
            toastification.show(
              context: context,
              title: const Text('Verification Failed'),
              description: Text('Verification failed: ${state.errorMessage}'),
              type: ToastificationType.error,
              autoCloseDuration: const Duration(seconds: 5),
            );
          }
        },
        builder: (context, state) {
          return _buildBody(state);
        },
      ),
    );
  }

  Widget _buildBody(ComplyCubeState state) {
    if (state is ComplyCubeTokenGenerating) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Initializing verification...'),
          ],
        ),
      );
    }

    if (state is ComplyCubeVerificationProcessing) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Processing verification...'),
          ],
        ),
      );
    }

    if (state is ComplyCubeIdentityCheckCreating) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Creating identity check...'),
          ],
        ),
      );
    }

    if (state is ComplyCubeTokenGenerationFailed) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                state.message,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<ComplyCubeBloc>()
                    .add(const GenerateComplyCubeSdkTokenEvent());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state is ComplyCubeWidgetReady) {
      // Get the current theme colors
      final theme = Theme.of(context);
      final primaryColor = theme.primaryColor;

      // Convert color to hex string
      final colorValue =
          primaryColor.toARGB32().toRadixString(16).padLeft(8, '0');
      final primaryColorHex = '#${colorValue.substring(2).toUpperCase()}';

      return ComplyCubeWidget(
        settings: {
          'clientID': state.tokenResponse.clientId,
          'clientToken': state.tokenResponse.sdkToken,
          'stages': const [
            {
              'name': 'intro',
              'title': 'Connect ID verification',
              'message': 'We will now verify your identity.',
            },
            {
              'name': 'documentCapture',
              'showGuidance': true,
              'useMLAssistance': true,
              'retryLimit': 3,
              'liveCapture': true,
              'allowUpload': false,
              'captureMethod': 'camera',
              'documentTypes': {
                'passport': true,
                'driving_license': true,
                'national_identity_card': true,
              },
            },
            {
              'name': 'faceCapture',
              'mode': 'photo',
              'showGuidance': true,
              'retryLimit': 3,
              'liveCapture': true,
              'allowUpload': false,
              'captureMethod': 'camera',
            },
          ],
          'language': 'en',
          'liveCapture': true,
          'lookAndFeel': {
            'uiInterfaceStyle': 'light',
            'primaryButtonBgColor': primaryColorHex,
            'primaryButtonTextColor': '#FFFFFF',
            'secondaryButtonBgColor': '#FFFFFF',
            'secondaryButtonTextColor': primaryColorHex,
            'bodyTextColor': '#000000',
            'headingTextColor': '#000000',
            'subheadingTextColor': '#666666',
            'linkButtonTextColor': primaryColorHex,
          },
        },
        onSuccess: _onComplyCubeSuccess,
        onError: _onComplyCubeError,
        onCancelled: _onComplyCubeCancelled,
      );
    }

    return const Center(
      child: CustomConnectLoader(),
    );
  }
}
