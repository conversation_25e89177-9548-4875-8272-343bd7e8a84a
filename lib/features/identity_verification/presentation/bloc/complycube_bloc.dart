import 'package:cbrs/core/services/complycube_session_tracker.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/check_complycube_status_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/create_complycube_client_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/generate_complycube_sdk_token_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/params/identity_verification_params.dart'
    hide CreateComplyCubeIdentityCheckParams;
import 'package:cbrs/features/identity_verification/domain/usecases/process_complycube_verification_usecase.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/complycube_event.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/complycube_state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// BLoC for managing ComplyCube identity verification flow
class ComplyCubeBloc extends Bloc<ComplyCubeEvent, ComplyCubeState> {
  ComplyCubeBloc({
    required CreateComplyCubeClientUseCase createClientUseCase,
    required GenerateComplyCubeSdkTokenUseCase generateSdkTokenUseCase,
    required ProcessComplyCubeVerificationUseCase processVerificationUseCase,
    required CreateComplyCubeIdentityCheckUseCase createIdentityCheckUseCase,
    required CheckComplyCubeStatusUseCase checkComplyCubeStatusUseCase,
  })  : _createClientUseCase = createClientUseCase,
        _generateSdkTokenUseCase = generateSdkTokenUseCase,
        _processVerificationUseCase = processVerificationUseCase,
        _createIdentityCheckUseCase = createIdentityCheckUseCase,
        _checkComplyCubeStatusUseCase = checkComplyCubeStatusUseCase,
        super(const ComplyCubeInitial()) {
    on<GenerateComplyCubeSdkTokenEvent>(_onGenerateSdkToken);
    on<ProcessComplyCubeVerificationEvent>(_onProcessVerification);
    on<CreateComplyCubeIdentityCheckEvent>(_onCreateIdentityCheck);
    on<CheckComplyCubeStatusEvent>(_onCheckStatus);
    on<ResetComplyCubeStateEvent>(_onResetState);
    on<ComplyCubeVerificationCancelledEvent>(_onVerificationCancelled);
    on<ComplyCubeVerificationErrorEvent>(_onVerificationError);
  }

  final CreateComplyCubeClientUseCase _createClientUseCase;
  final GenerateComplyCubeSdkTokenUseCase _generateSdkTokenUseCase;
  final ProcessComplyCubeVerificationUseCase _processVerificationUseCase;
  final CreateComplyCubeIdentityCheckUseCase _createIdentityCheckUseCase;
  final CheckComplyCubeStatusUseCase _checkComplyCubeStatusUseCase;

  /// Handles SDK token generation
  Future<void> _onGenerateSdkToken(
    GenerateComplyCubeSdkTokenEvent event,
    Emitter<ComplyCubeState> emit,
  ) async {
    try {
      debugPrint(
          '\n=== ComplyCube BLoC - Starting Client Creation and Token Generation ===');

      // Step 1: Create ComplyCube client
      emit(const ComplyCubeClientCreating());

      final clientResult = await _createClientUseCase();

      await clientResult.fold(
        (failure) async {
          debugPrint('ComplyCube client creation failed: ${failure.message}');
          emit(ComplyCubeClientCreationFailed(
            message: failure.message,
            statusCode: failure.statusCode,
          ));
        },
        (clientResponse) async {
          debugPrint(
              'ComplyCube client created successfully: ${clientResponse.clientId}');
          emit(ComplyCubeClientCreated(clientResponse: clientResponse));

          // Step 2: Generate SDK token using the created client ID
          emit(const ComplyCubeTokenGenerating());

          final tokenParams = GenerateComplyCubeSdkTokenParams(
            clientID: clientResponse.clientId,
          );

          final tokenResult = await _generateSdkTokenUseCase(tokenParams);

          tokenResult.fold(
            (failure) {
              debugPrint('SDK token generation failed: ${failure.message}');
              emit(ComplyCubeTokenGenerationFailed(
                message: failure.message,
                statusCode: failure.statusCode,
              ));
            },
            (tokenResponse) {
              debugPrint('SDK token generated successfully');
              // Start ComplyCube session tracking
              ComplyCubeSessionTracker.instance.startComplyCubeSession();
              // Only emit widget ready state - remove intermediate state
              emit(ComplyCubeWidgetReady(tokenResponse: tokenResponse));
            },
          );
        },
      );
    } catch (e) {
      debugPrint('Unexpected error during ComplyCube initialization: $e');
      emit(ComplyCubeTokenGenerationFailed(
        message: 'Unexpected error: $e',
      ));
    }
  }

  /// Handles verification result processing
  Future<void> _onProcessVerification(
    ProcessComplyCubeVerificationEvent event,
    Emitter<ComplyCubeState> emit,
  ) async {
    try {
      debugPrint('\n=== ComplyCube BLoC - Processing Verification ===');
      debugPrint('Verification result: ${event.verificationResult}');

      emit(ComplyCubeVerificationProcessing(
        verificationResult: event.verificationResult,
      ));

      // Skip the old verification processing endpoint and go directly to identity check creation
      final clientId = event.verificationResult.clientId;
      final livePhotoId = event.verificationResult.livePhotoId;
      var documentId = event.verificationResult.documentId;

      // If documentId is null, try to get it from metadata.allIds.documentIds
      if (documentId == null && event.verificationResult.metadata != null) {
        final allIds = event.verificationResult.metadata!['allIds']
            as Map<String, dynamic>?;
        if (allIds != null) {
          final documentIds = allIds['documentIds'] as List<dynamic>?;
          if (documentIds != null && documentIds.isNotEmpty) {
            documentId = documentIds.first.toString();
            debugPrint('Extracted document ID from metadata: $documentId');
          }
        }
      }

      if (livePhotoId != null && documentId != null) {
        debugPrint('Creating identity check with IDs...');
        debugPrint('Client ID: $clientId');
        debugPrint('Live Photo ID: $livePhotoId');
        debugPrint('Document ID: $documentId');
        add(
          CreateComplyCubeIdentityCheckEvent(
            clientId: clientId,
            livePhotoId: livePhotoId,
            documentId: documentId,
          ),
        );
      } else {
        debugPrint('Missing required IDs for identity check creation');
        debugPrint('Live Photo ID: $livePhotoId');
        debugPrint('Document ID: $documentId');
        // End ComplyCube session tracking on failure
        ComplyCubeSessionTracker.instance.endComplyCubeSession();
        emit(
          ComplyCubeVerificationFailed(
            message: 'Missing required IDs for identity check creation',
            originalResult: event.verificationResult,
          ),
        );
      }
    } catch (e) {
      debugPrint('Unexpected error during verification processing: $e');
      emit(
        ComplyCubeVerificationFailed(
          message: 'Unexpected error: $e',
          originalResult: event.verificationResult,
        ),
      );
    }
  }

  /// Handles identity check creation
  Future<void> _onCreateIdentityCheck(
    CreateComplyCubeIdentityCheckEvent event,
    Emitter<ComplyCubeState> emit,
  ) async {
    try {
      debugPrint('\n=== ComplyCube BLoC - Creating Identity Check ===');
      debugPrint('Client ID: ${event.clientId}');
      debugPrint('Live Photo ID: ${event.livePhotoId}');
      debugPrint('Document ID: ${event.documentId}');

      emit(
        ComplyCubeIdentityCheckCreating(
          clientId: event.clientId,
          livePhotoId: event.livePhotoId,
          documentId: event.documentId,
        ),
      );

      final params = CreateComplyCubeIdentityCheckParams(
        clientId: event.clientId,
        livePhotoId: event.livePhotoId,
        documentId: event.documentId,
      );

      final result = await _createIdentityCheckUseCase(params);

      result.fold(
        (failure) {
          debugPrint('Identity check creation failed: ${failure.message}');
          emit(
            ComplyCubeIdentityCheckFailed(
              message: failure.message,
              statusCode: failure.statusCode,
            ),
          );
        },
        (checkResult) {
          debugPrint('Identity check created successfully: $checkResult');
          // Mark ComplyCube verification as completed
          ComplyCubeSessionTracker.instance
              .markComplyCubeVerificationCompleted();
          emit(
            ComplyCubeIdentityCheckCreated(
              checkResult: checkResult,
              message: 'Identity check completed successfully!',
            ),
          );
        },
      );
    } catch (e) {
      debugPrint('Unexpected error during identity check creation: $e');
      emit(
        ComplyCubeIdentityCheckFailed(
          message: 'Unexpected error: $e',
        ),
      );
    }
  }

  /// Handles state reset
  void _onResetState(
    ResetComplyCubeStateEvent event,
    Emitter<ComplyCubeState> emit,
  ) {
    debugPrint('\n=== ComplyCube BLoC - Resetting State ===');
    emit(const ComplyCubeInitial());
  }

  /// Handles verification cancellation
  void _onVerificationCancelled(
    ComplyCubeVerificationCancelledEvent event,
    Emitter<ComplyCubeState> emit,
  ) {
    debugPrint('\n=== ComplyCube BLoC - Verification Cancelled ===');
    debugPrint('Reason: ${event.reason}');
    // End ComplyCube session tracking
    ComplyCubeSessionTracker.instance.endComplyCubeSession();
    emit(ComplyCubeVerificationCancelled(reason: event.reason));
  }

  void _onVerificationError(
    ComplyCubeVerificationErrorEvent event,
    Emitter<ComplyCubeState> emit,
  ) {
    debugPrint('\n=== ComplyCube BLoC - Verification Error ===');
    debugPrint('Error: ${event.errorMessage}');
    debugPrint('Error Code: ${event.errorCode}');
    // End ComplyCube session tracking
    ComplyCubeSessionTracker.instance.endComplyCubeSession();
    emit(
      ComplyCubeVerificationError(
        errorMessage: event.errorMessage,
        errorCode: event.errorCode,
      ),
    );
  }

  /// Handles ComplyCube status checking
  Future<void> _onCheckStatus(
    CheckComplyCubeStatusEvent event,
    Emitter<ComplyCubeState> emit,
  ) async {
    try {
      debugPrint('\n=== ComplyCube BLoC - Checking Status ===');

      emit(const ComplyCubeStatusChecking());

      final result = await _checkComplyCubeStatusUseCase();

      result.fold(
        (failure) {
          debugPrint('ComplyCube status check failed: ${failure.message}');
          emit(ComplyCubeStatusFailed(
            message: failure.message,
            statusCode: failure.statusCode,
          ));
        },
        (statusData) {
          debugPrint('ComplyCube status check successful: $statusData');
          final userId = statusData['userId'] as String? ?? '';
          final complyCubeStatus =
              statusData['complyCubeStatus'] as String? ?? '';

          emit(ComplyCubeStatusLoaded(
            userId: userId,
            complyCubeStatus: complyCubeStatus,
          ));
        },
      );
    } catch (e) {
      debugPrint('Unexpected error during ComplyCube status check: $e');
      emit(ComplyCubeStatusFailed(
        message: 'Unexpected error: $e',
      ));
    }
  }

  @override
  void onChange(Change<ComplyCubeState> change) {
    super.onChange(change);
    // Reduced logging for iOS performance
    if (kDebugMode) {
      debugPrint(
        'ComplyCube: ${change.currentState.runtimeType} -> '
        '${change.nextState.runtimeType}',
      );
    }
  }

  @override
  void onTransition(Transition<ComplyCubeEvent, ComplyCubeState> transition) {
    super.onTransition(transition);
    if (kDebugMode) {
      debugPrint('ComplyCube Event: ${transition.event.runtimeType}');
    }
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    super.onError(error, stackTrace);
    debugPrint('\n=== ComplyCube BLoC Error ===');
    debugPrint('Error: $error');
    debugPrint('StackTrace: $stackTrace');
  }
}
