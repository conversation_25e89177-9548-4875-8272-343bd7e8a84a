import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_tabs.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_phone_field.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/money_request/domain/entities/member_lookup_entity.dart';
import 'package:cbrs/features/money_request/presentation/bloc/send_money_request_member_lookup/send_money_request_member_lookup_bloc.dart';
import 'package:cbrs/features/money_request/presentation/helpers/contact_helper.dart';
import 'package:cbrs/features/money_request/presentation/widgets/contact_selector.dart';
import 'package:cbrs/features/my_connect/applications/bloc/my_connect_bloc.dart';
import 'package:cbrs/features/my_connect/domain/entities/connection_request_entity.dart';
import 'package:cbrs/features/send_money/presentation/widgets/reciepent_card.dart';
import 'package:cbrs/features/transactions/presentation/views/quick%20wallet%20transfer/quick_pay_recipent_card.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:phone_form_field/phone_form_field.dart';

enum SendMoneyRequestTab { phone, email }

class SendMoneyRequestMemberLookupScreen extends StatefulWidget {
  const SendMoneyRequestMemberLookupScreen({
    super.key,
    this.fromChatData,
  });

  final Map<String, dynamic>? fromChatData;

  @override
  State<SendMoneyRequestMemberLookupScreen> createState() =>
      _SendMoneyRequestMemberLookupScreenState();
}

class _SendMoneyRequestMemberLookupScreenState
    extends State<SendMoneyRequestMemberLookupScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  bool _isValidEmail = false;
  bool _isValidPhone = false;
  bool _canLookUpMember = true;

  PhoneController phoneFieldController = PhoneController(
    initialValue: const PhoneNumber(
      isoCode: IsoCode.ET,
      nsn: '',
    ),
  );

  final FocusNode focusNode = FocusNode();

  // From chat variables
  bool get _isFromChat => widget.fromChatData?['fromChat'] == true;
  String? get _conversationId =>
      widget.fromChatData?['conversationId'] as String?;
  String? get _recipientId => widget.fromChatData?['recipientId'] as String?;
  String? get _recipientName =>
      widget.fromChatData?['recipientName'] as String?;

  // Variables for My Connect integration
  final Map<String, List<ConnectionRequestEntity>> groupedConnections = {};
  List<String> sortedKeys = [];
  bool _isLoadingConnections = false;
  String _connectSearchQuery = '';
  final TextEditingController _connectSearchController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateEmail);
    phoneFieldController.addListener(_validatePhone);
    _connectSearchController.addListener(_filterConnections);
    _fetchConnections(); // Fetch user connections

    // If coming from chat, automatically navigate to add money screen
    if (_isFromChat && _recipientId != null && _recipientName != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _navigateToAddMoneyScreen();
      });
    }
  }

  void _navigateToAddMoneyScreen() {
    // Create a member lookup entity from chat data
    final memberLookup = MemberLookupEntity(
      id: _recipientId!,
      avatar: '',
      lastName: _recipientName!.split(' ').last,
      firstName: _recipientName!.split(' ').first,
      middleName: '',
      phoneNumber: '',
      emailAddress: '',
    );

    context.pushNamed(
      AppRouteName.sendMoneyRequestAddMoneyScreen,
      extra: {
        'member': memberLookup,
        'fromChatData': widget.fromChatData,
      },
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _connectSearchController.dispose();
    phoneFieldController.removeListener(_validatePhone);
    phoneFieldController.dispose();

    super.dispose();
  }

  void _validateEmail() {
    if (!mounted) return;

    _isValidEmail = EmailValidator.validate(_emailController.text.trim());

    if (!_isValidEmail) {
      setState(() {});

      context.read<SendMoneyRequestMemberLookupBloc>().add(
            const RemoveMemberLookupEvent(),
          );
      return;
    }
    setState(() {});

    final contactSyncService = Get.find<ContactSyncService>();

    contactSyncService.filterWithEmail(_emailController.text);

    if (contactSyncService.searchContacts.isNotEmpty) {
      final contactData = contactSyncService.searchContacts.first;
      context.read<SendMoneyRequestMemberLookupBloc>().add(
            ContactSelectedEvent(
              id: contactData.id,
              avatar: contactData.avatar,
              lastName: contactData.lastName,
              emailAddress: contactData.email,
              firstName: contactData.firstName,
              middleName: contactData.middleName,
              phoneNumber: contactData.phoneNumber,
            ),
          );
    }
  }

  void _handleContactSelected(
    MemberLookupEntity memberInfo,
    String name,
    String phone,
    String avatar,
    bool showPreview,
  ) {
    setState(() {
      _canLookUpMember = false;
      if (phone.isNotEmpty) _isValidPhone = true;

      context.read<SendMoneyRequestMemberLookupBloc>().add(
            ContactSelectedEvent(
              id: memberInfo.id,
              avatar: memberInfo.avatar,
              lastName: memberInfo.lastName,
              emailAddress: memberInfo.emailAddress,
              firstName: memberInfo.firstName,
              middleName: memberInfo.middleName,
              phoneNumber: memberInfo.phoneNumber,
            ),
          );

      _canLookUpMember = true;
    });
  }

  void _validatePhone() {
    _isValidPhone = phoneFieldController.value.isValid();

    if (!_isValidPhone) {
      context.read<SendMoneyRequestMemberLookupBloc>().add(
            const RemoveMemberLookupEvent(),
          );
      return;
    }

    setState(() {});

    final countryCode = phoneFieldController.value.countryCode;
    final nsn = phoneFieldController.value.nsn;
    final fullPhoneNumber = '+$countryCode$nsn';

    if (_canLookUpMember) {
      if (ContactHelper.checkInContact(
        fullPhoneNumber,
        (memberInfo, name, phone, avatar, showPreview) {
          setState(() {
            _canLookUpMember = false;
            context.read<SendMoneyRequestMemberLookupBloc>().add(
                  ContactSelectedEvent(
                    id: memberInfo.id,
                    avatar: memberInfo.avatar,
                    lastName: memberInfo.lastName,
                    emailAddress: memberInfo.emailAddress,
                    firstName: memberInfo.firstName,
                    middleName: memberInfo.middleName,
                    phoneNumber: memberInfo.phoneNumber,
                  ),
                );
          });
        },
      )) {
        return;
      }
    }
  }

  String _selectedTab = 'Phone Number';
  final List<String> tabList = ['Phone Number', 'Email'];

  void onTap(String tabName) {
    if (tabName != _selectedTab) {
      setState(() {
        _selectedTab = tabName;
        _isValidEmail = false;
        _isValidPhone = false;
        if (_selectedTab == 'Email') {
          _emailController.clear();
        } else {
          phoneFieldController.value =
              const PhoneNumber(isoCode: IsoCode.ET, nsn: '');
        }
      });

      context.read<SendMoneyRequestMemberLookupBloc>().add(
            const RemoveMemberLookupEvent(),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SendMoneyRequestMemberLookupBloc,
        SendMoneyRequestState>(
      listener: (context, state) {
        if (state is SendMoneyRequestErrorState) {
          CustomToastification(context, message: state.message);
        }
      },
      buildWhen: (prev, curr) => prev != curr,
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Send Money Request '),
          ),
          body: SafeArea(
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Padding(
                padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const CustomPageHeader(
                              pageTitle: 'Send Money Request',
                              description:
                                  'Search for relatives or contacts to send'
                                  ' a money request.',
                            ),
                            SizedBox(height: 24.h),
                            CustomRoundedTabs(
                              onTap: onTap,
                              selectedTab: _selectedTab,
                              tabList: tabList,
                            ),
                            SizedBox(height: 16.h),
                            if (_selectedTab == 'Email')
                              _buildEmailForm
                            else
                              _buildPhoneForm,
                            if (state is MemberFetchedState) ...[
                              SizedBox(height: 16.h),
                              Container(
                                decoration: const BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x0F000000),
                                      blurRadius: 24,
                                    ),
                                  ],
                                ),
                                child: RecipientCard(
                                  isBirrTransfer:
                                      Get.find<GetAppThemeController>()
                                          .isBirrTheme
                                          .value,
                                  avatar: state.memberLookup.avatar,
                                  name: state.memberLookup.fullName,
                                  accountNumber: _selectedTab != 'Email'
                                      ? state.memberLookup.phoneNumber
                                      : state.memberLookup.emailAddress,
                                  borderColor: Theme.of(context)
                                      .primaryColor
                                      .withAlpha(76),
                                  onTap: () => context.pushNamed(
                                    AppRouteName.sendMoneyRequestAddMoneyScreen,
                                    extra: {
                                      'member': state.memberLookup,
                                      'fromChatData': widget.fromChatData,
                                    },
                                  ),
                                ),
                              ),
                            ],

                            // My Connects Section
                            _buildMyConnectsSection(),
                          ],
                        ),
                      ),
                    ),
                    CustomRoundedBtn(
                      btnText: 'Continue',
                      isLoading: state is MemberLookupLoadingState,
                      onTap: (_selectedTab == 'Email'
                              ? _isValidEmail
                              : _isValidPhone)
                          ? () {
                              FocusScope.of(context).unfocus();

                              if (state is MemberFetchedState) {
                                context.pushNamed(
                                  AppRouteName.sendMoneyRequestAddMoneyScreen,
                                  extra: {
                                    'member': state.memberLookup,
                                    'fromChatData': widget.fromChatData,
                                  },
                                );
                                return;
                              }

                              if (_selectedTab == 'Email') {
                                context
                                    .read<SendMoneyRequestMemberLookupBloc>()
                                    .add(
                                      MemberLookupEvent(
                                        emailAddress:
                                            _emailController.text.trim(),
                                      ),
                                    );
                              } else {
                                context
                                    .read<SendMoneyRequestMemberLookupBloc>()
                                    .add(
                                  MemberLookupEvent(
                                    phoneNumber: () {
                                      final code = phoneFieldController
                                          .value.countryCode;
                                      final nsn =
                                          phoneFieldController.value.nsn;
                                      return '+$code$nsn';
                                    }(),
                                  ),
                                );
                              }
                            }
                          : null,
                      isBtnActive: (_selectedTab == 'Email'
                          ? _isValidEmail
                          : _isValidPhone),
                    ),
                    SizedBox(
                      height: Platform.isIOS ? 24.h : 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget get _buildEmailForm => CustomTextInput(
        hintText: 'Enter Email Address',
        inputLabel: 'Email Address',
        controller: _emailController,
        keyboardType: TextInputType.emailAddress,
      );

  Widget get _buildPhoneForm => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Phone Number',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: TransactionPhoneField(
                    controller: phoneFieldController,
                    focusNode: FocusNode(),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              ContactSelector(
                phoneController: phoneFieldController,
                onContactSelected: _handleContactSelected,
              ),
            ],
          ),
        ],
      );

  // Method to build the My Connects section
  Widget _buildMyConnectsSection() {
    return BlocConsumer<MyConnectBloc, MyConnectState>(
      listener: (context, state) {
        // Handle new CombinedConnectionsState
        if (state is CombinedConnectionsState) {
          final acceptedData = state.acceptedData;
          if (acceptedData != null) {
            setState(() {
              _isLoadingConnections = acceptedData.isLoading;
            });
            if (acceptedData.connections != null) {
              _groupConnections(acceptedData.connections!);
            }
          }
        }
        // Handle legacy ConnectionsLoadedState for backward compatibility
        else if (state is ConnectionsLoadedState) {
          setState(() {
            _isLoadingConnections = false;
          });
          _groupConnections(state.connections);
        }
      },
      builder: (context, state) {
        // Check for loading state in both new and legacy formats
        bool isLoading = false;
        if (state is CombinedConnectionsState) {
          isLoading = state.acceptedData?.isLoading == true;
        } else if (state is MyConnectLoadingState) {
          isLoading = true;
        }

        if (isLoading && groupedConnections.isEmpty) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 24.h),
              const CustomBuildText(
                text: 'My Connects List',
                color: Color(0xFFAAAAAA),
                fontSize: 13,
              ),
              const SizedBox(height: 8),
              const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 20),
                  child: CircularProgressIndicator(),
                ),
              ),
            ],
          );
        }

        // Check for error states
        String? errorMessage;
        if (state is CombinedConnectionsState) {
          errorMessage = state.acceptedData?.error;
        } else if (state is MyConnectErrorState) {
          errorMessage = state.message;
        }

        if (errorMessage != null) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 24.h),
              const CustomBuildText(
                text: 'My Connects List',
                color: Color(0xFFAAAAAA),
                fontSize: 13,
              ),
              const SizedBox(height: 8),
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Error loading connections: $errorMessage',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _fetchConnections,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        }

        // Check if we have connections to display
        bool hasConnections = false;
        if (state is CombinedConnectionsState) {
          hasConnections = state.acceptedData?.connections?.isNotEmpty == true;
        } else if (state is ConnectionsLoadedState) {
          hasConnections = state.connections.isNotEmpty;
        }

        if (hasConnections && sortedKeys.isNotEmpty) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 24.h),
              const CustomBuildText(
                text: 'My Connects List',
                color: Color(0xFFAAAAAA),
                fontSize: 13,
              ),
              const SizedBox(height: 16),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: sortedKeys.length,
                itemBuilder: (context, sectionIndex) {
                  final letter = sortedKeys[sectionIndex];
                  final connections = groupedConnections[letter]!;

                  return Column(
                    children: connections.map((connection) {
                      return FutureBuilder<Map<String, String>>(
                        future: _getConnectedPersonInfo(connection),
                        builder: (context, snapshot) {
                          if (!snapshot.hasData) {
                            return const SizedBox(
                              height: 80,
                              child: Center(
                                child: CircularProgressIndicator(),
                              ),
                            );
                          }

                          final connectedPerson = snapshot.data!;
                          final name = connectedPerson['name'] ?? '';
                          final phoneNumber = connectedPerson['phone'] ?? '';
                          final avatar = connectedPerson['avatar'] ?? '';

                          return InkWell(
                            onTap: () => _navigateToSendMoneyRequestAddMoney(
                              connectedPerson,
                            ),
                            child: Container(
                              margin: const EdgeInsets.symmetric(vertical: 4),
                              child: QuickPayRecipentCard(
                                recipientName: name,
                                recipientPhone: phoneNumber,
                                recipientAvatar: avatar,
                              ),
                            ),
                          );
                        },
                      );
                    }).toList(),
                  );
                },
              ),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  // Add method to fetch connections
  void _fetchConnections() {
    setState(() {
      _isLoadingConnections = true;
    });
    context.read<MyConnectBloc>().add(
          const FetchConnectionsEvent(
            status: 'accepted',
          ),
        );
  }

  // Method to group connections alphabetically
  void _groupConnections(List<ConnectionRequestEntity> connections) {
    groupedConnections.clear();
    sortedKeys.clear();

    // Process all connections synchronously
    for (final connection in connections) {
      // Get the name synchronously by determining which person is the connected user
      var name = '';

      // Use a simple synchronous approach to get the name
      try {
        // Assume we can get current user ID synchronously or use a cached value
        // For now, we'll use the recipient's name as default, but this should be improved
        if (connection.recipient.fullName.isNotEmpty == true) {
          name = connection.recipient.fullName;
        } else if (connection.requester.firstName?.isNotEmpty == true) {
          name =
              '${connection.requester.firstName ?? ''} ${connection.requester.lastName ?? ''}'
                  .trim();
        }
      } catch (e) {
        // Fallback to requester name
        name =
            '${connection.requester.firstName ?? ''} ${connection.requester.lastName ?? ''}'
                .trim();
      }

      if (name.isNotEmpty) {
        final letter = name[0].toUpperCase();
        groupedConnections.putIfAbsent(letter, () => []).add(connection);
      }
    }

    // Sort connections within each letter group alphabetically by name
    for (final letter in groupedConnections.keys) {
      groupedConnections[letter]!.sort((a, b) {
        var nameA = '';
        var nameB = '';

        // Get names for comparison
        if (a.recipient.fullName.isNotEmpty == true) {
          nameA = a.recipient.fullName;
        } else {
          nameA = '${a.requester.firstName ?? ''} ${a.requester.lastName ?? ''}'
              .trim();
        }

        if (b.recipient.fullName.isNotEmpty == true) {
          nameB = b.recipient.fullName;
        } else {
          nameB = '${b.requester.firstName ?? ''} ${b.requester.lastName ?? ''}'
              .trim();
        }

        return nameA.toLowerCase().compareTo(nameB.toLowerCase());
      });
    }

    // Sort the letter keys A-Z
    sortedKeys = groupedConnections.keys.toList()..sort();
  }

  // Method to filter connections based on search query
  void _filterConnections() {
    setState(() {
      _connectSearchQuery = _connectSearchController.text;
    });

    // Re-fetch and re-group connections with the new filter
    context.read<MyConnectBloc>().add(
          const FetchConnectionsEvent(
            status: 'accepted',
          ),
        );
  }

  // Helper method to get the connected person info (not the current user)
  Future<Map<String, String>> _getConnectedPersonInfo(
    ConnectionRequestEntity connection,
  ) async {
    try {
      final currentUserId = await sl<AuthLocalDataSource>().getUserId();

      // Determine which person is the connected user (not current user)
      if (currentUserId == connection.requester.id) {
        // Current user is the requester, so show recipient
        return {
          'name': connection.recipient.fullName ?? '',
          'phone': connection.recipient.phoneNumber ?? '',
          'avatar': connection.recipient.avatar ??
              connection.recipient.firstName ??
              '',
          'id': connection.recipient.id ?? '',
        };
      } else {
        // Current user is the recipient, so show requester
        return {
          'name':
              '${connection.requester.firstName ?? ''} ${connection.requester.lastName ?? ''}'
                  .trim(),
          'phone': connection.requester.phoneNumber ?? '',
          'avatar': connection.requester.avatar ??
              connection.requester.firstName ??
              '',
          'id': connection.requester.id ?? '',
        };
      }
    } catch (e) {
      // Fallback to recipient if there's an error
      return {
        'name': connection.recipient.fullName ?? '',
        'phone': connection.recipient.phoneNumber ?? '',
        'avatar':
            connection.recipient.avatar ?? connection.recipient.firstName ?? '',
        'id': connection.recipient.id ?? '',
      };
    }
  }

  // Method to navigate to send money request add money page
  void _navigateToSendMoneyRequestAddMoney(
    Map<String, String> connectedPerson,
  ) {
    // Create a member lookup entity from connected person data
    final memberLookup = MemberLookupEntity(
      id: connectedPerson['id'] ?? '',
      avatar: connectedPerson['avatar'] ?? '',
      lastName: connectedPerson['name']?.split(' ').last ?? '',
      firstName: connectedPerson['name']?.split(' ').first ?? '',
      middleName: '',
      phoneNumber: connectedPerson['phone'] ?? '',
      emailAddress: '',
    );

    context.pushNamed(
      AppRouteName.sendMoneyRequestAddMoneyScreen,
      extra: {
        'member': memberLookup,
        'fromChatData': widget.fromChatData,
      },
    );
  }
}
