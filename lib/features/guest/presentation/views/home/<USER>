import 'dart:async';

import 'package:cbrs/features/exchange_rate/presentation/widgets/exchange_rate.dart';
import 'package:cbrs/features/gift_packages/domain/entities/banner_item.dart';
import 'package:cbrs/features/gift_packages/domain/repositories/gift_package_repository.dart';
import 'package:cbrs/features/gift_packages/presentation/widgets/gift_banners_carousal.dart';
import 'package:cbrs/features/guest/presentation/widget/bank_accounts_section.dart';
import 'package:cbrs/features/guest/presentation/widget/financial_services_section.dart';
import 'package:cbrs/features/guest/presentation/widget/guest_additional_services.dart';
import 'package:cbrs/features/guest/presentation/widget/mini_apps_section.dart';
import 'package:cbrs/features/guest/presentation/widget/payment_options.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class GuestHomeScreen extends StatefulWidget {
  const GuestHomeScreen({super.key, this.isGuest = true});
  final bool isGuest;

  @override
  State<GuestHomeScreen> createState() => _GuestHomeScreenState();
}

class _GuestHomeScreenState extends State<GuestHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  List<BannerItem> _bannerItems = [];
  String selectedWallet = 'ETB';
  bool isAmountHidden = false;

  late ScrollController _scrollController;

  double _exchangeRate = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _initializeData();
  }

  Future<void> _initializeData() async {
    // await _initializeEssentialData();
    await _fetchExchangeRate();
    await _fetchBanners();
  }

  Future<void> _initializeEssentialData() async {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: const Color(0xFFF9F9F9),
          extendBodyBehindAppBar: true,
          body: RefreshIndicator(
            onRefresh: () async {},
            child: LayoutBuilder(
              builder: (context, constraints) {
                return CustomScrollView(
                  controller: _scrollController,
                  physics: const ClampingScrollPhysics(),
                  slivers: [
                    const SliverToBoxAdapter(
                      child: BankAccountsSection(
                        userName: 'Hi There!',
                        avatarUrl: 'assets/icons/Profile.png',
                      ),
                    ),
                    _buildContentSections(),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildContentSections() {
    return SliverList(
      delegate: SliverChildListDelegate([
        SizedBox(
          height: 28.h,
        ),
        BlocBuilder<HomeBloc, HomeState>(
          builder: (context, state) {
            return Column(
              children: [
                const PaymentOptions(),
                SizedBox(height: 32.h),
                if (_bannerItems.isNotEmpty)
                  GiftBannersCarousal(
                    imageUrls: _bannerItems,
                    isAutoPlay: true,
                    bannerHeight: 100.h,
                    dotsPosition: 2.h,
                    hasDots: true,
                    dotsBackgroundColor: Colors.transparent,
                    activeDotColor: Theme.of(context).primaryColor,
                    inActiveDotColor: Theme.of(context).secondaryHeaderColor,
                    isGuestMode: true,
                  ),
                SizedBox(height: 26.h),
                const FinancialServicesSection(),
                SizedBox(height: 30.h),
                const GuestAdditionalServices(),
                // SizedBox(height: 30.h),
                // const MiniAppsSection(),
                SizedBox(height: 30.h),
                const ExchangeRateWidget(),
                SizedBox(height: 30.h),
              ],
            );
          },
        ),
      ]),
    );
  }

  Future<void> _fetchBanners() async {
    try {
      final response = await context
          .read<GiftPackageRepository>()
          .getBanners(status: 'ongoing');

      if (mounted) {
        setState(() {
          _bannerItems = response.data;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _bannerItems = [];
        });
      }
    }
  }

  Future<void> _fetchExchangeRate() async {
    try {
      context.read<HomeBloc>().add(FetchExchangeRate());

      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted) {
        final currentState = context.read<HomeBloc>().state;
        if (currentState is HomeLoaded) {
          setState(() {
            _exchangeRate = currentState.data.exchangeRate;
          });
        }
      }
    } catch (e) {
      debugPrint('Error fetching exchange rate: $e');
    }
  }
}
