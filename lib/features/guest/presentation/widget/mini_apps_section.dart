import 'package:cbrs/core/common/widgets/build_view_all.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/guest/presentation/widget/show_guest_mode_bottom_sheet.dart';
import 'package:cbrs/features/home/<USER>/widgets/home_section_headers.dart';
import 'package:cbrs/features/mini_apps/application/bloc/miniapp_bloc.dart';
import 'package:cbrs/features/mini_apps/domain/entities/miniapp.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class MiniAppsSection extends StatelessWidget {
  const MiniAppsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<MiniappBloc>(),
      child: const _MiniAppsSectionContent(),
    );
  }
}

class _MiniAppsSectionContent extends StatefulWidget {
  const _MiniAppsSectionContent();

  @override
  State<_MiniAppsSectionContent> createState() =>
      _MiniAppsSectionContentState();
}

class _MiniAppsSectionContentState extends State<_MiniAppsSectionContent> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  bool _hasMoreData = true;
  List<MiniappDataEntity> _miniapps = [];

  @override
  void initState() {
    super.initState();
    // _loadMiniApps();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadMiniApps() {
    context.read<MiniappBloc>().add(
          GettingMiniappEvent(
            page: _currentPage,
            perPage: 8,
            stage: 'UAT',
          ),
        );
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
            _scrollController.position.maxScrollExtent &&
        _hasMoreData) {
      _currentPage++;
      _loadMiniApps();
    }
  }

  List<MiniappDataEntity> _filterMiniApps(List<MiniappDataEntity> apps) {
    return apps.where((app) => app.merchantType == 'MiniApp').toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const HomeSectionHeaders(
            title: 'Mini Apps',
            description:
                'All essential apps in one place—pay for all with Connect.',
          ),
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 16.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              children: [
                BlocConsumer<MiniappBloc, MiniappState>(
                  listener: (context, state) {
                    if (state is GetLoadedMiniappsState) {
                      final filteredApps =
                          _filterMiniApps(state.miniapp.miniappData);
                      setState(() {
                        if (_currentPage == 1) {
                          _miniapps = filteredApps;
                        } else {
                          _miniapps.addAll(filteredApps);
                        }
                        _hasMoreData =
                            state.miniapp.miniappPaginate?.currentPage !=
                                state.miniapp.miniappPaginate?.totalPages;
                      });
                    }
                  },
                  builder: (context, state) {
                    if (state is MiniappLoadingState && _currentPage == 1) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (state is MiniappErrorState && _miniapps.isEmpty) {
                      return Center(
                        child: Text(state.message),
                      );
                    }

                    return Column(
                      children: [
                        GridView.builder(
                          shrinkWrap: true,
                          controller: _scrollController,
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 13.h,
                          ),
                          physics: const AlwaysScrollableScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            mainAxisSpacing: 16.h,
                            crossAxisSpacing: 12.w,
                            mainAxisExtent: 80.h,
                          ),
                          itemCount: _miniapps.length +
                              (state is MiniappLoadingState ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index == _miniapps.length) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }
                            return _buildMiniAppCard(
                              label: _miniapps[index].miniAppName,
                              imageUrl: _miniapps[index].miniAppIcon,
                              url: _miniapps[index].url,
                              context: context,
                            );
                          },
                        ),
                        if (state is MiniappLoadingState && _currentPage > 1)
                          Padding(
                            padding: EdgeInsets.all(8.h),
                            child: const CircularProgressIndicator(),
                          ),
                      ],
                    );
                  },
                ),
                SizedBox(height: 12.h),
                BuildViewAll(
                  onTap: () {
                    showGuestModeBottomSheet(context);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiniAppCard({
    required String label,
    required String imageUrl,
    required String url,
    required BuildContext context,
  }) {
    return GestureDetector(
      onTap: () => showGuestModeBottomSheet(context),
      child: SizedBox(
        width: 80.w,
        child: Column(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: imageUrl.startsWith('http')
                  ? Image.network(
                      imageUrl,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.apps,
                          size: 24.w,
                          color: Colors.grey,
                        );
                      },
                    )
                  : Image.asset(
                      imageUrl,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.apps,
                          size: 24.w,
                          color: Colors.grey,
                        );
                      },
                    ),
            ),
            SizedBox(height: 6.h),
            Flexible(
              child: Text(
                label,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFF1A1A1A),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
