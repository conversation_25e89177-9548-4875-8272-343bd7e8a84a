import 'dart:io';

import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/features/profile/data/models/email_add_response_model.dart';
import 'package:cbrs/features/profile/data/models/pin_response_model%20copy.dart';
import 'package:cbrs/features/profile/data/models/profile_dto.dart';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';

abstract class ProfileRemoteDataSource {
  Future<PinResponseModel> updatePin({
    required String deviceUUID,
    required String oldPin,
    required String newPin,
  });

  Future<EmailAddResponseModel> addEmail({required String email});
  Future<EmailAddResponseModel> addPhone({required String phoneNumber});
  Future<EmailAddResponseModel> verifyPhoneOtp({required String otp});

  Future<EmailAddResponseModel> updateProfile({
    required String id,
    required String name,
    required String country,
    required String city,
    required String firstName,
    required String middleName,
    required String lastName,
    required String birthDate,
  });

  Future<void> verifyEmailOtp({required String email, required String otp});
  Future<EmailAddResponseModel> resendPhoneEmailOtp({
    required String phoneNumber,
    required String email,
  });
  Future<void> resendEmailVerification({required String email});
  Future<EmailAddResponseModel> deleteProfile();

  Future<ProfileModel> getUserData();
  Future<String> updateAvatar({required File avatar});
  Future<void> deleteAvatar();
}

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  ProfileRemoteDataSourceImpl(this._apiService);
  final ApiService _apiService;
  String? token;

  @override
  Future<EmailAddResponseModel> addEmail({required String email}) async {
    try {
      final result = await _apiService.post(
       ApiEndpoints.verifyEmailOrPhone,
        data: {'email': email, 'type': 'email'},
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        (data) {
          return EmailAddResponseModel.fromJson(data);
        },
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<EmailAddResponseModel> verifyPhoneOtp({required String otp}) async {
    try {
      final result = await _apiService.post(
            ApiEndpoints.verifyOtp,

        data: {'OTPCode': int.parse(otp)},
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        (data) {
          return EmailAddResponseModel.fromJson(data);
        },
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<EmailAddResponseModel> addPhone({required String phoneNumber}) async {
    try {
      final result = await _apiService.post(
              ApiEndpoints.verifyEmailOrPhone,

        data: {'phoneNumber': phoneNumber, 'type': 'phoneNumber'},
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        (data) {
          return EmailAddResponseModel.fromJson(data);
        },
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 400,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<void> verifyEmailOtp({
    required String email,
    required String otp,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.verifyEmail,
        data: {
          'email': email,
          'OTPCode': otp,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      result.fold(
        (data) => null,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<EmailAddResponseModel> resendPhoneEmailOtp({
    required String phoneNumber,
    required String email,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.resendPhoneOtp,
        data: phoneNumber.isNotEmpty
            ? {'phoneNumber': phoneNumber}
            : {'email': email},
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        EmailAddResponseModel.fromJson,
        (error) => EmailAddResponseModel(
          success: false,
          message: error.message,
          token: '',
          otp: '',
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<void> resendEmailVerification({required String email}) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.resendEmailVerification,
        data: {'email': email, 'type': 'email'},
        parser: (data) => data as Map<String, dynamic>,
      );

      result.fold(
        (data) => null,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ProfileModel> getUserData() async {
    throw Exception('Not implemented');
  }

  @override
  Future<EmailAddResponseModel> updateProfile({
    required String id,
    required String name,
    required String firstName,
    required String middleName,
    required String lastName,
    required String birthDate,
    required String country,
    required String city,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.updateProfile,
        data: {
          'name': name,
          'country': country,
          'city': city,
          'firstName': firstName,
          'middleName': middleName,
          'lastName': lastName,
          'birthDate': birthDate,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        EmailAddResponseModel.fromJson,
        (error) => const EmailAddResponseModel(
          success: false,
          message: 'Please try again later',
          token: '',
          otp: '',
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<PinResponseModel> updatePin({
    required String deviceUUID,
    required String oldPin,
    required String newPin,
  }) async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.updatePin,
        data: {
          'device_uuid': deviceUUID,
          'old_pin': oldPin,
          'new_pin': newPin,
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        PinResponseModel.fromJson,
        (error) => PinResponseModel(
          message: 'Error in changing pin: ${error.message}',
          success: false,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<EmailAddResponseModel> deleteProfile() async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.deletePicture,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        EmailAddResponseModel.fromJson,
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<String> updateAvatar({required File avatar}) async {
    try {
      final profilePic = await MultipartFile.fromFile(
        avatar.path,
        filename: avatar.path.split('/').last,
        contentType: DioMediaType('image', 'jpeg'),
      );

      // debugPrint("form daata ${formData.fields.first} ");
      debugPrint('avatar.path ${avatar.path}');

      final files = <String, MultipartFile>{
        'profilepic': profilePic,
      };

      final result = await _apiService.put(
        ApiEndpoints.uploadPicture,
        files: files,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        (data) {
          debugPrint('respisne $data');

          final returnedData = data;

          return returnedData['image'] as String;
        },
        //EmailAddResponseModel.fromJson(data),
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }

  @override
  Future<void> deleteAvatar() async {
    try {
      final result = await _apiService.post(
        ApiEndpoints.deletePicture,
        parser: (data) => data as Map<String, dynamic>,
      );

      return result.fold(
        (data) => true,
        //EmailAddResponseModel.fromJson(data),
        (error) => throw ApiException(
          message: error.message,
          statusCode: error.statusCode ?? 500,
        ),
      );
    } on ApiException {
      rethrow;
    } catch (e) {
      throw const ApiException(
        message: 'Something went wrong. please contact customer support.',
        statusCode: 500,
      );
    }
  }
}
