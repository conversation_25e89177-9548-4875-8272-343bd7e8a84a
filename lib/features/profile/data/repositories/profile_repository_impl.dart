import 'dart:io';

import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/profile/data/datasources/profile_local_datasource.dart';
import 'package:cbrs/features/profile/data/models/profile_dto.dart';
import 'package:cbrs/features/profile/domain/entities/email_add_response.dart';
import 'package:cbrs/features/profile/domain/entities/pin_response.dart';
import 'package:cbrs/features/profile/domain/repositories/profile_repositories.dart';
import 'package:cbrs/features/profile/data/datasources/profile_remote_datasource.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  const ProfileRepositoryImpl({
    required ProfileRemoteDataSource remoteDataSource,
    required ProfileLocalDataSource localDataSource,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource;

  final ProfileRemoteDataSource _remoteDataSource;
  final ProfileLocalDataSource _localDataSource;

  @override
  ResultFuture<EmailAddResponse> addEmail({required String email}) async {
    try {
      final returnData = await _remoteDataSource.addEmail(email: email);
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<EmailAddResponse> addPhone({required String phoneNumber}) async {
    try {
      final returnData =
          await _remoteDataSource.addPhone(phoneNumber: phoneNumber);
      return Right(returnData);
    }on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<ProfileModel> getUserData() async {
    try {
      debugPrint('Called from data/repository');
      final data = await _localDataSource.getCachedUserProfile();
      // throw Exception('error here ${data}');
      return Right(data!);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultVoid resendEmailVerification({required String email}) async {
    try {
      await _remoteDataSource.resendEmailVerification(email: email);
      return const Right(null);
    }on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultVoid resendPhoneOtp({required String phoneNumber}) async {
    try {
      await _remoteDataSource.addPhone(phoneNumber: phoneNumber);
      return const Right(null);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultVoid verifyEmailOtp({
    required String email,
    required String otp,
  }) async {
    try {
      await _remoteDataSource.verifyEmailOtp(email: email, otp: otp);
      return const Right(null);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<EmailAddResponse> verifyPhoneOtp({required String otp}) async {
    try {
      final returnData = await _remoteDataSource.verifyPhoneOtp(otp: otp);
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<EmailAddResponse> updateProfile({
    required String id,
    required String name,
    required String firstName,
    required String lastName,
    required String middleName,
    required String birthDate,
    required String country,
    required String city,
  }) async {
    try {
      final returnData = await _remoteDataSource.updateProfile(
        id: id,
        name: name,
        country: country,
        city: city,
        firstName: firstName,
        lastName: lastName,
        middleName: middleName,
        birthDate: birthDate,
      );
      return Right(returnData);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<PinResponse> updatePin({
    required String deviceUUID,
    required String oldPin,
    required String newPin,
  }) async {
    try {
      final returnData = await _remoteDataSource.updatePin(
        deviceUUID: deviceUUID,
        oldPin: oldPin,
        newPin: newPin,
      );
      return Right(returnData);
    }on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultVoid saveCaches(ProfileModel profile) async {
    try {
      await _localDataSource.cacheUserData(profile);
      return const Right(null);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
    // TODO: implement saveCaches
    throw UnimplementedError();
  }

  @override
  ResultFuture<EmailAddResponse> resendPhoneEmailOtp({
    required String phoneNumber,
    required String email,
  }) async {
    try {
      final returnData = await _remoteDataSource.resendPhoneEmailOtp(
        phoneNumber: phoneNumber,
        email: email,
      );
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<EmailAddResponse> deleteProfile() async {
    try {
      final returnData = await _remoteDataSource.deleteProfile();
      return Right(returnData);
    } on ServerException catch (e) {
      return Left(ServerFailure.fromException(e));
    }
  }

  @override
  ResultFuture<String> updateAvatar({required File avatar}) async {
    try {
      final returnData = await _remoteDataSource.updateAvatar(avatar: avatar);
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<void> deleteAvatar() async {
    try {
      final returnData = await _remoteDataSource.deleteAvatar();
      return Right(returnData);
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }
}
