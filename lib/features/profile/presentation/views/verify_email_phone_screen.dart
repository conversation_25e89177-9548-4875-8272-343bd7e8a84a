import 'dart:async';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/profile/data/models/profile_dto.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_event.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VerifyEmailPhoneScreen extends StatefulWidget {
  const VerifyEmailPhoneScreen({
    super.key,
    this.email = '',
    this.phone = '',
    this.isPhoneVerify = false,
  });
  final String email;
  final String phone;

  final bool isPhoneVerify;

  @override
  State<VerifyEmailPhoneScreen> createState() => _VerifyEmailPhoneScreenState();
}

class _VerifyEmailPhoneScreenState extends State<VerifyEmailPhoneScreen> {
  final _pinController = TextEditingController();
  bool _canResend = false;
  late StreamController<int> _timerController;
  StreamSubscription<int>? _timerSubscription;
  bool _isTimerActive = false;
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // _pinController.text = widget.otp;
    //  = TextEditingController(text: widget.otp);
    _timerController = StreamController<int>.broadcast();
    _startTimer();
  }

  void _startTimer() {
    if (_isTimerActive) return;
    _isTimerActive = true;
    _timerSubscription?.cancel();
    _timerSubscription = Stream.periodic(
      const Duration(seconds: 1),
      (count) => 60 - count,
    ).take(61).listen((time) {
      _timerController.add(time);
      if (time == 0) {
        setState(() {
          _canResend = true;
          _isTimerActive = false;
        });
      }
    });
  }

  Future<void> _verifyPhoneEmail() async {
    final otp = _pinController.text;
    if (otp.length != 6) {
      CustomToastification(
        context,
        message: 'Please enter a valid 6-digit code',
      );

      return;
    }

    context
        .read<ProfileBloc>()
        .add(VerifyPhoneEmailOtpEvent(_pinController.text));
  }

  void _resendCode() {
    debugPrint('Resend COde clicked :: ');

    if (!_canResend) return;
    _pinController.clear();
    setState(() {
      _canResend = false;
    });
    _startTimer();

    debugPrint('Resend COde --waiting :: ');
    debugPrint('true ${widget.isPhoneVerify}');
    // return;
    if (widget.isPhoneVerify) {
      debugPrint('true ${widget.phone}');
      // return;
      context
          .read<ProfileBloc>()
          .add(ResendPhoneEmailOtpEvent(phoneNumber: widget.phone, email: ''));
    } else {
      context
          .read<ProfileBloc>()
          .add(ResendPhoneEmailOtpEvent(phoneNumber: '', email: widget.email));
    }
  }

  @override
  void dispose() {
    _pinController.dispose();
    _timerSubscription?.cancel();
    _timerController.close();
    super.dispose();
  }

  Future<void> updateLocalAvatar() async {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();

    if (user != null) {
      debugPrint('unupdated avatar ${user.avatar}');
      if (widget.isPhoneVerify) {
        final updatedUser =
            user.copyWith(phoneNumber: widget.phone, isPhoneVerified: true);

        debugPrint('Updated avatar ${updatedUser.avatar}');
        await sl<AuthLocalDataSource>().saveUserData(updatedUser);

        final newUser = await sl<AuthLocalDataSource>().getCachedUserData();
        debugPrint('✅ Updated local imageUrl: ${newUser?.avatar}');
      } else {
        final updatedUser =
            user.copyWith(email: widget.email, isEmailVerified: true);

        debugPrint('Updated avatar ${updatedUser.avatar}');
        await sl<AuthLocalDataSource>().saveUserData(updatedUser);

        final newUser = await sl<AuthLocalDataSource>().getCachedUserData();
        debugPrint('✅ Updated local imageUrl: ${newUser?.avatar}');
      }
      // context.read<HomeBloc>().add(
      //       LoadEssentialDataEvent(
      //         walletType: GlobalVariable.currentlySelectedWallet,
      //       ),
      //     );
    }

    setState(() {});
    context.pop(true);
    context.pop(true);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocConsumer<ProfileBloc, ProfileState>(
      listener: (context, state) {
        if (state is ProfileLoading) {
          // circular progress indicator should be shown
        }
        if (state is PhoneEmailVerificationResent) {
          CustomToastification(
            context,
            message: state.response.message,
            isError: state.response.success,
          );
        } else if (state is PhoneAddError) {
          CustomToastification(
            context,
            message: state.message,
          );
        } else if (state is ProfileLoaded) {
          // navigate back to profile screeb
          debugPrint('profile loaded state.');
          context.pop(true);
          context.pop(true);
        } else if (state is EmailVerificationSentState) {
          CustomToastification(
            context,
            message: 'New verification code sent to your email',
          );
        } else if (state is PhoneAddError) {
          debugPrint('PhoneAddError()');
        } else if (state is PhoneVerifyState) {
          debugPrint('Phone Verified ${state.response.message}');
          debugPrint(' 🍞Props data ${widget.phone} 🦰');

          debugPrint('state.response.success😉😉 ${state.response.success}');

          if (state.response.success) {
            CustomToastification(
              context,
              message: state.response.message,
              isError: !state.response.success,
            );
            // _showCustomSnack(state.response.message);

            updateLocalAvatar();
          } else {
            _pinController.clear();
            CustomToastification(
              context,
              message: state.response.message,
              isError: !state.response.success,
            );

            // _showCustomSnack(state.response.message);
          }
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.isPhoneVerify
                  ? 'Phone Verification'
                  : 'Email Verification',
            ),
          ),
          body: SafeArea(
            bottom: false,
            child: GestureDetector(
              onTap: () {
                debugPrint('Keyboard hide');
                FocusScope.of(context).unfocus(); // Hide the keyboard
              },
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(
                        vertical: 16.h,
                        horizontal: 16.w,
                      ),
                      child: Column(
                        children: [
                          CustomPageHeader(
                            pageTitle: widget.isPhoneVerify
                                ? 'Phone Verification'
                                : 'Email Verification',
                            description: widget.isPhoneVerify
                                ? "We've sent a verification code to your phone. Please enter the code to confirm your phone."
                                : "We've sent a verification code to your email. Please enter the code to confirm your email address.",
                          ),
                          SizedBox(height: 40.h),
                          _buildPinInput(theme),
                          SizedBox(height: 34.h),
                          _buildResendButton(theme),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: 16.w,
                      right: 16.w,
                      bottom: 20,
                    ),
                    child: CustomRoundedBtn(
                      btnText: 'Verify',
                      isLoading: state is ProfileLoading,
                      onTap: _verifyPhoneEmail,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPinInput(ThemeData theme) {
    final screenSize = MediaQuery.sizeOf(context);
    return Pinput(
      length: 6,
      controller: _pinController,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      defaultPinTheme: PinTheme(
        width: screenSize.width * 0.12,
        height: screenSize.width * 0.12,
        textStyle: GoogleFonts.outfit(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: theme.primaryColor,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.onTertiary,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
      ),
      focusedPinTheme: PinTheme(
        width: screenSize.width * 0.12,
        height: screenSize.width * 0.12,
        textStyle: GoogleFonts.outfit(
          fontSize: 20.sp,
          fontWeight: FontWeight.w600,
          color: theme.primaryColor,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.onTertiary,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.primaryColor),
          boxShadow: [
            BoxShadow(
              color: theme.primaryColor.withOpacity(0.1),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildResendButton(ThemeData theme) {
    return Column(
      children: [
        if (_canResend)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text("Didn't receive code?"),
              TextButton(
                onPressed: _resendCode,
                child: Text(
                  'Resend OTP',
                  style: GoogleFonts.outfit(
                    color: theme.primaryColor,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
      ],
    ).animate().fadeIn(duration: 800.ms);
  }
}
