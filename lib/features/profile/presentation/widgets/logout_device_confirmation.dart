import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/instance_manager.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LogoutDeviceConfirmationDialog extends StatefulWidget {
  const LogoutDeviceConfirmationDialog({super.key});

  @override
  State<LogoutDeviceConfirmationDialog> createState() =>
      _LogoutDeviceConfirmationDialogState();
}

class _LogoutDeviceConfirmationDialogState
    extends State<LogoutDeviceConfirmationDialog> {
  final bool _isLoggingOut = false;
  bool _isCancelling = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is LoggedOutState) {
          Navigator.of(context).pop();
          context.go(AppRouteName.tokenDeviceLogin);
       
        } else if (state is AuthError) {
          CustomToastification(context, message: state.message);
        }
      },
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(24.r),
          ),
         
            child: _buildContentBox(context, state),
          
        );
      },
    );
  }

  Widget _buildContentBox(BuildContext context, AuthState state) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 30.h),
      // margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(36.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            _buildIconButton(context),
            SizedBox(height: 16.h),
            Text(
              'Logout',
              style: GoogleFonts.outfit(
                fontSize: 20.sp,
                fontWeight: FontWeight.w700,
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: 6.h),
            Text(
              'Are you sure you want to logout?',
              style: GoogleFonts.outfit(
                  fontSize: 16.sp, color: const Color(0xFFAAAAAA)),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                  child: CustomButton(
                    text: 'Cancel',
                    showLoadingIndicator: _isCancelling,
                    onPressed: () {
                      if (!_isCancelling) {
                        setState(() => _isCancelling = true);
                        Navigator.of(context).pop();
                      }
                    },
                    options: CustomButtonOptions(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      color: theme.cardColor,
                      textStyle: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: theme.primaryColor,
                      ),
                      borderSide: BorderSide(
                        color: theme.primaryColor.withOpacity(0.4),
                      ),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: CustomButton(
                    text: 'Logout',
                    showLoadingIndicator: state is AuthLoading || _isLoggingOut,
                    onPressed: () {
                      Get.find<GetAppThemeController>().toggleThemeToDollar();
                      context.read<AuthBloc>().add(const LogoutEvent());
                    },
                    options: CustomButtonOptions(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      color: Colors.red,
                      textStyle: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ).animate().slideY(
          begin: 1,
          end: 0,
          duration: 300.ms,
          curve: Curves.easeOutQuart,
        );
  }

  Widget _buildIconButton(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Image.asset(
        MediaRes.logoutIcon,
        height: 30.h,
        width: 30.w,
        color: Theme.of(context).primaryColor,
      ),
    );
  }
}
