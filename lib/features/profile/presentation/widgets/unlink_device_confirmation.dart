import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_toast.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/features/auth/application/bloc/auth_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/foundation.dart';

class UnlinkDeviceConfirmationDialog extends StatefulWidget {
  const UnlinkDeviceConfirmationDialog({super.key});

  @override
  State<UnlinkDeviceConfirmationDialog> createState() =>
      _UnlinkDeviceConfirmationDialogState();
}

class _UnlinkDeviceConfirmationDialogState
    extends State<UnlinkDeviceConfirmationDialog> {
  bool _isUnlinking = false;
  bool _isCancelling = false;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is DeviceUnlinkedState) {
          Navigator.of(context).pop();
          context.go(AppRouteName.signIn);
        } else if (state is AuthError) {
          Navigator.of(context).pop();
          CustomToastification(context, message: state.message);
        }
      },
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: _buildContentBox(context, state),
        );
      },
    );
  }

  Widget _buildContentBox(BuildContext context, AuthState state) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.0.w, vertical: 30.h),
      // margin: EdgeInsets.only(left: 0.w, right: 0.w, bottom: 16.h),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(36.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            _buildIconButton(context),
            SizedBox(height: 16.h),
            Text(
              "Unlink Device",
              style: GoogleFonts.outfit(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            SizedBox(height: 5.h),
            Text(
              "Are you sure you want to unlink this device?",
              style:
                  GoogleFonts.outfit(fontSize: 16.sp, color: Color(0xFFAAAAAA)),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                  child: CustomRoundedBtn(
                    btnText: "Cancel",
                    isLoading: _isCancelling,
                    textColor: Theme.of(context).primaryColor,
                    borderSide:
                        BorderSide(color: Theme.of(context).primaryColor),
                    bgColor: theme.cardColor,
                    onTap: () {
                      if (!_isCancelling) {
                        setState(() => _isCancelling = true);
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: CustomRoundedBtn(
                    btnText: "Unlink",
                    isLoading: state is AuthLoading || _isUnlinking,
                    onTap: () {
                      if (!_isUnlinking && state is! AuthLoading) {
                        setState(() => _isUnlinking = true);
                        Get.find<GetAppThemeController>().toggleThemeToDollar();
                        debugPrint('Initiating device unlink...');
                        GlobalVariable.currentlySelectedWallet = 'USD';
                        context.read<AuthBloc>().add(const UnlinkDeviceEvent());
                      }
                    },
                    bgColor: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconButton(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: theme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Image.asset(
        MediaRes.unlinkDevice,
        height: 30.h,
        width: 30.h,
        color: Theme.of(context).primaryColor,
      ),
    );
  }
}
