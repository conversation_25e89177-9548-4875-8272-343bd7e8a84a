import 'package:dartz/dartz.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';
import 'package:cbrs/features/transactions/domain/repositories/transaction_repository.dart';

class GetTransactions
    implements UsecaseWithParams<List<Transaction>, GetTransactionsParams> {
  const GetTransactions(this.repository);
  final TransactionRepository repository;

  @override
  Future<Either<Failure, List<Transaction>>> call(
    GetTransactionsParams params,
  ) {
    return repository.getTransactions(
        page: params.page,
        perPage: params.perPage,
        currency: params.currency,
        transactionType: params.transactionType,
        billReason: params.billReason,
        startDate: params.startDate,
        endDate: params.endDate,
        status: params.status,
        multipleStatus: params.multipleStatus);
  }

  Future<Map<String, String>> fetchUserAvatars(List<String> userIds) {
    return repository.fetchUserAvatars(userIds);
  }
}

class GetTransactionsParams {
  const GetTransactionsParams({
    required this.page,
    required this.perPage,
    required this.status,
    required this.currency,

     this.billReason,
    this.transactionType,
    this.startDate,
    this.endDate,
    this.multipleStatus,
  });
  final int page;
  final int perPage;
 final  String currency;

 final  String? billReason;
  final String? transactionType;
  final String? startDate;
  final String? endDate;
  final String status;
  final String? multipleStatus;
}
