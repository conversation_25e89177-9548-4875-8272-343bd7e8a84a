import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/auth/data/models/user_dto.dart';
import 'package:cbrs/features/transactions/domain/entities/transaction.dart';

class TransactionModel extends Transaction {
  const TransactionModel({
    required super.id,
    required super.senderId,
    required super.senderName,
    required super.transactionOwner,
    required super.transactionType,
    required super.billAmount,
    required super.paidAmount,
    required super.billRefNo,
    required super.status,
    required super.createdAt,
    required super.lastModified,
    required super.serviceCharge,
    required super.vat,
    required super.totalAmount,
    super.overPaid,
    super.underPaid,
    super.acceptedAmount,
    super.senderPhone,
    super.senderEmail,
    super.beneficiaryId,
    super.beneficiaryName,
    super.beneficiaryEmail,
    super.beneficiaryAccountNo,
    super.beneficiaryPhone,
    super.bankName,
    super.bankCode,
    super.bankLogo,
    super.orderId,
    super.giftPackageId,
    super.totalGiftPackageQty,
    super.originalCurrency,
    super.changedCurrency,
    super.exchangeRate,
    super.billReason,
    super.paidDate,
    super.walletFTNumber,
    super.beneficiaryConnectCode,
    super.cardNumber,
    super.connectRefNo,
    super.mpgsReference,
    super.ftNumber,
    super.senderConnectCode,
    super.loanType,
    super.facilitationFee,
    super.productDetails,
    super.giftpackage,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>? ?? json;
    return TransactionModel(
      id: data['id']?.toString() ?? '',
      senderId: data['senderId'] as String? ?? '',
      senderName: data['senderName'] as String? ?? '',
      senderPhone: data['senderPhone'] as String?,
      senderEmail: data['senderEmail'] as String?,
      transactionOwner: data['transactionOwner'] as String? ?? '',
      beneficiaryId: data['beneficiaryId'] as String?,
      beneficiaryName: data['beneficiaryName'] as String?,
      beneficiaryEmail: data['beneficiaryEmail'] as String?,
      beneficiaryAccountNo: data['beneficiaryAccountNo'] as String?,
      beneficiaryPhone: data['beneficiaryPhone'] as String?,
      bankName: data['bankName'] as String?,
      bankCode: data['bankCode'] as String?,
      bankLogo: data['bankLogo'] as String?,
      transactionType: data['transactionType'] as String? ?? '',
      orderId: data['orderId'] as String?,
      giftPackageId: data['giftPackageId'] as String?,
      totalGiftPackageQty: data['totalGiftPackageQty'] as int?,
      billAmount: (data['billAmount'] as num?)?.toDouble() ?? 0.0,
      originalCurrency: data['originalCurrency'] as String?,
      changedCurrency: data['changedCurrency'] as String?,
      exchangeRate: (data['exchangeRate'] as num?)?.toDouble(),
      paidAmount: (data['paidAmount'] as num?)?.toDouble() ?? 0.0,
      billRefNo: data['billRefNo'] as String? ?? '',
      billReason: data['billReason'] as String?,
      paidDate: data['paidDate'] as String?,
      walletFTNumber: data['walletFTNumber'] as String?,
      status: data['status'] as String? ?? '',
      createdAt: DateTime.parse(
        data['createdAt'] as String? ?? DateTime.now().toIso8601String(),
      ),
      lastModified: DateTime.parse(
        data['lastModified'] as String? ?? DateTime.now().toIso8601String(),
      ),
      totalAmount: AppMapper.safeDouble(data['totalAmount']),
      vat: AppMapper.safeDouble(data['VAT']),
      serviceCharge: AppMapper.safeDouble(data['serviceCharge']),
      cardNumber: AppMapper.safeString(data['cardNumber']),
      beneficiaryConnectCode:
          AppMapper.safeString(data['beneficiaryConnectCode']),
      connectRefNo: AppMapper.safeString(data['connectRefNo']),
      mpgsReference: AppMapper.safeString(data['mpgsReference']),
      ftNumber: AppMapper.safeString(data['FTNumber']),
      senderConnectCode: AppMapper.safeString(data['senderConnectCode']),
      overPaid: AppMapper.safeBool(data['overPaid']),
      underPaid: AppMapper.safeBool(data['underPaid']),
      acceptedAmount: AppMapper.safeDouble(data['acceptedAmount']),
      loanType: AppMapper.safeString(data['loanType']),
      facilitationFee: AppMapper.safeDouble(data['facilitationFee']),
      productDetails: TransactionProductDetailsModel.fromJson(
        AppMapper.safeMap(json['productDetails']),
      ),
      giftpackage: json['giftPackage'] != null
          ? TransactionGiftPackageModel.fromJson(
              json['giftPackage'] as Map<String, dynamic>,
            )
          : null,

/*
this.connectRefNo,
    this.beneficiaryConnectCode,
    this.mpgsRefNo,*/
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderPhone': senderPhone,
      'senderEmail': senderEmail,
      'transactionOwner': transactionOwner,
      'beneficiaryId': beneficiaryId,
      'beneficiaryName': beneficiaryName,
      'beneficiaryEmail': beneficiaryEmail,
      'beneficiaryAccountNo': beneficiaryAccountNo,
      'beneficiaryPhone': beneficiaryPhone,
      'bankName': bankName,
      'bankCode': bankCode,
      'transactionType': transactionType,
      'orderId': orderId,
      'giftPackageId': giftPackageId,
      'totalGiftPackageQty': totalGiftPackageQty,
      'billAmount': billAmount,
      'originalCurrency': originalCurrency,
      'changedCurrency': changedCurrency,
      'exchangeRate': exchangeRate,
      'paidAmount': paidAmount,
      'billRefNo': billRefNo,
      'billReason': billReason,
      'paidDate': paidDate,
      'walletFTNumber': walletFTNumber,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
    };
  }
}

class TransactionProductDetailsModel extends TransactionProductDetailsEntity {
  TransactionProductDetailsModel({
    required super.make,
    required super.name,
    required super.color,
    required super.model,
    required super.price,
  });

  factory TransactionProductDetailsModel.fromJson(Map<String, dynamic> json) {
    return TransactionProductDetailsModel(
      color: AppMapper.safeString(json['color']),
      make: AppMapper.safeString(json['make']),
      name: AppMapper.safeString(json['name']),
      model: AppMapper.safeString(json['model']),
      price: AppMapper.safeString(json['price']),
    );
  }
}

class TransactionGiftPackageModel extends TransactionGiftPackageEntity {
  const TransactionGiftPackageModel({
    required super.name,
    required super.merchant,
    required super.discountBannerLevel,
    required super.unitPrice
  });

  factory TransactionGiftPackageModel.fromJson(Map<String, dynamic> json) {

    return TransactionGiftPackageModel(
      name: safeString(json['name']),
      merchant: TransactionMerchantsModel.fromJson(
        AppMapper.safeMap(json['merchant']),
      ),
      discountBannerLevel: AppMapper.safeList<dynamic>(
              json['discountBannerLevel'])
          .map(
            (item) =>
                TransactionBannelLevelModel.fromJson(AppMapper.safeMap(item)),
          )
          .toList(),

          unitPrice: AppMapper.safeFormattedNumberWithDecimal(json['unitPrice'])
    );
  }
}

class TransactionMerchantsModel extends TransactionMerchantsEntity {
  TransactionMerchantsModel({
    required super.merchantName,
    super.merchantPhone,
    super.merchantEmail,
  });

  factory TransactionMerchantsModel.fromJson(Map<String, dynamic> json) {
    return TransactionMerchantsModel(
      merchantName: safeString(json['name']),
      merchantPhone: safeString(json['phone']),
      merchantEmail: safeString(json['email']),
    );
  }
}

class TransactionBannelLevelModel extends TransactionBannelLevelEntity {
  TransactionBannelLevelModel({
    required super.discountType,
    super.discountPercentage,
    super.discountAmount,
  });

  factory TransactionBannelLevelModel.fromJson(Map<String, dynamic> json) {
    return TransactionBannelLevelModel(
      discountType: safeString(json['discountType']),
      discountPercentage: safeString(json['discountPercentage']),
      discountAmount: safeString(json['discountAmount']),
    );
  }
}
