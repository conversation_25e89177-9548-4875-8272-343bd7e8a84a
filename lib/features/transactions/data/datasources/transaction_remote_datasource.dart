import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/transactions/data/models/confirm_transfer_response_model.dart';
import 'package:cbrs/features/transactions/data/models/invoice-response_model.dart';
import 'package:cbrs/features/transactions/data/models/transaction_model.dart';
import 'package:cbrs/features/transactions/data/models/transaction_with_avatar.dart';
import 'package:cbrs/features/transactions/data/models/transfer_limit_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/instance_manager.dart';
import 'dart:typed_data';

class AvatarModel {
  AvatarModel({required this.id, required this.name, this.avatar});

  factory AvatarModel.fromJson(Map<String, dynamic> json) {
    return AvatarModel(
      id: json['id'] as String,
      name: json['name'] as String,
      avatar: json['avatar'] != null ? json['avatar'] as String : '',
    );
  }
  final String id;
  final String name;
  final String? avatar;
}

abstract class TransactionRemoteDataSource {
  /// Retrieves a paginated list of transactions with optional filters
  ///
  /// [page] - The page number to retrieve (starting from 1)
  /// [perPage] - Number of items per page
  /// [transactionType] - Optional filter for transaction type
  /// [startDate] - Optional start date filter in ISO format
  /// [endDate] - Optional end date filter in ISO format
  Future<List<TransactionModel>> getTransactions({
    int page = 1,
    int perPage = 10,
    required String status,
   required String currency,

   String? billReason,
String? multipleStatus,
    String? transactionType,
    String? startDate,
    String? endDate,
  });

  /// Retrieves detailed information for a specific transaction
  ///
  /// [id] - The unique identifier of the transaction
  Future<TransactionModel> getTransactionDetails(String id);
  Future<TransactionModel> validateTransaction(String id);

  /// Retrieves invoice information using a bill reference number
  ///
  /// [billRefNo] - The bill reference number
  Future<InvoiceResponseModel> getInvoice(String billRefNo);

  /// Fetches user avatars for a list of user IDs
  ///
  /// [userIds] - List of user IDs to fetch avatars for
  /// Returns a map of user ID to avatar URL
  Future<Map<String, String>> fetchUserAvatars(List<String> userIds);

  /// Retrieves recent wallet transfers with user avatars
  ///
  /// [limit] - Maximum number of transfers to retrieve
  /// [currentUserId] - ID of the current user to filter transfers
  Future<List<TransactionWithAvatar>> getRecentWalletTransfers({
    required int limit,
    required String currentUserId,
  });

  Future<TransferLimitModel> getTransferLimit({
    required String transactionType,
    required String currency,
  });

  Future<String> getLoanInvoice({
    required String billRefNo,
    required LoanReceipt loanReceipt,
  });

  Future<ConfirmTransferResponseModel> confirmTransfer({
    required String pin,
    required String billRefNo,
    required String transactionType,
    String? otp,
  });

  Future<bool> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  });

  Future<dynamic> resendOtp({
    required String billRefNo,
    required String otpFor,
  });
}

/// Implementation of [TransactionRemoteDataSource] that communicates with backend APIs
class TransactionRemoteDataSourceImpl implements TransactionRemoteDataSource {
  TransactionRemoteDataSourceImpl({
    required ApiService apiService,
    required AuthLocalDataSource authLocalDataSource,
  }) : _apiService = apiService;

  final ApiService _apiService;

  @override
  Future<List<TransactionModel>> getTransactions({
    int page = 1,
    int perPage = 10,
   required String status,
   required String currency,

   String? billReason,
   String? multipleStatus,
    String? transactionType,
    String? startDate,
    String? endDate,
  }) async {
    final queryParams = {
      if(status.isNotEmpty)
      'status': status.toUpperCase(),
      if(multipleStatus != null)
      'multipleStatus': multipleStatus!.toUpperCase(),
      'page': page,
      'per_page': perPage,
      'startDate': startDate,
      'endDate': endDate,
      'currency': currency
    };

    // Handle special case for MERCHANT_PAYMENT with Till billReason
    if (transactionType != null && transactionType != 'All') {
      final formattedType = transactionType.toLowerCase().replaceAll(' ', '_');
      queryParams['transactionType'] = formattedType;

      // Add billReason=Till for MERCHANT_PAYMENT
      // if (transactionType == 'MERCHANT_PAYMENT') {
      //   queryParams['billReason'] = 'Default';
      // }
    }

    final result = await _apiService.get(
      ApiEndpoints.memberTransactionsPaginate,
      queryParameters: queryParams,
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        final responseData = data as Map<String, dynamic>;
        final transactionData = responseData['data'] as Map<String, dynamic>;
        final docs = transactionData['docs'] as List;

        final transactions = docs
            .map(
              (json) => TransactionModel.fromJson(json as Map<String, dynamic>),
            )
            .toList();

        return transactions;
      },
      (error) => throw ApiException(
        message: 'Failed to fetch transactions: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<TransactionModel> getTransactionDetails(String id) async {
    final result = await _apiService.get(
      ApiEndpoints.memberTransactionDetails(id),
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        final responseData = data as Map<String, dynamic>;
        return TransactionModel.fromJson(
          responseData['data'] as Map<String, dynamic>,
        );
      },
      (error) => throw ApiException(
        message: 'Failed to fetch transaction details: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<TransactionModel> validateTransaction(String id) async {
    final result = await _apiService.get(
      ApiEndpoints.validateTransaction(id),
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        final responseData = data as Map<String, dynamic>;
        return TransactionModel.fromJson(
          responseData['data'] as Map<String, dynamic>,
        );
      },
      (error) => throw ApiException(
        message: 'Failed to fetch transaction details: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<InvoiceResponseModel> getInvoice(String billRefNo) async {
    final result = await _apiService.get(
      ApiEndpoints.invoice(billRefNo),
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        final responseData = data as Map<String, dynamic>;
        return InvoiceResponseModel.fromJson(responseData);
      },
      (error) => throw ApiException(
        message: 'Failed to fetch invoice: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<Map<String, String>> fetchUserAvatars(List<String> userIds) async {
    if (userIds.isEmpty) return {};

    userIds.join(',');
    final result = await _apiService.post(
      ApiEndpoints.bulkMemberLookUp,
      data: {'ids': userIds},
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        try {
          final responseData = data as Map<String, dynamic>;
          final avatarsData = responseData['data'] as List<dynamic>;

          debugPrint('avatar ${avatarsData.length} ');

          final avatarMap = <String, String>{};
          for (final avatar in avatarsData) {
            debugPrint('issiisis $avatar');
            final av = AvatarModel.fromJson(avatar as Map<String, dynamic>);
            avatarMap[av.id] = av.avatar ?? '';
          }

          return avatarMap;
        } catch (error) {
          throw const ApiException(
            message: 'error in casting avatars',
            statusCode: 500,
          );
        }
      },
      (error) => throw ApiException(
        message: 'Error fetching avatars: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

/*
  @override
  Future<Map<String, String>> fetchUserAvatars(List<String> userIds) async {
    debugPrint('Hey niggas');

    if (userIds.isEmpty) return {};

    final queryString = userIds.join(',');
    final result = await _apiService.get(
      ApiEndpoints.bulkMemberLookUp,
      queryParameters: {'ids': queryString},
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        final responseData = data as Map<String, dynamic>;
        final avatarsData = responseData['data'] as Map<String, dynamic>?;

        debugPrint("avatar ${avatarsData} ");
        if (avatarsData == null) return {};

        final avatarMap = <String, String>{};
        avatarsData.forEach((userId, avatar) {
          if (avatar != null) {
            debugPrint("avatar url ${avatar}");
            avatarMap[userId] = avatar.toString();
          }
        });

        return avatarMap;
      },
      (error) => throw ApiException(
        message: 'Error fetching avatars: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }
*/
  @override
  Future<List<TransactionWithAvatar>> getRecentWalletTransfers({
    required int limit,
    required String currentUserId,
  }) async {
    final result = await _apiService.get(
      ApiEndpoints.memberTransactionsPaginate,
      queryParameters: {
        'status': 'COMPLETED',
        'page': 1,
        'per_page': 10,
        'transactionType': 'wallet_transfer',
        'isBeneficiary': false,
      },
      parser: (data) => data,
    );

    return result.fold(
      (data) async {
        final responseData = data as Map<String, dynamic>;
        final transactionData = responseData['data'] as Map<String, dynamic>;
        final docs = transactionData['docs'] as List;

        debugPrint(
          'transaction docs length ${docs.length} and userID $currentUserId',
        );

        // Filter wallet transfers where current user is either sender or beneficiary
        final transactions = docs
            .map(
              (json) => TransactionModel.fromJson(json as Map<String, dynamic>),
            )
            .where(
              (t) =>
                  // t.transactionType.toLowerCase() == 'wallet_transfer' &&

                  (t.senderId == currentUserId ||
                      t.beneficiaryId == currentUserId) &&
                  t.senderId != t.beneficiaryId,
            )
            .toList();

        debugPrint('after  ${transactions.length} ');

        debugPrint('hellow transaction my id $currentUserId ');
        final userIds = transactions
            .map(
              (t) => currentUserId == t.senderId ? t.beneficiaryId : t.senderId,
            )
            .where((id) => id != null)
            .cast<String>()
            .toList();

        debugPrint('user id s$userIds lenght ${userIds.length}');

        // final avatarMap = await fetchUserAvatars(userIds);
        late Map<String, dynamic> avatarMap;
        try {
          avatarMap = await fetchUserAvatars(userIds);
        } catch (e, stack) {
          debugPrint('Error fetching avatars: $e\n$stack');
          rethrow;
        }

        final uniqueContactTransactions = <String, TransactionModel>{};

        for (final transaction in transactions) {
          final contactId = currentUserId == transaction.senderId
              ? transaction.beneficiaryId
              : transaction.senderId;

          if (contactId != null &&
              !uniqueContactTransactions.containsKey(contactId)) {
            uniqueContactTransactions[contactId] = transaction;
          }
        }

        debugPrint('hhhhhhhhhhhhh $avatarMap');

        // final limitedTransactions = uniqueContactTransactions.values.toList();
        try {
          final result = uniqueContactTransactions.values.map((transaction) {
            final contactId = currentUserId == transaction.senderId
                ? transaction.beneficiaryId
                : transaction.senderId;

            return TransactionWithAvatar(
              transaction: transaction,
              avatar: contactId != null
                  ? avatarMap[contactId] != null
                      ? avatarMap[contactId] as String
                      : null
                  : null,
              displayName: currentUserId == transaction.senderId
                  ? transaction.beneficiaryName
                  : transaction.senderName,
            );
          }).toList();

          debugPrint('ressult of final ${result.length}');
          return result;
        } catch (e, stack) {
          debugPrint('Error during result mapping: $e\n$stack');
          rethrow;
        }

        // final result = transactions.map((transaction) {
        //   debugPrint('heellll');
        //   final contactId = currentUserId == transaction.senderId
        //       ? transaction.beneficiaryId
        //       : transaction.senderId;

        //   return TransactionWithAvatar(
        //     transaction: transaction,
        //     avatar: contactId != null ? avatarMap[contactId] : null,
        //     displayName: currentUserId == transaction.senderId
        //         ? transaction.beneficiaryName
        //         : transaction.senderName,
        //   );
        // }).toList();

        // debugPrint('ressult of final ${result.length}');

        // return result;
      },
      (error) => throw ApiException(
        message: 'Failed to fetch recent wallet transfers: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

/*
  @override
  Future<List<TransactionWithAvatar>> getRecentWalletTransfers({
    required int limit,
    required String currentUserId,
  }) async {
    final result = await _apiService.get(
      ApiEndpoints.memberTransactionsPaginate,
      queryParameters: {
        'status': 'COMPLETED',
        'page': 1,
        'per_page': 10,
        'transactionType': 'wallet_transfer',
        'isBeneficiary': false,
      },
      parser: (data) => data,
    );

    return result.fold(
      (data) async {
        final responseData = data as Map<String, dynamic>;
        final transactionData = responseData['data'] as Map<String, dynamic>;
        final docs = transactionData['docs'] as List;

        debugPrint(
          'transaction docs length ${docs.length} and userID $currentUserId',
        );

        // Filter wallet transfers where current user is either sender or beneficiary
        final transactions = docs
            .map(
              (json) => TransactionModel.fromJson(json as Map<String, dynamic>),
            )
            .where(
              (t) =>
                  // t.transactionType.toLowerCase() == 'wallet_transfer' &&

                  (t.senderId == currentUserId ||
                      t.beneficiaryId == currentUserId) &&
                  t.senderId != t.beneficiaryId,
            )
            .toList();

        debugPrint('after  ${transactions.length} ');

        // Sort transactions by date (newest first)
        transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        // Get unique contacts by taking the first transaction for each contact
        // final uniqueContactTransactions = <String, TransactionModel>{};

        // for (final transaction in transactions) {
        //   final contactId = currentUserId == transaction.senderId
        //       ? transaction.beneficiaryId
        //       : transaction.senderId;

        //   if (contactId != null &&
        //       !uniqueContactTransactions.containsKey(contactId)) {
        //     uniqueContactTransactions[contactId] = transaction;
        //   }
        // }

        // debugPrint('uniq ${uniqueContactTransactions.length}');
        // // Limit to requested number
        // final limitedTransactions =
        //     uniqueContactTransactions.values.take(limit).toList();

        // Extract user IDs for avatar fetching

        debugPrint('hellow transaction');
        final userIds = transactions
            .map(
              (t) => currentUserId == t.senderId ? t.beneficiaryId : t.senderId,
            )
            .where((id) => id != null)
            .cast<String>()
            .toList();

        // Fetch avatars in a single API call
        final avatarMap = await fetchUserAvatars(userIds);

        // debugPrint('limitedTransactions ${limitedTransactions.length}');

        // Combine transactions with avatars
        final result = transactions.map((transaction) {
          debugPrint('heellll');
          final contactId = currentUserId == transaction.senderId
              ? transaction.beneficiaryId
              : transaction.senderId;

          return TransactionWithAvatar(
            transaction: transaction,
            avatar: contactId != null ? avatarMap[contactId] : null,
            displayName: currentUserId == transaction.senderId
                ? transaction.beneficiaryName
                : transaction.senderName,
          );
        }).toList();

        debugPrint('ressult ${result.length}');

        return result;
      },
      (error) => throw ApiException(
        message: 'Failed to fetch recent wallet transfers: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

*/
  @override
  Future<TransferLimitModel> getTransferLimit({
    required String transactionType,
    required String currency,
  }) async {
    final result = await _apiService.get(
      ApiEndpoints.transferLimit,
      queryParameters: {
        'transactionType': transactionType,
        'currency': currency,
      },
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        final responseData = data as Map<String, dynamic>;
        return TransferLimitModel.fromJson(responseData);
      },
      (error) => throw ApiException(
        message: 'Failed to fetch transfer limit: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<String> getLoanInvoice({
    required String billRefNo,
    required LoanReceipt loanReceipt,
  }) async {
    // Use the ApiService to get the loan invoice
    final invoiceUrl =
        ApiConstants.getCarLoanInvoiceUrl(billRefNo, loanReceipt.name);

    final result = await _apiService.get(
      invoiceUrl,
      parser: (data) => data,
    );

    return result.fold((data) {
      final responseData = data as Map<String, dynamic>;
      final invoiceUrl = responseData['invoiceURL'] ?? '';

      return invoiceUrl as String;
    }, (error) {
      throw const ApiException(
        message: 'Invalid invoice response format',
        statusCode: 500,
      );
    });

    /*

//         final invoiceUrl = responseData['invoiceURL'] !=null  ? responseData['invoiceURL'] : '';
    return invoiceUrl as String;
    return result.fold(
      (data) {
        debugPrint('🔍 RemoteDataSource: Loan invoice response: $data');
        final responseData = data as Map<String, dynamic>;

        if (responseData['success'] == true &&
            responseData['invoiceURL'] != null) {
          // Get the invoice URL for direct launching
          final downloadUrl = ApiConstants.getInvoiceDownloadUrl(
            responseData['invoiceURL'] as String,
          );
          debugPrint(
            '🔍 RemoteDataSource: Invoice URL for launching: $downloadUrl',
          );

          return InvoiceResponseModel(
            invoiceUrl: downloadUrl,
            invoiceData: Uint8List(0),
          );
        }

        throw const ApiException(
          message: 'Invalid invoice response format',
          statusCode: 500,
        );
      },
      (error) => throw ApiException(
        message: 'Failed to fetch loan invoice: ${error.message}',
        statusCode: error.statusCode ?? 500,
      ),
    );
    */
  }

  @override
  Future<ConfirmTransferResponseModel> confirmTransfer({
    required String pin,
    required String billRefNo,
    required String transactionType,
    String? otp,
  }) async {
    final data = <String, dynamic>{
      'PIN': pin,
      'billRefNo': billRefNo,
      'transactionType': transactionType,
      'authType': otp != null ? 'PIN_AND_OTP' : 'PIN',
    };

    if (otp != null) {
      data['otpCode'] = otp;
    }

    debugPrint('Confirming transaction for $transactionType');
    final result = await _apiService.post(
      ApiEndpoints.confirmPayment,
      data: data,
      parser: (data) => data,
    );

    return result.fold(
      (data) {
        final responseData = data as Map<String, dynamic>;
        return ConfirmTransferResponseModel.fromJson(responseData);
      },
      (error) => throw ApiException(
        message: error.message ?? 'Failed to confirm transfer',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<bool> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  }) async {
    final result = await _apiService.post(
      ApiEndpoints.verifyOtp,
      data: {
        'billRefNo': billRefNo,
        'otpFor': otpFor,
        'otpCode': otpCode,
      },
      parser: (data) => data,
    );

    return result.fold(
      (data) => true,
      (error) => throw ApiException(
        message: error.message ?? 'Failed to verify OTP',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }

  @override
  Future<dynamic> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    final result = await _apiService.post(
      ApiEndpoints.resendOtp,
      data: {
        'billRefNo': billRefNo,
        'otpFor': otpFor,
      },
      parser: (data) => data,
    );

    return result.fold(
      (data) => data,
      (error) => throw ApiException(
        message: error.message ?? 'Failed to resend OTP',
        statusCode: error.statusCode ?? 500,
      ),
    );
  }
}
