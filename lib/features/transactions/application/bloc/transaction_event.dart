// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'transaction_bloc.dart';

abstract class TransactionEvent extends Equatable {
  const TransactionEvent();

  @override
  List<Object?> get props => [];
}

class FetchTransactionsEvent extends TransactionEvent {
  const FetchTransactionsEvent({
    required this.page,
    required this.perPage,
    required this.status,
    this.multipleStatus,
    this.keepCache = true,
        required this.currency,
    this.billReason,
  });
  final int page;
  final int perPage;
  final bool keepCache;
  final String status;
  final String? multipleStatus;
    final String currency;
  final String? billReason;

  @override
  List<Object?> get props => [page, perPage, keepCache, status, multipleStatus];
}

class FilterTransactionsEvent extends TransactionEvent {
  const FilterTransactionsEvent({
    required this.category,
    required this.status,
    required this.currency,
    this.billReason,
    this.multipleStatus,
  });
  final String category;
  final String status;
  final String currency;
  final String? billReason;
  final String? multipleStatus;

  @override
  List<Object?> get props => [category];
}

class FilterByDateRangeEvent extends TransactionEvent {
  const FilterByDateRangeEvent({
    required this.startDate,
    required this.endDate,
    required this.transactionType,
    this.multipleStatus,
    required this.status,
    this.billReason,
    required this.currency,
  });
  final DateTime startDate;
  final DateTime endDate;
  final String transactionType;
  final String? multipleStatus;
  final String status;
  final String? billReason;
  final String currency;


  @override
  List<Object?> get props => [startDate, endDate, transactionType];
}

class FilterBySingleDateEvent extends TransactionEvent {
  const FilterBySingleDateEvent({
    required this.date,
    required this.status,
    this.multipleStatus,
        this.billReason,
    required this.currency,
  });
  final DateTime date;
  final String status;
  final String? multipleStatus;
  final String? billReason;
  final String currency;
  @override
  List<Object?> get props => [date];
}

class FetchMoreTransactionsEvent extends TransactionEvent {
  const FetchMoreTransactionsEvent({
    required this.page,
    required this.perPage,
    required this.status,
    this.multipleStatus,
           this.billReason,
    required this.currency,
  });
  final int page;
  final int perPage;
  final String status;
  final String? multipleStatus;
  final String? billReason;
  final String currency;
  @override
  List<Object?> get props => [page, perPage, status, multipleStatus];
}

class FetchRecentWalletTransfersEvent extends TransactionEvent {
  const FetchRecentWalletTransfersEvent({
    this.limit = 5,
  });
  final int limit;

  @override
  List<Object?> get props => [limit];
}

class FetchTransferLimitEvent extends TransactionEvent {
  const FetchTransferLimitEvent({
    required this.transactionType,
    required this.currency,
  });
  final String transactionType;
  final String currency;

  @override
  List<Object?> get props => [transactionType, currency];
}

class GetLoanInvoiceEvent extends TransactionEvent {
  const GetLoanInvoiceEvent({
    required this.billRefNo,
    required this.loanReceipt,
  });
  final String billRefNo;
  final LoanReceipt loanReceipt;

  @override
  List<Object?> get props => [billRefNo, loanReceipt];
}

class DownloadReceiptEvent extends TransactionEvent {
  const DownloadReceiptEvent({
    required this.billRefNo,
  });
  final String billRefNo;

  @override
  List<Object?> get props => [billRefNo];
}

class ConfirmTransferEvent extends TransactionEvent {
  const ConfirmTransferEvent({
    required this.pin,
    required this.billRefNo,
    required this.transactionType,
    this.otp,
  });
  final String pin;
  final String billRefNo;
  final TransactionType transactionType;
  final String? otp;

  @override
  List<Object?> get props => [pin, billRefNo, transactionType, otp];
}

class VerifyOtpEvent extends TransactionEvent {
  const VerifyOtpEvent({
    required this.billRefNo,
    required this.otpFor,
    required this.otpCode,
  });
  final String billRefNo;
  final String otpFor;
  final int otpCode;

  @override
  List<Object?> get props => [billRefNo, otpFor, otpCode];
}

class ResendOtpEvent extends TransactionEvent {
  const ResendOtpEvent({
    required this.billRefNo,
    required this.otpFor,
  });
  final String billRefNo;
  final String otpFor;

  @override
  List<Object?> get props => [billRefNo, otpFor];
}
