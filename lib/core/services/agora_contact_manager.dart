import 'dart:async';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cbrs/core/services/agora_chat_service.dart';
import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/features/chat/data/models/chat_contact_model.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Dedicated service to manage Agora Chat contacts integration
/// Handles mapping between app contacts and Agora usernames
class AgoraContactManager extends GetxService {
  AgoraContactManager({
    required AgoraChatService agoraChatService,
    required ContactSyncService contactSyncService,
  })  : _agoraChatService = agoraChatService,
        _contactSyncService = contactSyncService;

  final AgoraChatService _agoraChatService;
  final ContactSyncService _contactSyncService;

  // Cache for contact mappings
  final _contactCache = <String, ChatContactModel>{}.obs;
  final _blockedUsers = <String>[].obs;
  final _isInitialized = false.obs;

  // Getters
  bool get isInitialized => _isInitialized.value;
  List<String> get blockedUsers => _blockedUsers.toList();
  Map<String, ChatContactModel> get contactCache => Map.from(_contactCache);

  /// Initialize the Agora contact manager
  Future<void> initialize() async {
    if (_isInitialized.value) return;

    try {
      debugPrint('AgoraContactManager: Initializing...');

      // Ensure Agora Chat is initialized
      if (!_agoraChatService.isInitialized) {
        await _agoraChatService.initialize();
      }

      // Load blocked users
      await _refreshBlockedUsers();

      _isInitialized.value = true;
      debugPrint('AgoraContactManager: Initialization complete');
    } catch (e) {
      debugPrint('AgoraContactManager: Initialization failed - $e');
      rethrow;
    }
  }

  /// Refresh blocked users list from Agora
  Future<void> _refreshBlockedUsers() async {
    try {
      // Check if Agora Chat is logged in before attempting to get blocked users
      if (!_agoraChatService.isLoggedIn) {
        debugPrint(
          'AgoraContactManager: Cannot refresh blocked users - not logged in',
        );
        return;
      }

      final blockedUsersList = await _agoraChatService.getBlockedUsers();
      _blockedUsers.value = blockedUsersList;
      debugPrint(
        'AgoraContactManager: Found ${blockedUsersList.length} blocked users',
      );
    } catch (e) {
      debugPrint('AgoraContactManager: Error refreshing blocked users - $e');
      // Don't throw the error, just log it and continue
    }
  }

  /// Get all contacts with Agora attributes
  Future<List<ChatContactModel>> getContactsWithAttributes() async {
    if (!_isInitialized.value) {
      await initialize();
    }

    try {
      // Ensure contacts are synced
      if (_contactSyncService.members.isEmpty) {
        await _contactSyncService.syncContacts();
      }

      final appUsers = _contactSyncService.members;
      final contactsWithAttributes = <ChatContactModel>[];

      // Refresh blocked users
      await _refreshBlockedUsers();
      debugPrint(
          'Agora ContactManager: Processing ${appUsers.length} contacts');

      // Process synced app users
      for (final user in appUsers) {
        // Only process users who have an agoraUsername
        if (user.agoraUsername?.isNotEmpty == true) {
          try {
            // Check cache first
            final cacheKey = user.agoraUsername!;
            if (_contactCache.containsKey(cacheKey)) {
              final cachedContact = _contactCache[cacheKey]!;
              // Update blocked status
              final isBlocked = _isUserBlocked(user.agoraUsername!);
              final updatedContact =
                  cachedContact.copyWithBlockedStatus(isBlocked);
              contactsWithAttributes.add(updatedContact);
              continue;
            }

            // Add user as Agora contact (auto-accept is enabled)
            await _agoraChatService.addContact(user.agoraUsername!);

            // Fetch their Agora user attributes
            final userAttributes = await _agoraChatService
                .fetchUserAttributes(user.agoraUsername!);

            // Debug print to see what attributes we got from Agora
            debugPrint(
                '🔍 AgoraContactManager - Fetched attributes for ${user.agoraUsername}:');
            debugPrint(
                '  - User data: firstName="${user.firstName}", lastName="${user.lastName}", email="${user.email}", phone="${user.phoneNumber}"');
            debugPrint('  - Agora attributes: $userAttributes');

            // User attributes are now fetched via REST API
            debugPrint(
                '🔍 AgoraContactManager - Using REST API for user attributes');

            // Check if this user is blocked
            final isBlocked = _isUserBlocked(user.agoraUsername!);

            // Create contact model with both app data and Agora attributes
            final contactModel =
                ChatContactModel.fromAppUserWithAgoraAttributes(
              user,
              userAttributes,
            );

            // Debug print the final contact model
            debugPrint('🔍 AgoraContactManager - Created contact model:');
            debugPrint('  - ID: ${contactModel.id}');
            debugPrint('  - Username: ${contactModel.username}');
            debugPrint('  - DisplayName: ${contactModel.displayName}');
            debugPrint('  - Email: ${contactModel.email}');
            debugPrint('  - Phone: ${contactModel.phoneNumber}');
            debugPrint('  - CustomData: ${contactModel.customData}');

            final contactWithBlockedStatus =
                contactModel.copyWithBlockedStatus(isBlocked);

            // Cache the contact
            _contactCache[cacheKey] = contactWithBlockedStatus;

            debugPrint(
                'AgoraContactManager: Created contact - ID: ${contactWithBlockedStatus.id}, '
                'Username: ${contactWithBlockedStatus.username}, '
                'AgoraUsername: ${user.agoraUsername}, '
                'Blocked: ${contactWithBlockedStatus.isBlocked}');

            contactsWithAttributes.add(contactWithBlockedStatus);
          } catch (e) {
            debugPrint(
                'AgoraContactManager: Error processing user ${user.agoraUsername}: $e');
            // Fallback to basic contact from app data
            final isBlocked = _isUserBlocked(user.agoraUsername!);
            final basicContact = ChatContactModel.fromAppUser(user);
            final contactWithBlockedStatus =
                basicContact.copyWithBlockedStatus(isBlocked);
            contactsWithAttributes.add(contactWithBlockedStatus);
          }
        }
      }

      // Process blocked users who are not in the synced contacts
      final processedUsernames = contactsWithAttributes
          .map((contact) => contact.username.toLowerCase())
          .toSet();

      for (final blockedUsername in _blockedUsers) {
        if (!processedUsernames.contains(blockedUsername.toLowerCase())) {
          try {
            debugPrint(
                'AgoraContactManager: Processing blocked user not in synced contacts: $blockedUsername');

            // Check cache first
            if (_contactCache.containsKey(blockedUsername)) {
              final cachedContact = _contactCache[blockedUsername]!;
              final updatedContact = cachedContact.copyWithBlockedStatus(true);
              contactsWithAttributes.add(updatedContact);
              continue;
            }

            // Fetch their Agora user attributes
            final userAttributes =
                await _agoraChatService.fetchUserAttributes(blockedUsername);

            // Create contact model from Agora data only
            final contactModel =
                ChatContactModel.fromAgoraContact(blockedUsername)
                    .copyWithUserInfo(
              displayName:
                  userAttributes['nickname'] as String? ?? blockedUsername,
              email: userAttributes['email'] as String?,
              phoneNumber: userAttributes['phone'] as String?,
              avatarUrl: userAttributes['avatarUrl'] as String?,
            );

            final contactWithBlockedStatus =
                contactModel.copyWithBlockedStatus(true);

            // Cache the contact
            _contactCache[blockedUsername] = contactWithBlockedStatus;

            debugPrint(
                'AgoraContactManager: Created blocked contact - ID: ${contactWithBlockedStatus.id}, '
                'Username: ${contactWithBlockedStatus.username}, '
                'DisplayName: ${contactWithBlockedStatus.displayName}, '
                'Blocked: ${contactWithBlockedStatus.isBlocked}');

            contactsWithAttributes.add(contactWithBlockedStatus);
          } catch (e) {
            debugPrint(
                'AgoraContactManager: Error processing blocked user $blockedUsername: $e');
            // Fallback to basic contact from username only
            final basicContact =
                ChatContactModel.fromAgoraContact(blockedUsername);
            final contactWithBlockedStatus =
                basicContact.copyWithBlockedStatus(true);
            contactsWithAttributes.add(contactWithBlockedStatus);
          }
        }
      }

      debugPrint(
          'AgoraContactManager: Total contacts with attributes: ${contactsWithAttributes.length}');
      debugPrint(
          'AgoraContactManager: Blocked contacts: ${contactsWithAttributes.where((c) => c.isBlocked).length}');

      return contactsWithAttributes;
    } catch (e) {
      debugPrint(
          'AgoraContactManager: Error getting contacts with attributes: $e');
      return [];
    }
  }

  /// Get basic Agora contacts (without attributes)
  Future<List<ChatContactModel>> getBasicContacts() async {
    if (!_isInitialized.value) {
      await initialize();
    }

    try {
      final contacts = await _agoraChatService.getContacts();
      await _refreshBlockedUsers();

      return contacts.map((contactId) {
        final contact = ChatContactModel.fromAgoraContact(contactId);
        final isBlocked = _isUserBlocked(contactId);
        return contact.copyWithBlockedStatus(isBlocked);
      }).toList();
    } catch (e) {
      debugPrint('AgoraContactManager: Error getting basic contacts: $e');
      return [];
    }
  }

  /// Add a contact by username and ensure attributes are fetched/set
  Future<void> addContact(String username) async {
    if (!_isInitialized.value) {
      await initialize();
    }

    // Check if Agora Chat is logged in before attempting to add contact
    if (!_agoraChatService.isLoggedIn) {
      debugPrint('🔥 Agora Chat: Cannot add contact - not logged in');
      throw Exception('Chat client not initialized or not logged in');
    }

    try {
      debugPrint(
          '🔍 AgoraContactManager: Adding contact with attributes: $username');

      // Add contact to Agora Chat
      await _agoraChatService.addContact(username);

      // Fetch user attributes (this will create default ones if they don't exist)
      final userAttributes =
          await _agoraChatService.fetchUserAttributes(username);
      debugPrint(
          '🔍 AgoraContactManager: Fetched attributes for $username: $userAttributes');

      // Remove from cache to force refresh on next access
      _contactCache.remove(username);

      debugPrint(
          '🔍 AgoraContactManager: Contact added with attributes: $username');
    } catch (e) {
      debugPrint('🔍 AgoraContactManager: Error adding contact: $e');
      rethrow;
    }
  }

  /// Remove a contact by ID
  Future<void> removeContact(String contactId) async {
    if (!_isInitialized.value) {
      await initialize();
    }

    try {
      await _agoraChatService.chatClient!.contactManager
          .deleteContact(contactId);

      // Remove from cache
      _contactCache.remove(contactId);

      debugPrint('AgoraContactManager: Contact removed: $contactId');
    } catch (e) {
      debugPrint('AgoraContactManager: Error removing contact: $e');
      rethrow;
    }
  }

  /// Block a user
  Future<void> blockUser(String username) async {
    if (!_isInitialized.value) {
      await initialize();
    }

    try {
      await _agoraChatService.blockUser(username);

      // Update local blocked users list
      if (!_blockedUsers.contains(username)) {
        _blockedUsers.add(username);
      }

      // Update cache
      if (_contactCache.containsKey(username)) {
        final contact = _contactCache[username]!;
        _contactCache[username] = contact.copyWithBlockedStatus(true);
      }

      debugPrint('AgoraContactManager: User blocked: $username');
    } catch (e) {
      debugPrint('AgoraContactManager: Error blocking user: $e');
      rethrow;
    }
  }

  /// Unblock a user
  Future<void> unblockUser(String username) async {
    if (!_isInitialized.value) {
      await initialize();
    }

    try {
      await _agoraChatService.unblockUser(username);

      // Update local blocked users list
      _blockedUsers.remove(username);

      // Update cache
      if (_contactCache.containsKey(username)) {
        final contact = _contactCache[username]!;
        _contactCache[username] = contact.copyWithBlockedStatus(false);
      }

      debugPrint('AgoraContactManager: User unblocked: $username');
    } catch (e) {
      debugPrint('AgoraContactManager: Error unblocking user: $e');
      rethrow;
    }
  }

  /// Check if a user is blocked (case-insensitive)
  bool _isUserBlocked(String username) {
    return _blockedUsers.any(
        (blockedUser) => blockedUser.toLowerCase() == username.toLowerCase());
  }

  /// Find contact by participant ID
  ChatContactModel? findContactByParticipantId(String participantId) {
    // Try to find in cache first
    if (_contactCache.containsKey(participantId)) {
      return _contactCache[participantId];
    }

    // Try to find contact by participant ID in synced contacts
    try {
      final contact = _contactSyncService.members.firstWhere(
        (member) =>
            member.agoraUsername == participantId || member.id == participantId,
        orElse: () => _contactSyncService.members.firstWhere(
          (member) => member.phoneNumber == participantId,
          orElse: () => throw StateError('Contact not found'),
        ),
      );

      // Create and cache the contact
      final chatContact = ChatContactModel.fromAppUser(contact);
      final isBlocked = _isUserBlocked(participantId);
      final contactWithBlockedStatus =
          chatContact.copyWithBlockedStatus(isBlocked);

      _contactCache[participantId] = contactWithBlockedStatus;
      return contactWithBlockedStatus;
    } catch (e) {
      debugPrint(
          'AgoraContactManager: Contact not found for participant: $participantId');
      return null;
    }
  }

  /// Clear contact cache
  void clearCache() {
    _contactCache.clear();
    debugPrint('AgoraContactManager: Cache cleared');
  }

  /// Check if user data should be synced to Agora
  bool _shouldSyncUserDataToAgora(
      Map<String, dynamic> agoraAttributes, dynamic user) {
    // Check if Agora attributes are empty or missing key information
    final hasNickname = agoraAttributes['nickname']?.isNotEmpty == true &&
        agoraAttributes['nickname'] != user.agoraUsername;
    final hasEmail = agoraAttributes['email']?.isNotEmpty == true;
    final hasPhone = agoraAttributes['phone']?.isNotEmpty == true;

    // Check if backend has data that's missing in Agora
    final backendHasName =
        user.firstName?.isNotEmpty == true && user.lastName?.isNotEmpty == true;
    final backendHasEmail = user.email?.isNotEmpty == true;
    final backendHasPhone = user.phoneNumber?.isNotEmpty == true;

    // Debug logging
    debugPrint('🔍 _shouldSyncUserDataToAgora - Checking sync conditions:');
    debugPrint(
        '  - Agora hasNickname: $hasNickname (${agoraAttributes['nickname']})');
    debugPrint('  - Agora hasEmail: $hasEmail (${agoraAttributes['email']})');
    debugPrint('  - Agora hasPhone: $hasPhone (${agoraAttributes['phone']})');
    debugPrint(
        '  - Backend hasName: $backendHasName (${user.firstName} ${user.lastName})');
    debugPrint('  - Backend hasEmail: $backendHasEmail (${user.email})');
    debugPrint('  - Backend hasPhone: $backendHasPhone (${user.phoneNumber})');

    // Sync if backend has data but Agora doesn't
    final shouldSync = (backendHasName && !hasNickname) ||
        (backendHasEmail && !hasEmail) ||
        (backendHasPhone && !hasPhone);

    debugPrint('  - Final decision: shouldSync = $shouldSync');
    return shouldSync;
  }

  /// Sync user data from backend to Agora
  Future<void> _syncUserDataToAgora(dynamic user) async {
    try {
      final displayName = '${user.firstName} ${user.lastName}'.trim();

      await _agoraChatService.updateUserAttributes(
        nickname: displayName.isNotEmpty ? displayName : null,
        email: user.email?.isNotEmpty == true ? user.email as String? : null,
        phone: user.phoneNumber?.isNotEmpty == true
            ? user.phoneNumber as String?
            : null,
        ext: _createExtendedInfo(user),
      );

      debugPrint(
          '🔍 AgoraContactManager - Successfully synced user data to Agora');
    } catch (e) {
      debugPrint(
          '🔍 AgoraContactManager - Error syncing user data to Agora: $e');
    }
  }

  /// Create extended info string from user data
  String? _createExtendedInfo(dynamic user) {
    final extData = <String, String>{};

    if (user.city?.isNotEmpty == true) extData['city'] = user.city as String;
    if (user.country?.isNotEmpty == true) {
      extData['country'] = user.country as String;
    }
    if (user.memberCode?.isNotEmpty == true) {
      extData['memberCode'] = user.memberCode as String;
    }
    if (user.firstName?.isNotEmpty == true) {
      extData['firstName'] = user.firstName as String;
    }
    if (user.lastName?.isNotEmpty == true) {
      extData['lastName'] = user.lastName as String;
    }

    if (extData.isEmpty) return null;

    return extData.entries.map((e) => '${e.key}:${e.value}').join('|');
  }

  /// Dispose resources
  @override
  void onClose() {
    _contactCache.clear();
    _blockedUsers.clear();
    super.onClose();
  }
}
