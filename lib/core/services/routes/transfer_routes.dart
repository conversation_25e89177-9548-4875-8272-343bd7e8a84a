part of 'route.dart';

final transferRoutes = [
  // Transfer History and Reciept Routes
  GoRoute(
    name: AppRouteName.transactions,
    path: AppRouteName.transactions,
    pageBuilder: (context, state) => _pageBuilder(
      BlocProvider(
        create: (context) => sl<TransactionBloc>(),
        child: const TransactionsScreen(),
      ),
      state,
    ),
    routes: [
      GoRoute(
        name: AppRouteName.transactionDetails,
        path: ':id',
        pageBuilder: (context, state) {
          final extra = state.extra != null
              ? state.extra as Map<String, dynamic>
              : {'isValidating': false};
          return _pageBuilder(
            MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => sl<TransactionDetailsBloc>(),
                ),
                BlocProvider(
                  create: (context) => sl<TransactionBloc>(),
                ),
              ],
              child: TransactionDetailsScreen(
                transactionId: state.pathParameters['id']!,
                isValidatingTransaction: extra['isValidating'] != null
                    ? extra['isValidating'] as bool
                    : false,
              ),
            ),
            state,
          );
        },
      ),
    ],
  ),

  // Load Wallet Routes
  GoRoute(
    name: AppRouteName.loadToWallet,
    path: AppRouteName.loadToWallet,
    pageBuilder: (context, state) => _pageBuilder(
      const LoadToWalletPage(),
      state,
    ),
    routes: [
      GoRoute(
        name: AppRouteName.loadToWalletWebView,
        path: AppRouteName.loadToWalletWebView,
        pageBuilder: (context, state) => _pageBuilder(
          LoadToWalletWebView(
            url: state.uri.queryParameters['url'] ?? '',
            redirectURL: state.uri.queryParameters['redirectURL'] ?? '',
            billRefNo: state.uri.queryParameters['billRefNo'] ?? '',
          ),
          state,
        ),
      ),
      GoRoute(
        name: AppRouteName.failurePage,
        path: AppRouteName.failurePage,
        pageBuilder: (context, state) => _pageBuilder(
          const FailurePage(),
          state,
        ),
      ),
    ],
  ),

  // Send Money Routes with Bank Transfer
  GoRoute(
    name: AppRouteName.sendMoney,
    path: 'send-money/:currency',
    pageBuilder: (context, state) => _pageBuilder(
      MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => sl<BankTransferBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<TransactionBloc>(),
          ),
        ],
        child: SendMoneyScreen(
          currency: state.pathParameters['currency'] == 'usd'
              ? TransferCurrency.usd
              : TransferCurrency.etb,
        ),
      ),
      state,
    ),
    routes: [
      GoRoute(
        name: AppRouteName.recipientAccountNumberBankTransfer,
        path: 'recipient-account-number-bank-transfer',
        pageBuilder: (context, state) => _pageBuilder(
          MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => sl<BankTransferBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<BankTransferRecentTransactionBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<TransactionBloc>(),
              ),
            ],
            child: RecipientAccountNumberScreen(
              isBirrTransfer: (state.extra as Map)['isBirrTransfer'] as bool,
              bank: (state.extra as Map)['bank'] as Bank,
            ),
          ),
          state,
        ),
      ),
      GoRoute(
        name: AppRouteName.addAmountBankTransfer,
        path: 'add-amount-bank-transfer',
        pageBuilder: (context, state) => _pageBuilder(
          MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => sl<BankTransferBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<TransactionBloc>(),
              ),
              BlocProvider(
                create: (context) => sl<BankTransferRecentTransactionBloc>(),
              ),
            ],
            child: OtherBankAddAmountScreen(
              senderName: (state.extra as Map)['senderName'] as String,
              recipientName: (state.extra as Map)['recipientName'] as String,
              recipientAccount:
                  (state.extra as Map)['recipientAccount'] as String,
              isBirrTransfer: state.pathParameters['currency'] == 'etb',
              bank: (state.extra as Map)['bank'] as Bank,
              exchangeRate: state.pathParameters['currency'] == 'etb'
                  ? null
                  : (state.extra as Map)['exchangeRate'] as double?,
            ),
          ),
          state,
        ),
      ),

      // Wallet Transfer Routes
    ],
  ),

  GoRoute(
    name: AppRouteName.walletTransferPage,
    path: 'wallet-transfer-page/:currency',
    pageBuilder: (context, state) => _pageBuilder(
      MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => sl<WalletTransferBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<TransactionBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<BankTransferBloc>(),
          ),
          BlocProvider.value(
            value: context.read<RecentWalletTransferBloc>(),
          ),
        ],
        child: const TransferToWalletPage(),
      ),
      state,
    ),
  ),
  GoRoute(
    name: AppRouteName.walletTransferAddAmount,
    path: '${AppRouteName.walletTransferAddAmount}/:currency',
    pageBuilder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final currency = state.pathParameters['currency'] ?? 'usd';
      return _pageBuilder(
        MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: context.read<WalletTransferBloc>(),
            ),
            BlocProvider.value(
              value: context.read<TransactionBloc>(),
            ),
            BlocProvider.value(
              value: context.read<RecentWalletTransferBloc>(),
            ),
          ],
          child: WalletTransferAddAmount(
            isFromQuick: extra['isFromQuick'] as bool,
            recipent: extra['recipent'] != null
                ? extra['recipent'] as RecentRecipient
                : null,
       
            memberInfo: extra['memberInfo'] != null
                ? extra['memberInfo'] as MemberLookupResponse
                : null,
            ignoreAmountCheck: extra['ignoreAmountCheck'] != null
                ? extra['ignoreAmountCheck'] as bool
                : false,
            isFromChat: extra['isFromChat'] != null
                ? extra['isFromChat'] as bool
                : false,
            chatContext: extra['chatContext'] != null
                ? extra['chatContext'] as Map<String, dynamic>
                : null,
            currencyType:
                currency == 'usd' ? CurrencyType.usd : CurrencyType.etb,
          ),
        ),
        state,
      );
    },
  ),

  GoRoute(
    name: AppRouteName.changeToBirr,
    path: AppRouteName.changeToBirr,
    pageBuilder: (context, state) => _pageBuilder(
      MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => sl<ChangeToBirrBloc>(),
          ),
          BlocProvider(
            create: (context) => sl<TransactionBloc>(),
          ),
        ],
        child: const ChangeToBirrAddAmount(),
      ),
      state,
    ),
  ),
];
