class AppRouteName {
  AppRouteName._();

  // Root Routes

  static const String onboarding = '/';

  static const String forceUpdate = '/force_update';


  static const String home = '/main';
  static const String authRoot = '/auth';
  static const String connectionLost = '/connection-lost';

  // Authentication Routes
  static const String signIn = '$authRoot/sign-in';
  static const String signUp = '$authRoot/sign-up';
  static const String verifyEmail = '$authRoot/verify-email';
  static const String verifyOtp = '$authRoot/verify-otp';
  static const String createPin = '$authRoot/create-pin';
  static const String confirmPin = '$authRoot/confirm-pin';

  static const String guestBankListScreen = '/guest-bank-screen';
  static const String guestRecipeintScreen = '/guest-recipient-screen';
  static const String guestAddAmountScreen = '/guest-add-amount';
  static const String guestWebViewScreen = '/guest-web-view';
  static const String guestHomePage = '/guest-home-page';

  static const String tokenDeviceLogin = '/token-device-login';
  static const String changePin = 'change-pin';
  static const String forgotPin = '/forgot-pin';

  // Main Navigation Routes
  static const String notifications = 'notifications';
  static const String notificationDetails = 'notification-details';
  static const String contacts = 'contacts';

  // Profile & Settings Routes
  static const String profileInfo = 'profile-info';
  static const String updatePin = 'update-pin';
  static const String addPhone = 'add-phone';
  static const String addEmail = 'add-email';
  static const String profilePhoto = 'profile-photo';

  static const String customerSupport = 'customer-support';
  static const String faq = 'faq';
  static const String privacyPolicy = 'privacy-policy';
  static const String termsAndConditions = 'terms-and-conditions';

  static const String verifyAddedPhone = 'verify-phone';
  static const String verifyAddedEmail = 'verify-email';

  // Car Loan Routes
  static const String carLoan = 'car-loan';

  // Bank Transfer Routes
  static const String sendMoney = 'send-money-bank-transfer';
  static const String addAmountBankTransfer = 'add-amount-bank-transfer';
  static const String confirmationSendBankTransfer =
      'confirmation-send-bank-transfer';
  static const String successSendBankTransfer = 'success-send-bank-transfer';
  static const String bankTransferOtpConfirmation =
      'bank-transfer-otp-confirmation';

  static const String recipientAccountNumberBankTransfer =
      'recipient-account-number-bank-transfer';

  // Wallet Transfer Routes
  static const String walletTransferPage = 'wallet-transfer-page';
  static const String walletTransferAddAmount = 'wallet-transfer-add-amount';

  // USD Transaction Routes
  static const String sendUsd = 'send-usd';
  static const String addAmount = 'add-amount';
  static const String confirmationSendUsd = 'confirmation-send-usd';
  static const String transferToWalletUsd = 'transfer-to-wallet-usd';
  static const String addAmountTransfer = 'add-amount-transfer';
  static const String confirmWalletTransfer = 'confirm-wallet-transfer';
  static const String confirmTransfer = 'confirm-transfer';
  static const String transferSuccess = 'transfer-success';

  // ETB/Birr Transaction Routes
  static const String sendEtb = 'send-etb';
  static const String addAmountEtb = 'add-amount-etb';
  static const String confirmationSendEtb = 'confirmation-send-etb';
  static const String transferToWalletBirr = 'transfer-to-wallet-birr';
  static const String addAmountTransferBirr = 'add-amount-transfer-birr';
  static const String confirmTransferBirr = 'confirm-transfer-birr';
  static const String transferSuccessBirr = 'transfer-success-birr';

  // Currency Exchange Routes
  static const String changeToBirr = 'change-to-birr';
  static const String confirmChangeToBirr = 'confirm-change-to-birr';
  static const String changeSuccess = 'change-success';
  static const String quickWalletTransfer = 'quick-wallet-transfer';

  // Wallet & Payment Routes
  static const String loadToWallet = 'load-to-wallet';
  static const String loadToWalletWebView = 'load-to-wallet-web-view';

  static const String paymentPage = 'payment-page';
  static const String successPage = 'success-page';
  static const String failurePage = 'failure-page';
  static const String sendMoneySuccess = 'sendMoneySuccess';

  // Transactions Routes
  static const String transactions = 'transactions';
  static const String transactionDetails = ':id';
  static const String validateTransaction = 'validate-transaction';

  // Birr Transaction Routes
  static const String addAmountBirr = 'add-amount-birr';

  static const confirmWalletTransferBirr = 'confirm-wallet-transfer-birr';
  static const transferWalletEtbSuccessBirr =
      'transfer-wallet-etb-success-birr';

  // Quick Wallet Transfer Routes
  static const String addAmountUsdQuickWallet = 'add-amount-usd-quick-wallet';
  static const String addAmountBirrQuickWallet = 'add-amount-birr-quick-wallet';
  static const String confirmQuickWalletTransfer =
      'confirm-quick-wallet-transfer';

  // Guest routes
  static const guestSendMoney = 'guestSendMoney';

  static const String recipientDetails = 'recipientDetails';

  static const String chatContactList = 'chat-contact-list';
  static const String chatUserDetail = 'chat-user-detail';
  static const String chatProfileDetails = 'chat-profile-details';
  static const String chatFullScreenImageViewer =
      'chat-full-screen-image-viewer';
  static const String chatAddAmount = 'chat-add-amount';
  static const String ChatSendMoneyConfirmationScreen =
      'chat-send-money-confirmation-screen';
  static const String ChatSendMoneySuccessScreen =
      'chat-send-money-success-screen';
  static const String searchMembersScreen = 'search-members-screen';
  static const String chatRequestAddAmount = 'chat-request-add-amount';

  // my loan

  static const loanDetailPage = 'loan-detail';
  static const payLoan = 'pay-loan';
  static const confirmRepay = 'confirm-repay';

  static const successRepay = 'success-repay';
  static const failureRepay = 'failure-repay';

  static const paymentHistoryDetailPage = 'success-repay';

  static const paymentDetail = 'payment-detail';

  // Car Loan Routes
  static const String loanPaymentPeriod = 'loan-payment-period';
  static const String loanTermsAndConditions = 'loan-terms-and-conditions';

  // Loans Routes
  static const String loansApplication = 'loans-application';
  static const String carLoanRoute = 'car_loan';
  static const String mortgageRoute = 'mortgage';
  static const String loanItemDetails = 'loan-item-details';
  static const String loanBankInfo = 'loan-bank-info';
  static const String virtualTourWebView = 'virtual-tour-webview';
  static const String documentForLoan = 'document-for-loan';
  static const String loanTermsConditions = 'loan-terms-condition';

  // Mortgage Loan Routes
  static const String mortgageLoanPage = 'mortgage-loan-page';
  static const String mortgagePropertyDetails = 'mortgage-property-details';
  static const String mortgageLoanDetails = 'mortgage-loan-details';
  static const String requiredDocumentsForMortgageLoan =
      'required-documents-for-mortgage-loan';
  static const String mortgageWebView = 'mortgage-web-view';
  static const String mortgageLoanTermsConditions = 'mortgage-loan-terms';
  static const String mortgageLoanConfirmation = 'mortgage-loan-confirmation';
  static const String mortgageLoanSuccess = 'mortgage-loan-success';

  static const String donations = 'donations';
  static const String donationDetails = 'donation-details';
  static const String donationAmount = 'donation-amount';
  static const String donationConfirm = 'donation-confirm';
  static const String donationSuccess = 'donation-success';
  static const String moneyRequestDetails = 'money-request-details';

  static const String carLoanSuccess = 'car-loan-success';
  static const String carLoanConfirmation = 'car-loan-confirmation';
  static const String carDetails = 'car-details';

  static const String giftPackages = 'gift-packages';
  static const String packageDetail = 'package-detail';
  static const String packageSearch = 'package-search';
  static const String merchantPackages = 'merchant-packages';
  static const String giftRecipientDetails = 'recipient-details';
  static const String confirmPurchase = 'confirm-purchase';
  static const String purchaseSuccess = 'purchase-success';

  static const String guestCarList = 'guest-car-list';
  static const String guestCarDetails = 'guest-car-details';

  static const String guestRecipientAccount = 'guest-recipient-account';
  static const String guestAddAmount = 'guest-add-amount';
  static const String guestConfirmation = 'guest-confirmation';

  static const String guestTransferSuccess = 'guest-transfer-success';
  static const String guestFailure = 'guest-transfer-failure';

  static const String commingsoon = 'coming-soon';

  // Chat Routes
  static const String conversations = 'conversations';
  static const String chatInterface = 'chat-interface';
  static const String newConversation = 'new-conversation';
  static const String chatContacts = 'chat-contacts';
  static const String chatSearch = 'chat-search';
  static const String blockedContacts = 'blocked-contacts';

  // merchant payment flow
  static const String merchantPayment = 'merchant-payment';
  static const String merchantPaymentAddAmount = 'merchant-payment-add-amount';
  static const String merchantPaymentByQrAddAmount =
      'merchant-payment-by-qr-add-amount';
  static const String merchantPaymentOtpConfirmation =
      'merchant-payment-otp-confirmation';
  // static const String merchantPaymentSuccess = 'merchant-payment-success';
  static const String merchantPaymentByQr = 'merchant-payment-by-qr';

  // Add Money Routes

  static const String addMoney = 'add-money';
  static const String addMoneyConfirmation = 'add-money-confirmation';
  static const String addMoneySuccess = 'add-money-success';

  // View Balance Route
  static const String balanceCheck = 'balance-check';

  // Add this with other route names
  static const String transactionDetail = 'transaction-detail';
  static const String balanceHistory = 'balance-history';
  static const String addMoneyOtpConfirmation = 'add-money-otp-confirmation';
  static const String linkAccountMenu = 'link_account_menu';
  static const String linkBankList = 'link_bank_list';
  static const String linkedBanks = 'linked_banks';
  static const String linkAccountForm = 'link_account_form';
  static const String linkedAccountDetail = 'linked_account_detail';

  // cash in cash out routes
  static const String cashInOut = 'cash-in-out';
  static const String cashIn = 'cash-in';
  static const String cashInSuccess = 'cash-in-success';
  static const String cashInQrSuccess = 'cash-in-qr-success';
  static const String cashOut = 'cash-out';
  static const String cashOutAgent = 'cash-out-agent';
  static const String cashOutVoucher = 'cash-out-voucher';
  static const String cashOutQr = 'cash-out-qr';
  static const String cashOutAgentAddAmount = 'cash-out-agent-add-amount';
  static const String cashOutConfirmation = 'cash-out-confirmation';
  static const String cashOutOtpConfirmation = 'cash-out-otp-confirmation';
  static const String cashOutSuccess = 'cash-out-success';

  // Agent Locator
  static const String agentLocator = 'agent-locator';
  static const String agentLocatorComing = 'agentLocatorComing';

  static const String topUpMenu = 'toUpMenu';
  static const String topUpContact = 'topUpContact';
  static const String topUpAddAmount = 'topUpAddAmount';
  static const String topUpConfrim = 'topUpConfrim';
  static const String topUpConfirmOtp = 'topUpConfirmOtp';
  static const String topUpSuccess = 'topUpSuccess';

  static const String moneyRequestMenuScreen = 'money-request-menu-screen';
  static const String sendMoneyRequestMemberLookupScreen =
      'send-money-request-lookup-screen';
  static const String myMoneyRequestListScreen = 'my-money-request-list-screen';
  static const String moneyRequestedListScreen = 'money-requested-list-screen';
  static const String moneyRequestCheckAmountScreen =
      'money-request-check-amount-screen';
  static const String moneyRequestConfirmRequestScreen =
      'money-request-confirm-request-screen';
  static const String sendMoneyRequestAddMoneyScreen =
      'send-money-request-add-money-screen';
  static const String sendRequestMoneyConfirmRequestScreen =
      'send-request-money-confirm-request-screen';
  static const String sendMoneyRequestSuccessScreen =
      'send-money-request-success-screen';
  static const String requestDetailScreen = 'request-detail-screen';

  static const String carLoanApplication = 'car-loan-application';

  static const String requiredDocumentsForCarLoan =
      'required-documents-for-car';
  static const String carLoanTermsConditions = 'car-loan-terms-conditions';

  // Chat
  static const String chat = 'chat';
  static const String chatContactListScreen = 'chat-contact-list-screen';
  static const String chatMessageDetailScreen = 'chat-message-detail-screen';
  static const String chatSendMoneyRequestAddAmountScreen =
      'chat-send-money-request-add-amount-screen';

  static const String chatMoneyRequestDetailScreen =
      'chat-money-request-detail-screen';
  static const String chatSendRequestMoneyConfirmRequestScreen =
      'chat-send-request-money-confirm-request-screen';

  static const String chatMoneyRequestConfirmRequestScreen =
      'chat-money-request-confirm-request-screen';
  static const String chatMoneyRequestCheckAmountScreen =
      'chat-money-request-check-amount-screen';

  static const videoCall = '/video-call';

  static const String chatWalletTransferPage = 'chat-wallet-transfer-page';
  static const String chatWalletTransferAddAmount =
      'chat-wallet-transfer-add-amount';
  static const String chatConfirmWalletTransfer =
      'chat-confirm-wallet-transfer';
  static const String chatTransferSuccess = 'chat-transfer-success';

  static const String qrOptions = 'qr-options';
  static const String qrWalletTransferAddAmount =
      'qr-wallet-transfer-add-amount';
  // static const String chatTransferSuccess = 'chat-transfer-success';
  static const String utilityScreen = 'utitlity-screen';
  static const String utilityWebView = 'utitlity-web-view';
  static const String utilitySuccess = 'utility-success';
  // Miniapp
  static const String miniappScreen = 'miniapp-screen';
  static const String miniappWebView = 'miniapp-web-view';

  static const String miniappConfirmation = 'miniapp-confirmation';
  static const String miniappSuccess = 'miniapp-success';

  static const String identityVerification = 'identity-verification';
  static const String complyCubeVerification = 'complycube-verification';
  static const String faceScanCamera = 'face-scan-camera';
  static const String documentScan = 'document-scan';

  static const String miniStatements = 'mini-statements';
}
