part of 'route.dart';

final loanRoutes = [
  // Virtual Tour WebView route
  GoRoute(
    name: AppRouteName.virtualTourWebView,
    path: '/virtual-tour-webview',
    builder: (context, state) => VirtualTourWebView(
      url: state.uri.queryParameters['url'] ?? '',
    ),
  ),

  // Document for loan route
  GoRoute(
    name: AppRouteName.documentForLoan,
    path: '/document-for-loan',
    pageBuilder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final bank = extra['bank'] as LoanBank;
      final selectedUpfrontPayment = extra['selectedUpfrontPayment'] as String;
      final loanItem = extra['loanItem'] as LoanItem;

      return _pageBuilder(
        DocumentForLoanPage(
          bank: bank,
          selectedUpfrontPayment: selectedUpfrontPayment,
          loanItem: loanItem,
        ),
        state,
      );
    },
  ),

  GoRoute(
    name: AppRouteName.loanTermsAndConditions,
    path: AppRouteName.loanTermsAndConditions,
    pageBuilder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final bank = extra['bank'] as LoanBank;
      final selectedUpfrontPayment = extra['selectedUpfrontPayment'] as String;
      final loanItem = extra['loanItem'] as LoanItem;

      return _pageBuilder(
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => sl<LoanTermsCubit>(),
            ),
            BlocProvider(
              create: (context) => sl<LoanItemsBloc>(),
            ),
          ],
          child: LoanTermsAndConditionsPage(
            bank: bank,
            selectedUpfrontPayment: selectedUpfrontPayment,
            loanItem: loanItem,
          ),
        ),
        state,
      );
    },
  ),
  //loanTermsConditions

  // Unified loan item detail route
  GoRoute(
    name: AppRouteName.loanItemDetails,
    path: '/loan-item-details',
    pageBuilder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final loanItem = extra['loanItem'] as LoanItem;
      final isGuest = extra['isGuest'] as bool? ?? false;

      return _pageBuilder(
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => sl<LoanItemsBloc>(),
            ),
          ],
          child: LoanItemDetailPage(
            loanItem: loanItem,
            isGuest: isGuest,
          ),
        ),
        state,
      );
    },
  ),

  // Loan bank info route
  GoRoute(
    name: AppRouteName.loanBankInfo,
    path: '/loan-bank-info',
    pageBuilder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final loanItem = extra['loanItem'] as LoanItem;

      return _pageBuilder(
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => sl<LoanBanksCubit>(),
            ),
            BlocProvider(
              create: (_) => sl<LoanPaymentInfoCubit>(),
            ),
          ],
          child: LoanBankInfoPage(
            loanItem: loanItem,
          ),
        ),
        state,
      );
    },
  ),
  // New unified loans application routes - moved to top level for direct
  // navigation to fix "unknown route name" errors
  GoRoute(
    name: AppRouteName.carLoanRoute,
    path: 'car_loan',
    pageBuilder: (context, state) {
      final isGuest = state.extra is Map
          ? (state.extra as Map)['isGuest'] as bool? ?? false
          : false;

      return _pageBuilder(
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => sl<LoanCategoriesCubit>(),
            ),
            BlocProvider(
              create: (_) => sl<LoanItemsBloc>(),
            ),
          ],
          child: LoansApplicationPage(
            loanType: 'car_loan',
            isGuest: isGuest,
          ),
        ),
        state,
      );
    },
    routes: [
      // Loan item details as a sub-route of car loan
      GoRoute(
        name: '${AppRouteName.carLoanRoute}_${AppRouteName.loanItemDetails}',
        path: AppRouteName.loanItemDetails,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final loanItem = extra['loanItem'] as LoanItem;
          final isGuest = extra['isGuest'] as bool? ?? false;

          return _pageBuilder(
            MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (_) => sl<LoanItemsBloc>(),
                ),
              ],
              child: LoanItemDetailPage(
                loanItem: loanItem,
                isGuest: isGuest,
              ),
            ),
            state,
          );
        },
      ),
      GoRoute(
        name: '${AppRouteName.carLoanRoute}_${AppRouteName.loanBankInfo}',
        path: AppRouteName.loanBankInfo,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final loanItem = extra['loanItem'] as LoanItem;

          return _pageBuilder(
            MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (_) => sl<LoanBanksCubit>(),
                ),
                BlocProvider(
                  create: (_) => sl<LoanPaymentInfoCubit>(),
                ),
              ],
              child: LoanBankInfoPage(
                loanItem: loanItem,
              ),
            ),
            state,
          );
        },
      ),
      // Document for loan as a sub-route of car loan
      GoRoute(
        name: '${AppRouteName.carLoanRoute}_${AppRouteName.documentForLoan}',
        path: AppRouteName.documentForLoan,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final bank = extra['bank'] as LoanBank;
          final selectedUpfrontPayment =
              extra['selectedUpfrontPayment'] as String;
          final loanItem = extra['loanItem'] as LoanItem;

          return _pageBuilder(
            DocumentForLoanPage(
              bank: bank,
              selectedUpfrontPayment: selectedUpfrontPayment,
              loanItem: loanItem,
            ),
            state,
          );
        },
      ),
    ],
  ),
  GoRoute(
    name: AppRouteName.mortgageRoute,
    path: 'mortgage',
    pageBuilder: (context, state) {
      final isGuest = state.extra is Map
          ? (state.extra as Map)['isGuest'] as bool? ?? false
          : false;

      return _pageBuilder(
        MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => sl<LoanCategoriesCubit>(),
            ),
            BlocProvider(
              create: (_) => sl<LoanItemsBloc>(),
            ),
          ],
          child: LoansApplicationPage(
            loanType: 'mortgage',
            isGuest: isGuest,
          ),
        ),
        state,
      );
    },
    routes: [
      // Loan item details as a sub-route of mortgage
      GoRoute(
        name: '${AppRouteName.mortgageRoute}_${AppRouteName.loanItemDetails}',
        path: AppRouteName.loanItemDetails,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final loanItem = extra['loanItem'] as LoanItem;
          final isGuest = extra['isGuest'] as bool? ?? false;

          return _pageBuilder(
            MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (_) => sl<LoanItemsBloc>(),
                ),
              ],
              child: LoanItemDetailPage(
                loanItem: loanItem,
                isGuest: isGuest,
              ),
            ),
            state,
          );
        },
      ),
      // Loan bank info as a sub-route of mortgage
      GoRoute(
        name: '${AppRouteName.mortgageRoute}_${AppRouteName.loanBankInfo}',
        path: AppRouteName.loanBankInfo,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final loanItem = extra['loanItem'] as LoanItem;

          return _pageBuilder(
            MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (_) => sl<LoanBanksCubit>(),
                ),
                BlocProvider(
                  create: (_) => sl<LoanPaymentInfoCubit>(),
                ),
              ],
              child: LoanBankInfoPage(
                loanItem: loanItem,
              ),
            ),
            state,
          );
        },
      ),
      // Document for loan as a sub-route of mortgage
      GoRoute(
        name: '${AppRouteName.mortgageRoute}_${AppRouteName.documentForLoan}',
        path: AppRouteName.documentForLoan,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final bank = extra['bank'] as LoanBank;
          final selectedUpfrontPayment =
              extra['selectedUpfrontPayment'] as String;
          final loanItem = extra['loanItem'] as LoanItem;

          return _pageBuilder(
            DocumentForLoanPage(
              bank: bank,
              selectedUpfrontPayment: selectedUpfrontPayment,
              loanItem: loanItem,
            ),
            state,
          );
        },
      ),
    ],
  ),
  // Parent loans application route (keeping for potential future use)
  GoRoute(
    name: AppRouteName.loansApplication,
    path: 'loans-application',
    pageBuilder: (context, state) => _pageBuilder(
      MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (_) => sl<LoanCategoriesCubit>(),
          ),
          BlocProvider(
            create: (_) => sl<LoanItemsBloc>(),
          ),
        ],
        child: const LoansApplicationPage(loanType: 'general'),
      ),
      state,
    ),
  ),
  // Existing car loan routes (keeping for backward compatibility)
];
