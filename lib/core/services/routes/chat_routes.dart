part of 'route.dart';

final chatRoutes = [
  // Chat Routes
  GoRoute(
    name: AppRouteName.conversations,
    path: 'conversations',
    pageBuilder: (context, state) => _pageBuilder(
      ProtectedRoute(
        child: BlocProvider(
          create: (context) => sl<ChatBloc>(),
          child: const ConversationsPage(),
        ),
      ),
      state,
    ),
  ),
  GoRoute(
    name: AppRouteName.chatInterface,
    path: 'chat-interface/:conversationId',
    pageBuilder: (context, state) {
      final conversationId = state.pathParameters['conversationId']!;

      // Extract extra parameters if provided
      final extra = state.extra as Map<String, dynamic>?;
      final conversation = extra?['conversation'] as ConversationEntity?;
      final userAttributes = extra?['userAttributes'] as UserAttributesEntity?;

      return _pageBuilder(
        ProtectedRoute(
          child: BlocProvider(
            create: (context) => sl<ChatBloc>(),
            child: ChatInterfacePage(
              conversationId: conversationId,
              conversation: conversation,
              userAttributes: userAttributes,
            ),
          ),
        ),
        state,
      );
    },
  ),
  GoRoute(
    name: AppRouteName.newConversation,
    path: 'new-conversation',
    pageBuilder: (context, state) => _pageBuilder(
      ProtectedRoute(
        child: BlocProvider(
          create: (context) => sl<ChatBloc>(),
          child: const NewConversationPage(),
        ),
      ),
      state,
    ),
  ),
  GoRoute(
    name: AppRouteName.chatSearch,
    path: 'chat-search',
    pageBuilder: (context, state) => _pageBuilder(
      ProtectedRoute(
        child: BlocProvider(
          create: (context) => sl<ChatBloc>(),
          child: const GlobalChatSearchPage(),
        ),
      ),
      state,
    ),
  ),
  GoRoute(
    name: AppRouteName.blockedContacts,
    path: 'blocked-contacts',
    pageBuilder: (context, state) => _pageBuilder(
      ProtectedRoute(
        child: BlocProvider(
          create: (context) => sl<ChatBloc>(),
          child: const BlockedContactsPage(),
        ),
      ),
      state,
    ),
  ),
];
