part of 'route.dart';

final navigationService = NavigationService(
  hiveBoxManager: HiveBoxManager(),
  authLocalDataSource: sl<AuthLocalDataSource>(),
);

final GoRouter router = GoRouter(
  navigatorKey: mainNavigatorKey,
  initialLocation: '/splashLoadingScreen',
  redirect: (context, state) {
    debugPrint('Redirecting to from the main rout 🦲 ${state.matchedLocation}');
    return navigationService.handleRedirect(state.matchedLocation);
  },
  routes: [
    authRoutes,
    GoRoute(
      path: '/splashLoadingScreen',
      name: '/splashLoadingScreen',
      pageBuilder: (context, state) => _pageBuilder(
        const SplashLoadingScreen(),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.forceUpdate,
      name: AppRouteName.forceUpdate,
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider.value(
          value: context.read<InAppUpdateBloc>(),
          child: const InAppUpdateView(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.onboarding,
      name: AppRouteName.onboarding,
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider.value(
          value: sl<OnBoardingCubit>(),
          child: const OnBoardingScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.createPin,
      name: AppRouteName.createPin,
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(
          CreateNewPinScreen(
            source: (state.extra as Map)['source'] as String,
          ),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.confirmPin,
      name: AppRouteName.confirmPin,
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(
          CreateConfirmPinScreen(
            source: (state.extra as Map)['source'] as String,
            newPin: (state.extra as Map)['newPin'] as String,
          ),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.tokenDeviceLogin,
      name: AppRouteName.tokenDeviceLogin,
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider.value(
          value: sl<AuthBloc>(),
          child: const TokenDeviceLoginScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: '/connection-lost',
      name: '/connection-lost',
      pageBuilder: (context, state) {
        return _pageBuilder(
          const ConnectionLostScreen(),
          state,
        );
      },
    ),
    GoRoute(
      name: AppRouteName.forgotPin,
      path: '/forgot-pin',
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider(
          create: (context) => sl<AuthBloc>(),
          child: const ForgotPasswordScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.guestHomePage,
      name: AppRouteName.guestHomePage,
      pageBuilder: (context, state) => _pageBuilder(
        const GuestRateScreen(),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.guestBankListScreen,
      name: AppRouteName.guestBankListScreen,
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider(
          create: (context) => sl<GuestBloc>(),
          child: const GuestSendMoneyScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.guestRecipeintScreen,
      name: AppRouteName.guestRecipeintScreen,
      pageBuilder: (context, state) {
        final extra = state.extra as Map<String, dynamic>;
        return _pageBuilder(
          BlocProvider(
            create: (context) => sl<GuestBloc>(),
            child: GuestRecipientAccountNumberScreen(
              bank: extra['bank'] as Bank,
            ),
          ),
          state,
        );
      },
    ),
    GoRoute(
      path: AppRouteName.guestAddAmountScreen,
      name: AppRouteName.guestAddAmountScreen,
      pageBuilder: (context, state) {
        final extra = state.extra as Map<String, dynamic>;
        return _pageBuilder(
          BlocProvider(
            create: (context) => sl<GuestBloc>(),
            child: GuestAddAmountScreen(
              senderName: extra['senderName'] as String,
              recipientName: extra['recipientName'] as String,
              recipientAccount: extra['recipientAccountNumber'] as String,
              bank: extra['bank'] as Bank,
              reason: extra['reason'] as String?,
            ),
          ),
          state,
        );
      },
    ),
    GoRoute(
      path: AppRouteName.guestWebViewScreen,
      name: AppRouteName.guestWebViewScreen,
      pageBuilder: (context, state) {
        final extra = state.extra as Map<String, dynamic>;
        return _pageBuilder(
          BlocProvider(
            create: (context) => sl<GuestBloc>(),
            child: GuestWebViewScreen(
              url: extra['url'] as String,
              redirectURL: extra['redirectURL'] as String,
              billRefNo: extra['billRefNo'] as String,
            ),
          ),
          state,
        );
      },
    ),
    GoRoute(
  path: '/compromised-device',
  name: 'compromised-device',
  pageBuilder: (context, state) => _pageBuilder(
    const CompromisedDevicePage(),
    state,
  ),
),
    /*
    GoRoute(
      path: AppRouteName.guestBankListScreen,
      name: AppRouteName.guestBankListScreen,
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(
          const GuestSendMoneyScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.guestBankListScreen,
      name: AppRouteName.guestBankListScreen,
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(
          const GuestSendMoneyScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.guestBankListScreen,
      name: AppRouteName.guestBankListScreen,
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(
          const GuestSendMoneyScreen(),
        ),
        state,
      ),
    ),

    */
    ShellRoute(
      builder: (context, state, child) => MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: sl<AuthBloc>(),
          ),
        ],
        child: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) async {},
          child: child,
        ),
      ),
      routes: [
        GoRoute(
          path: AppRouteName.home,
          pageBuilder: (context, state) => _pageBuilder(
            const MainPage(),
            state,
          ),
          routes: _protectedRoutes,
        ),
      ],
    ),
  ],
  errorBuilder: (context, state) => const ErrorScreen(),
);

CustomTransitionPage<dynamic> _pageBuilder(Widget page, GoRouterState state) {
  return CustomTransitionPage(
    key: state.pageKey,
    child: page,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      // Shared element transition for logo
      if (state.matchedLocation == AppRouteName.signUp ||
          state.matchedLocation == AppRouteName.signIn) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.1),
              end: Offset.zero,
            ).animate(
              CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              ),
            ),
            child: child,
          ),
        );
      }

      // Default transition
      return FadeTransition(
        opacity: CurveTween(curve: Curves.easeInOut).animate(animation),
        child: child,
      );
    },
    transitionDuration: const Duration(milliseconds: 400),
  );
}
