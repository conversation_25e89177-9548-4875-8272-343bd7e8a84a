part of 'route.dart';

final authRoutes = GoRoute(
  path: AppRouteName.authRoot,
  builder: (context, state) => _wrapWithAuthBloc(const SignInScreen()),
  routes: [
    GoRoute(
      path: 'sign-in',
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(const SignInScreen()),
        state,
      ),
    ),
    GoRoute(
      path: 'sign-up',
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(const SignUpScreen()),
        state,
      ),
    ),
    GoRoute(
      path: 'forgot-pin',
      name: 'forgotPin',
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(const ForgotPasswordScreen()),
        state,
      ),
    ),
  
    GoRoute(
      name: AppRouteName.guestSendMoney,
      path: 'guest-send-money',
      pageBuilder: (context, state) => _pageBuilder(
        const GuestSendMoneyScreen(),
        state,
      ),
    ),
    GoRoute(
      path: 'verify-email',
      name: 'verifyEmail',
      pageBuilder: (context, state) {
        debugPrint('State ${state.extra}');
        return _pageBuilder(
          _wrapWithAuthBloc(
            EmailVerificationScreen(
              email: (state.extra as Map)['email'] as String,
              source: (state.extra as Map)['source'] as String,
              otp: (state.extra as Map)['otp'] != null
                  ? (state.extra as Map)['otp'] as String
                  : '',
            ),
          ),
          state,
        );
      },
    ),
    GoRoute(
      path: 'verify-otp',
      name: 'verifyOtp',
      pageBuilder: (context, state) => _pageBuilder(
        _wrapWithAuthBloc(
          OTPVerificationScreen(
            phoneNumber: (state.extra as Map)['phoneNumber'] as String,
            source: (state.extra as Map)['source'] as String,
          ),
        ),
        state,
      ),
    ),

    //   GoRoute(
    //   path: 'create-pin',
    //   name: 'create-pin',
    //   pageBuilder: (context, state) => _pageBuilder(
    //     _wrapWithAuthBloc(
    //         CreateNewPinScreen(
    //         source: (state.extra as Map)['source'] as String,
    //       ),
    //     ),
    //     state,
    //   ),
    // ),
    //  GoRoute(
    //   path: 'confirm-pin',
    //   name: 'confirm-pin',
    //   pageBuilder: (context, state) => _pageBuilder(
    //     _wrapWithAuthBloc(
    //         CreateNewPinScreen(
    //         source: (state.extra as Map)['source'] as String,
    //       ),
    //     ),
    //     state,
    //   ),
    // ),

    // GoRoute(
    //   path: 'create-pin',
    //   name: 'createPin',
    //   pageBuilder: (context, state) => _pageBuilder(
    //     _wrapWithAuthBloc(
    //       CreatePinScreen(
    //         source: (state.extra as Map)['source'] as String,
    //       ),
    //     ),
    //     state,
    //   ),
    // ),
   
  
    // GoRoute(
    //   name: AppRouteName.guestHomePage,
    //   path: AppRouteName.guestHomePage,
    //   pageBuilder: (context, state) => _pageBuilder(
    //     const GuestRateScreen(),
    //     state,
    //   ),
    // ),
  ],
);

Widget _wrapWithAuthBloc(Widget page) {
  return BlocProvider(
    create: (_) => sl<AuthBloc>(),
    child: page,
  );
}
