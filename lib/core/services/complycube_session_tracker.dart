import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Service to track ComplyCube verification session state
/// This helps prevent session timeouts during ComplyCube verification
class ComplyCubeSessionTracker extends GetxService {
  static ComplyCubeSessionTracker get instance =>
      Get.find<ComplyCubeSessionTracker>();

  // Static flag for immediate access without dependency injection
  static bool _globalComplyCubeActive = false;

  final RxBool _isComplyCubeActive = false.obs;
  final RxBool _isComplyCubeVerificationInProgress = false.obs;

  /// Whether ComplyCube verification is currently active
  bool get isComplyCubeActive => _isComplyCubeActive.value;

  /// Whether ComplyCube verification is in progress
  bool get isComplyCubeVerificationInProgress =>
      _isComplyCubeVerificationInProgress.value;

  /// Observable for ComplyCube active state
  RxBool get isComplyCubeActiveObs => _isComplyCubeActive;

  /// Static method to check if ComplyCube is active (immediate access)
  static bool get isGlobalComplyCubeActive => _globalComplyCubeActive;

  /// Start ComplyCube session tracking
  void startComplyCubeSession() {
    debugPrint('🔐 ComplyCubeSessionTracker: Starting ComplyCube session');
    debugPrint(
        '🔐 ComplyCubeSessionTracker: Before - Active: ${_isComplyCubeActive.value}, InProgress: ${_isComplyCubeVerificationInProgress.value}');

    // Set both instance and static flags
    _isComplyCubeActive.value = true;
    _isComplyCubeVerificationInProgress.value = true;
    _globalComplyCubeActive = true;

    debugPrint(
        '🔐 ComplyCubeSessionTracker: After - Active: ${_isComplyCubeActive.value}, InProgress: ${_isComplyCubeVerificationInProgress.value}, Global: $_globalComplyCubeActive');

    // Force a small delay to ensure state is set before any session timeout checks
    Future.delayed(const Duration(milliseconds: 100), () {
      debugPrint(
          '🔐 ComplyCubeSessionTracker: Delayed check - Active: ${_isComplyCubeActive.value}, Global: $_globalComplyCubeActive');
    });
  }

  /// End ComplyCube session tracking
  void endComplyCubeSession() {
    debugPrint('🔐 ComplyCubeSessionTracker: Ending ComplyCube session');
    _isComplyCubeActive.value = false;
    _isComplyCubeVerificationInProgress.value = false;
    _globalComplyCubeActive = false;
    debugPrint(
        '🔐 ComplyCubeSessionTracker: Session ended - Global: $_globalComplyCubeActive');
  }

  /// Mark ComplyCube verification as completed (but may still be on ComplyCube screens)
  void markComplyCubeVerificationCompleted() {
    debugPrint(
        '🔐 ComplyCubeSessionTracker: ComplyCube verification completed');
    _isComplyCubeVerificationInProgress.value = false;
    // Keep _isComplyCubeActive true until user navigates away
  }

  /// Reset all ComplyCube tracking state
  void reset() {
    debugPrint('🔐 ComplyCubeSessionTracker: Resetting all state');
    _isComplyCubeActive.value = false;
    _isComplyCubeVerificationInProgress.value = false;
  }
}
