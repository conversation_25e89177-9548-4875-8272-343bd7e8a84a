import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Service to track ComplyCube verification session state
/// This helps prevent session timeouts during ComplyCube verification
class ComplyCubeSessionTracker extends GetxService {
  static ComplyCubeSessionTracker get instance => Get.find<ComplyCubeSessionTracker>();
  
  final RxBool _isComplyCubeActive = false.obs;
  final RxBool _isComplyCubeVerificationInProgress = false.obs;
  
  /// Whether ComplyCube verification is currently active
  bool get isComplyCubeActive => _isComplyCubeActive.value;
  
  /// Whether ComplyCube verification is in progress
  bool get isComplyCubeVerificationInProgress => _isComplyCubeVerificationInProgress.value;
  
  /// Observable for ComplyCube active state
  RxBool get isComplyCubeActiveObs => _isComplyCubeActive;
  
  /// Start ComplyCube session tracking
  void startComplyCubeSession() {
    debugPrint('🔐 ComplyCubeSessionTracker: Starting ComplyCube session');
    _isComplyCubeActive.value = true;
    _isComplyCubeVerificationInProgress.value = true;
  }
  
  /// End ComplyCube session tracking
  void endComplyCubeSession() {
    debugPrint('🔐 ComplyCubeSessionTracker: Ending ComplyCube session');
    _isComplyCubeActive.value = false;
    _isComplyCubeVerificationInProgress.value = false;
  }
  
  /// Mark ComplyCube verification as completed (but may still be on ComplyCube screens)
  void markComplyCubeVerificationCompleted() {
    debugPrint('🔐 ComplyCubeSessionTracker: ComplyCube verification completed');
    _isComplyCubeVerificationInProgress.value = false;
    // Keep _isComplyCubeActive true until user navigates away
  }
  
  /// Reset all ComplyCube tracking state
  void reset() {
    debugPrint('🔐 ComplyCubeSessionTracker: Resetting all state');
    _isComplyCubeActive.value = false;
    _isComplyCubeVerificationInProgress.value = false;
  }
}
