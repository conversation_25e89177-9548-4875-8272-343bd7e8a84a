import 'package:get/get.dart';

class ComplyCubeSessionTracker extends GetxService {
  static ComplyCubeSessionTracker get instance =>
      Get.find<ComplyCubeSessionTracker>();

  static bool _globalComplyCubeActive = false;

  final RxBool _isComplyCubeActive = false.obs;
  final RxBool _isComplyCubeVerificationInProgress = false.obs;

  bool get isComplyCubeActive => _isComplyCubeActive.value;

  bool get isComplyCubeVerificationInProgress =>
      _isComplyCubeVerificationInProgress.value;

  RxBool get isComplyCubeActiveObs => _isComplyCubeActive;

  static bool get isGlobalComplyCubeActive => _globalComplyCubeActive;

  void startComplyCubeSession() {
    _isComplyCubeActive.value = true;
    _isComplyCubeVerificationInProgress.value = true;
    _globalComplyCubeActive = true;
  }

  void endComplyCubeSession() {
    _isComplyCubeActive.value = false;
    _isComplyCubeVerificationInProgress.value = false;
    _globalComplyCubeActive = false;
  }

  void markComplyCubeVerificationCompleted() {
    _isComplyCubeVerificationInProgress.value = false;
  }

  void reset() {
    _isComplyCubeActive.value = false;
    _isComplyCubeVerificationInProgress.value = false;
  }
}
