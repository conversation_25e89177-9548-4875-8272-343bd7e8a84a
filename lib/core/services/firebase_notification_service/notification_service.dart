import 'dart:convert';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/services/device/session_timeout_service.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/notifications/presentation/widgets/notification_group.dart';
import 'package:cbrs/main.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class NotificationService {
  final FirebaseMessaging messaging = FirebaseMessaging.instance;

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initLocalNotifications() async {
    const androidInitSetting =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosInitSetting = DarwinInitializationSettings();
    const initializationSetting = InitializationSettings(
      android: androidInitSetting,
      iOS: iosInitSetting,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSetting,
      onDidReceiveNotificationResponse: (NotificationResponse response) async {
        if (response.payload != null) {
          GlobalVariable.notificationData =
              jsonDecode(response.payload!) as Map<String, dynamic>;
          if (mainNavigatorKey.currentContext != null) {
            handleNotificationRedirect(
              mainNavigatorKey.currentContext!,
            );
          }
        }
      },
    );
  }

  Future<void> setUpFirebaseMessaging(BuildContext context) async {
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      await handleMessage(message);

      if (!Get.find<SessionTimeoutService>().isSessionTimeout.value) {
        handleNotificationRedirect(context);
      }
    });

    final initialMessage = await messaging.getInitialMessage();
    if (initialMessage != null) {
      await handleMessage(initialMessage);

      if (!Get.find<SessionTimeoutService>().isSessionTimeout.value) {
        handleNotificationRedirect(context);
      }
    }

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (message.data['notificationType'] == 'VIDEO_CALL') {
        return;
      }

      if (message.data['notificationType'] == 'VIDEO_CALL_REJECTED') {
        if (context.mounted) context.pop();
        return;
      }

      if (Platform.isIOS) {
        iosForegroundMessage(message);
      }
      if (Platform.isAndroid) {
        showNotification(message);
      }
    });
  }

  Future<void> handleMessage(RemoteMessage message) async {
    GlobalVariable.isAppOpenFromNotification = true;
    GlobalVariable.notificationData = message.data;
  }

  Future<void> showNotification(RemoteMessage message) async {
    final androidChannel = AndroidNotificationChannel(
      message.notification?.android?.channelId ?? 'default_channel',
      message.notification?.android?.channelId ?? 'Default Channel',
      importance: Importance.high,
    );

    final androidNotificationDetails = AndroidNotificationDetails(
      androidChannel.id,
      androidChannel.name,
      channelDescription: 'Channel Description',
      importance: Importance.high,
      priority: Priority.high,
      sound: androidChannel.sound,
    );

    const iosNotificationDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iosNotificationDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      0,
      message.notification?.title,
      message.notification?.body,
      notificationDetails,
      payload: jsonEncode(message.data),
    );
  }

  Future<void> iosForegroundMessage(RemoteMessage message) async {
    await messaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    debugPrint('iOS foreground message: $message');
  }

  Future<void> requestNotificationPermission() async {
    final settings = await messaging.requestPermission(
      provisional: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted notification permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      debugPrint('User granted provisional notification permission');
    } else {
      _showPermissionDialog();
    }
  }

  void _showPermissionDialog() {
    showDialog<void>(
      context: mainNavigatorKey.currentContext!,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Permission Required'),
          content: const Text(
              'This app needs notification permissions to function properly.'
              ' Please enable it in the app settings.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('Open Settings'),
              onPressed: () {
                Navigator.of(context).pop();
                AppSettings.openAppSettings(type: AppSettingsType.notification);
              },
            ),
          ],
        );
      },
    );
  }
}

void handleNotificationRedirect(BuildContext context) {
  GlobalVariable.isAppOpenFromNotification = false;
  final notificationData = GlobalVariable.notificationData;

  if (notificationData.containsKey('notificationType') &&
      notificationData['notificationType'] == 'TRANSACTION') {
    context.pushNamed(
      AppRouteName.notificationDetails,
      extra: NotificationItem(
        title: notificationData['title'] as String? ?? '',
        description: notificationData['description'] as String? ?? '',
        isRead: false,
        id: notificationData['id'] as String? ?? '',
        date: DateTime.now().toString(),
        transactionType: notificationData['transactionType'] as String? ?? '',
      ),
    );
  }

  if (notificationData.containsKey('notificationType') &&
      notificationData['notificationType'] == 'CHAT') {
    context.pushNamed(AppRouteName.chat);
  }

  GlobalVariable.notificationData = {};
}
