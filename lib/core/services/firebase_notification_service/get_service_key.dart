import 'package:cbrs/core/common/service_account_json.dart';
import 'package:googleapis_auth/auth_io.dart' as auth;
import 'package:http/http.dart' as http;

class GetServerKey {
  static Future<String> getServerKey() async {
    final scopes = <String>[
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/firebase.database',
      'https://www.googleapis.com/auth/firebase.messaging',
    ];

    final client = http.Client();

    try {
      final credentials = await auth.obtainAccessCredentialsViaServiceAccount(
        auth.ServiceAccountCredentials.fromJson(serviceAccountJson),
        scopes,
        client,
      );

      final accessServerKey = credentials.accessToken.data;

      return accessServerKey;
    } finally {
      client.close();
    }
  }
}
