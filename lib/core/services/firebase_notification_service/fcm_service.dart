import 'dart:io';

import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

class FcmService {
  FcmService({required this.authLocalDataSource});

  final FirebaseMessaging messaging = FirebaseMessaging.instance;

  final AuthLocalDataSource authLocalDataSource;

  /// Store FCM token in Firestore
  Future<void> storeFcmToken() async {
    final settings = await messaging.requestPermission();
    debugPrint('lets stroe fcm token');
    // Only proceed if permission is granted
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      try {
        final token = await messaging.getToken();
        GlobalVariable.fcmToken = token ?? '';
        debugPrint('FCM Token: $token');

        final userId = await authLocalDataSource.getUserId();
        if (Platform.isIOS) {
          var apnsToken = await messaging.getAPNSToken();
          if (apnsToken != null) {
            await messaging.subscribeToTopic(userId ?? '');
          } else {
            await Future<void>.delayed(
              const Duration(
                seconds: 3,
              ),
            );
            apnsToken = await messaging.getAPNSToken();
            await messaging.subscribeToTopic(userId ?? '');
          }
        } else {
          await messaging.subscribeToTopic(userId ?? '');
        }
        if (userId == null) {
          debugPrint('User ID is null. Cannot store FCM token.');
          return;
        }

        await _updateOrCreateFirestoreDoc(
          collection: 'users',
          docId: userId,
          data: {'fcmToken': token},
        );

        GlobalVariable.fcmToken = token ?? '';
        debugPrint('Fcom token $token');

        debugPrint('FCM token stored successfully for user: $userId');
      } catch (e) {
        debugPrint('Error storing FCM token: $e');
      }
    } else {
      debugPrint('Notification permission not granted.');
    }
  }

  /// Get FCM token for a user from Firestore
  Future<String?> getFcmToken(String userId) async {
    try {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists) {
        final data = userDoc.data();
        return data?['fcmToken'] as String?;
      } else {
        debugPrint('User document does not exist for userId: $userId');
        return null;
      }
    } catch (e) {
      debugPrint('Error retrieving FCM token: $e');
      return null;
    }
  }

  /// Helper method to update or create a Firestore document
  Future<void> _updateOrCreateFirestoreDoc({
    required String collection,
    required String docId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final docRef =
          FirebaseFirestore.instance.collection(collection).doc(docId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        await docRef.update(data);
        debugPrint('Updated Firestore document: $collection/$docId');
      } else {
        await docRef.set(data, SetOptions(merge: true));
        debugPrint('Created Firestore document: $collection/$docId');
      }
    } catch (e) {
      debugPrint('Error updating/creating Firestore document: $e');
      rethrow;
    }
  }
}

/// Update user avatar in Firestore
Future<void> updateAvatar(String avatar) async {
  try {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();
    final userId = user?.id;

    if (userId == null) {
      debugPrint('User ID is null. Cannot update avatar.');
      return;
    }

    await _updateOrCreateFirestoreDoc(
      collection: 'users',
      docId: userId,
      data: {'avatar': avatar},
    );

    debugPrint('Avatar updated successfully for user: $userId');
  } catch (e) {
    debugPrint('Error updating avatar: $e');
  }
}

/// Helper method to update or create a Firestore document
Future<void> _updateOrCreateFirestoreDoc({
  required String collection,
  required String docId,
  required Map<String, dynamic> data,
}) async {
  try {
    final docRef = FirebaseFirestore.instance.collection(collection).doc(docId);
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      await docRef.update(data);
      debugPrint('Updated Firestore document: $collection/$docId');
    } else {
      await docRef.set(data, SetOptions(merge: true));
      debugPrint('Created Firestore document: $collection/$docId');
    }
  } catch (e) {
    debugPrint('Error updating/creating Firestore document: $e');
    rethrow;
  }
}

Future<void> updateUserStatus({required bool isOnline}) async {
  try {
    final user = await sl<AuthLocalDataSource>().getCachedUserData();
    final userId = user?.id;

    if (userId == null) {
      debugPrint('User ID is null. Cannot update user status.');
      return;
    }

    await FirebaseFirestore.instance.collection('users').doc(userId).set(
      {
        'isOnline': isOnline,
        'lastSeen': isOnline ? null : FieldValue.serverTimestamp(),
      },
      SetOptions(merge: true),
    );

    debugPrint("User status updated: ${isOnline ? 'Online' : 'Offline'}");
  } catch (e) {
    debugPrint('Error updating user status: $e');
  }
}
