import 'dart:async';

import 'package:cbrs/core/services/agora_chat_service.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/chat/domain/entities/chat_contact.dart';
import 'package:flutter/widgets.dart';
import 'package:rxdart/rxdart.dart';

/// Service to manage user presence (online/offline status)
/// Defaults to offline and manages status based on app lifecycle and
/// authentication
class PresenceService {
  factory PresenceService() => _instance;
  PresenceService._internal();
  static final PresenceService _instance = PresenceService._internal();

  // Stream controllers for presence state
  final _currentStatusController =
      BehaviorSubject<ContactStatus>.seeded(ContactStatus.offline);
  final _presenceUpdatesController =
      StreamController<Map<String, ContactStatus>>.broadcast();

  // Current user status - defaults to offline
  ContactStatus _currentStatus = ContactStatus.offline;
  String? _currentUserId;
  bool _isInitialized = false;
  Timer? _presenceHeartbeatTimer;

  // Getters
  ContactStatus get currentStatus => _currentStatus;
  String? get currentUserId => _currentUserId;
  bool get isOnline => _currentStatus == ContactStatus.online;
  bool get isInitialized => _isInitialized;

  // Streams
  Stream<ContactStatus> get statusStream => _currentStatusController.stream;
  Stream<Map<String, ContactStatus>> get presenceUpdatesStream =>
      _presenceUpdatesController.stream;

  /// Initialize the presence service
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🟢 PresenceService: Initializing...');

    try {
      // Check if AuthLocalDataSource is available
      if (!sl.isRegistered<AuthLocalDataSource>()) {
        debugPrint(
          '🟡 PresenceService: AuthLocalDataSource not yet registered, deferring initialization',
        );
        return;
      }

      // Get current user
      final authLocalDataSource = sl<AuthLocalDataSource>();
      final userData = await authLocalDataSource.getCachedUserData();
      _currentUserId = userData?.id;

      if (_currentUserId != null) {
        debugPrint('🟢 PresenceService: Initialized for user: $_currentUserId');

        // Set initial status to offline (default)
        await _updatePresenceStatus(ContactStatus.offline, notifyRemote: false);

        // Start presence heartbeat for online status maintenance
        _startPresenceHeartbeat();
      }

      _isInitialized = true;
      debugPrint('🟢 PresenceService: Initialization complete');
    } catch (e) {
      debugPrint('🔴 PresenceService: Initialization failed: $e');
    }
  }

  /// Set user status to online
  Future<void> setOnline() async {
    if (!_isInitialized) {
      debugPrint('🟡 PresenceService: Not initialized, initializing now...');
      await initialize();
      if (!_isInitialized) {
        debugPrint(
          '🔴 PresenceService: Failed to initialize, cannot set online',
        );
        return;
      }
    }

    if (_currentStatus != ContactStatus.online) {
      debugPrint('🟢 PresenceService: Setting status to ONLINE');
      await _updatePresenceStatus(ContactStatus.online);
      _startPresenceHeartbeat();
    }
  }

  /// Set user status to offline
  Future<void> setOffline() async {
    if (!_isInitialized) return;

    if (_currentStatus != ContactStatus.offline) {
      debugPrint('🔴 PresenceService: Setting status to OFFLINE');
      await _updatePresenceStatus(ContactStatus.offline);
      _stopPresenceHeartbeat();
    }
  }

  /// Set user status to away
  Future<void> setAway() async {
    if (!_isInitialized) return;

    if (_currentStatus != ContactStatus.away) {
      debugPrint('🟡 PresenceService: Setting status to AWAY');
      await _updatePresenceStatus(ContactStatus.away);
    }
  }

  /// Set user status to busy
  Future<void> setBusy() async {
    if (!_isInitialized) return;

    if (_currentStatus != ContactStatus.busy) {
      debugPrint('🔴 PresenceService: Setting status to BUSY');
      await _updatePresenceStatus(ContactStatus.busy);
    }
  }

  /// Update presence status internally and notify Agora Chat
  Future<void> _updatePresenceStatus(
    ContactStatus status, {
    bool notifyRemote = true,
  }) async {
    final previousStatus = _currentStatus;
    _currentStatus = status;

    // Notify local listeners
    _currentStatusController.add(status);

    debugPrint(
      '🔄 PresenceService: Status changed from ${previousStatus.name} to ${status.name}',
    );

    // Update remote presence if needed
    if (notifyRemote && _currentUserId != null) {
      await _updateRemotePresence(status);
    }
  }

  /// Update presence on Agora Chat
  Future<void> _updateRemotePresence(ContactStatus status) async {
    try {
      final agoraChatService = sl<AgoraChatService>();

      if (agoraChatService.isLoggedIn) {
        final presenceDescription = _mapStatusToPresenceDescription(status);
        await agoraChatService.chatClient?.presenceManager
            .publishPresence(presenceDescription);
        debugPrint(
          '🌐 PresenceService: Remote presence updated to: $presenceDescription',
        );
      } else {
        debugPrint(
          '🟡 PresenceService: Agora Chat not logged in, will wait and retry',
        );
        // Wait for Agora Chat to be ready and then update presence
        _waitForAgoraChatAndUpdatePresence(status);
      }
    } catch (e) {
      debugPrint('🔴 PresenceService: Failed to update remote presence: $e');
      // Don't throw - presence updates should be non-blocking
    }
  }

  /// Wait for Agora Chat to be ready and then update presence
  Future<void> _waitForAgoraChatAndUpdatePresence(ContactStatus status) async {
    final agoraChatService = sl<AgoraChatService>();

    // Wait up to 10 seconds for Agora Chat to be ready
    for (var i = 0; i < 10; i++) {
      await Future<void>.delayed(const Duration(seconds: 1));

      if (agoraChatService.isLoggedIn) {
        debugPrint(
          '🟢 PresenceService: Agora Chat is now ready, updating presence',
        );
        try {
          final presenceDescription = _mapStatusToPresenceDescription(status);
          await agoraChatService.chatClient?.presenceManager
              .publishPresence(presenceDescription);
          debugPrint(
            '🌐 PresenceService: Remote presence updated to: $presenceDescription',
          );
          return; // Success, exit the retry loop
        } catch (e) {
          debugPrint(
            '🔴 PresenceService: Failed to update presence after wait: $e',
          );
          return;
        }
      }
    }

    debugPrint(
      '🔴 PresenceService: Timeout waiting for Agora Chat to be ready',
    );
  }

  /// Map ContactStatus to Agora presence description
  String _mapStatusToPresenceDescription(ContactStatus status) {
    switch (status) {
      case ContactStatus.online:
        return 'online';
      case ContactStatus.away:
        return 'away';
      case ContactStatus.busy:
        return 'busy';
      case ContactStatus.offline:
        return 'offline';
    }
  }

  /// Start heartbeat timer to maintain online presence
  void _startPresenceHeartbeat() {
    _stopPresenceHeartbeat(); // Stop any existing timer

    if (_currentStatus == ContactStatus.online) {
      _presenceHeartbeatTimer =
          Timer.periodic(const Duration(minutes: 2), (timer) {
        if (_currentStatus == ContactStatus.online) {
          _updateRemotePresence(ContactStatus.online);
        } else {
          timer.cancel();
        }
      });
      debugPrint('🔄 PresenceService: Started presence heartbeat');
    }
  }

  /// Stop presence heartbeat timer
  void _stopPresenceHeartbeat() {
    _presenceHeartbeatTimer?.cancel();
    _presenceHeartbeatTimer = null;
    debugPrint('⏹️ PresenceService: Stopped presence heartbeat');
  }

  Future<void> handleAppLifecycleChange(AppLifecycleState state) async {
    if (!_isInitialized) {
      // Try to initialize if not already done
      await initialize();
      if (!_isInitialized) return;
    }

    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('📱 PresenceService: App resumed - setting online');
        await setOnline();
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        debugPrint('📱 PresenceService: App paused/inactive - setting offline');
        await setOffline();
      case AppLifecycleState.detached:
        debugPrint('📱 PresenceService: App detached - setting offline');
        await setOffline();
      case AppLifecycleState.hidden:
        debugPrint('📱 PresenceService: App hidden - setting away');
        await setAway();
    }
  }

  /// Handle user authentication changes
  Future<void> handleAuthenticationChange({
    required bool isAuthenticated,
    String? userId,
  }) async {
    if (isAuthenticated && userId != null) {
      _currentUserId = userId;
      if (!_isInitialized) {
        await initialize();
      }
      // Set user online immediately after authentication
      debugPrint(
          '🔐 PresenceService: User authenticated: $userId - setting online');
      await setOnline();
    } else {
      debugPrint('🔐 PresenceService: User logged out - setting offline');
      await setOffline();
      _currentUserId = null;
    }
  }

  /// Get presence status for a specific contact
  Future<ContactStatus> getContactPresence(String contactId) async {
    try {
      final agoraChatService = sl<AgoraChatService>();

      if (agoraChatService.isLoggedIn) {
        // Fetch presence from Agora Chat
        final presenceList = await agoraChatService.chatClient?.presenceManager
            .fetchPresenceStatus(members: [contactId]);

        if (presenceList?.isNotEmpty == true) {
          final presence = presenceList!.first;
          return _mapPresenceDescriptionToStatus(
            presence.statusDescription,
          );
        }
      }

      // Default to offline if unable to fetch
      return ContactStatus.offline;
    } catch (e) {
      debugPrint(
        '🔴 PresenceService: Failed to get contact presence for $contactId: $e',
      );
      return ContactStatus.offline;
    }
  }

  /// Map Agora presence description to ContactStatus
  ContactStatus _mapPresenceDescriptionToStatus(String description) {
    switch (description.toLowerCase()) {
      case 'online':
        return ContactStatus.online;
      case 'away':
        return ContactStatus.away;
      case 'busy':
        return ContactStatus.busy;
      case 'offline':
      default:
        return ContactStatus.offline;
    }
  }

  /// Subscribe to presence updates for contacts
  Future<void> subscribeToContactsPresence(List<String> contactIds) async {
    if (contactIds.isEmpty) return;

    try {
      final agoraChatService = sl<AgoraChatService>();

      if (agoraChatService.isLoggedIn) {
        await agoraChatService.chatClient?.presenceManager.subscribe(
          members: contactIds,
          expiry: 86400, // 24 hours
        );
        debugPrint(
          '🔔 PresenceService: Subscribed to presence for ${contactIds.length} contacts',
        );
      }
    } catch (e) {
      debugPrint(
        '🔴 PresenceService: Failed to subscribe to contacts presence: $e',
      );
    }
  }

  /// Unsubscribe from presence updates for contacts
  Future<void> unsubscribeFromContactsPresence(List<String> contactIds) async {
    if (contactIds.isEmpty) return;

    try {
      final agoraChatService = sl<AgoraChatService>();

      if (agoraChatService.isLoggedIn) {
        await agoraChatService.chatClient?.presenceManager.unsubscribe(
          members: contactIds,
        );
        debugPrint(
          '🔕 PresenceService: Unsubscribed from presence for ${contactIds.length} contacts',
        );
      }
    } catch (e) {
      debugPrint(
        '🔴 PresenceService: Failed to unsubscribe from contacts presence: $e',
      );
    }
  }

  /// Handle presence status changes from Agora Chat
  void handlePresenceStatusChanged(List<dynamic> presenceList) {
    final presenceUpdates = <String, ContactStatus>{};

    for (final presence in presenceList) {
      if (presence.publisher != null && presence.statusDescription != null) {
        final userId = presence.publisher as String;
        final status = _mapPresenceDescriptionToStatus(
          presence.statusDescription as String,
        );
        presenceUpdates[userId] = status;
      }
    }

    if (presenceUpdates.isNotEmpty) {
      _presenceUpdatesController.add(presenceUpdates);
      debugPrint(
        '👥 PresenceService: Received presence updates for ${presenceUpdates.length} users',
      );
    }
  }

  /// Force refresh presence status
  Future<void> refreshPresence() async {
    if (!_isInitialized || _currentUserId == null) return;

    debugPrint('🔄 PresenceService: Refreshing presence status');
    await _updateRemotePresence(_currentStatus);
  }

  /// Get formatted status text for UI display
  String getStatusText(ContactStatus status) {
    switch (status) {
      case ContactStatus.online:
        return 'Online';
      case ContactStatus.away:
        return 'Away';
      case ContactStatus.busy:
        return 'Busy';
      case ContactStatus.offline:
        return 'Offline';
    }
  }

  /// Get status color for UI display
  String getStatusColor(ContactStatus status) {
    switch (status) {
      case ContactStatus.online:
        return '#4CAF50'; // Green
      case ContactStatus.away:
        return '#FF9800'; // Orange
      case ContactStatus.busy:
        return '#F44336'; // Red
      case ContactStatus.offline:
        return '#9E9E9E'; // Grey
    }
  }

  /// Dispose of the service
  void dispose() {
    debugPrint('🗑️ PresenceService: Disposing...');
    _stopPresenceHeartbeat();
    _currentStatusController.close();
    _presenceUpdatesController.close();
    _isInitialized = false;
  }
}
