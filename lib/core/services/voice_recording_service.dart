import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';

/// Service for handling voice recording functionality
class VoiceRecordingService {
  factory VoiceRecordingService() => _instance;
  VoiceRecordingService._internal();
  static final VoiceRecordingService _instance =
      VoiceRecordingService._internal();

  AudioRecorder? _recorder;
  bool _isRecording = false;
  String? _currentRecordingPath;
  Timer? _recordingTimer;
  int _recordingDuration = 0;

  // Stream controllers for real-time updates
  final _recordingStateController = StreamController<bool>.broadcast();
  final _recordingDurationController = StreamController<int>.broadcast();

  // Getters
  bool get isRecording => _isRecording;
  String? get currentRecordingPath => _currentRecordingPath;
  int get recordingDuration => _recordingDuration;

  // Streams
  Stream<bool> get recordingStateStream => _recordingStateController.stream;
  Stream<int> get recordingDurationStream =>
      _recordingDurationController.stream;

  /// Ensure recorder is initialized
  void _ensureRecorderInitialized() {
    if (_recorder == null) {
      _recorder = AudioRecorder();
      debugPrint('AudioRecorder initialized');
    }
  }

  /// Start recording voice message
  Future<bool> startRecording() async {
    try {
      // Check if already recording
      if (_isRecording) {
        debugPrint('Already recording');
        return false;
      }

      // Ensure recorder is initialized
      _ensureRecorderInitialized();

      // Check and request microphone permission
      debugPrint('Checking microphone permission...');
      final permission = await Permission.microphone.status;
      debugPrint('Current permission status: $permission');

      if (!permission.isGranted) {
        if (permission.isPermanentlyDenied) {
          debugPrint('Microphone permission permanently denied');
          return false;
        }

        debugPrint('Requesting microphone permission...');
        final requestResult = await Permission.microphone.request();
        debugPrint('Permission request result: $requestResult');

        if (!requestResult.isGranted) {
          debugPrint('Microphone permission denied after request');
          return false;
        }
      }

      debugPrint('Microphone permission granted, proceeding with recording');

      // Generate unique file path
      final directory = await getTemporaryDirectory();
      final fileName = 'voice_${DateTime.now().millisecondsSinceEpoch}.m4a';
      _currentRecordingPath = '${directory.path}/$fileName';
      debugPrint('Recording path: $_currentRecordingPath');

      // Start recording
      debugPrint('Starting recorder...');
      await _recorder!.start(
        const RecordConfig(),
        path: _currentRecordingPath!,
      );

      _isRecording = true;
      _recordingDuration = 0;
      _recordingStateController.add(true);

      // Start duration timer
      _startDurationTimer();

      debugPrint(
        'Voice recording started successfully: $_currentRecordingPath',
      );
      return true;
    } catch (e) {
      debugPrint('Failed to start recording: $e');
      return false;
    }
  }

  /// Stop recording and return the file path and duration
  Future<VoiceRecordingResult?> stopRecording() async {
    try {
      if (!_isRecording) {
        debugPrint('Not currently recording');
        return null;
      }

      // Stop recording
      final path = await _recorder?.stop();
      _isRecording = false;
      _recordingStateController.add(false);

      // Stop duration timer
      _stopDurationTimer();

      if (path != null && await File(path).exists()) {
        final result = VoiceRecordingResult(
          filePath: path,
          duration: _recordingDuration,
        );

        // Reset duration after successful recording
        _recordingDuration = 0;
        _recordingDurationController.add(0);
        _currentRecordingPath = null;

        debugPrint(
          'Voice recording stopped: $path, duration: ${result.duration}s',
        );
        return result;
      } else {
        debugPrint('Recording file not found');
        // Reset state even if file not found
        _recordingDuration = 0;
        _recordingDurationController.add(0);
        _currentRecordingPath = null;
        return null;
      }
    } catch (e) {
      debugPrint('Failed to stop recording: $e');
      _isRecording = false;
      _recordingStateController.add(false);
      _stopDurationTimer();
      // Reset state on error
      _recordingDuration = 0;
      _recordingDurationController.add(0);
      _currentRecordingPath = null;

      await _recorder?.dispose();
      _recorder = null;

      return null;
    }
  }

  /// Cancel current recording
  Future<void> cancelRecording() async {
    try {
      if (_isRecording && _recorder != null) {
        await _recorder!.stop();
        _isRecording = false;
        _recordingStateController.add(false);
        _stopDurationTimer();

        // Reset duration to 0
        _recordingDuration = 0;
        _recordingDurationController.add(0);

        // Delete the recording file
        if (_currentRecordingPath != null) {
          final file = File(_currentRecordingPath!);
          if (await file.exists()) {
            await file.delete();
          }
        }

        _currentRecordingPath = null;
        debugPrint('Voice recording cancelled');
      }
    } catch (e) {
      debugPrint('Failed to cancel recording: $e');
      // Reset state even if there's an error
      _isRecording = false;
      _recordingStateController.add(false);
      _stopDurationTimer();
      _recordingDuration = 0;
      _recordingDurationController.add(0);
      _currentRecordingPath = null;

      await _recorder?.dispose();
      _recorder = null;
    }
  }

  /// Start the duration timer
  void _startDurationTimer() {
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _recordingDuration++;
      _recordingDurationController.add(_recordingDuration);
    });
  }

  /// Stop the duration timer
  void _stopDurationTimer() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
  }

  /// Check if microphone permission is granted
  Future<bool> hasPermission() async {
    try {
      final status = await Permission.microphone.status;
      debugPrint('Permission status: $status');
      return status.isGranted;
    } catch (e) {
      debugPrint('Error checking permission: $e');
      return false;
    }
  }

  /// Request microphone permission
  Future<bool> requestPermission() async {
    try {
      final status = await Permission.microphone.request();
      debugPrint('Permission request result: $status');
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting permission: $e');
      return false;
    }
  }

  /// Reset the recording service to initial state
  void reset() {
    _stopDurationTimer();
    _isRecording = false;
    _recordingDuration = 0;
    _currentRecordingPath = null;
    _recordingStateController.add(false);
    _recordingDurationController.add(0);

    // Dispose and recreate recorder to ensure clean state
    _recorder?.dispose();
    _recorder = null;

    debugPrint('Voice recording service reset');
  }

  /// Dispose resources
  void dispose() {
    _recordingStateController.close();
    _recordingDurationController.close();
    _stopDurationTimer();
    _recorder?.dispose();
    _recorder = null;
  }
}

/// Result of voice recording
class VoiceRecordingResult {
  const VoiceRecordingResult({
    required this.filePath,
    required this.duration,
  });

  final String filePath;
  final int duration; // Duration in seconds

  @override
  String toString() =>
      'VoiceRecordingResult(filePath: $filePath, duration: ${duration}s)';
}
