import 'package:cbrs/core/models/agora_user.dart';
import 'package:cbrs/core/services/agora_chat_service.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:flutter/foundation.dart';

/// Service for fetching user attributes using Direct User Attributes Fetch
class UserAttributeService {
  factory UserAttributeService() => _instance;
  UserAttributeService._internal();
  static final UserAttributeService _instance =
      UserAttributeService._internal();

  late final AgoraChatService _agoraChatService;

  /// Initialize the service
  void initialize() {
    _agoraChatService = sl<AgoraChatService>();
  }

  /// Get user attributes by username - Direct fetch from Agora
  Future<Map<String, dynamic>?> getUserAttributes(String username) async {
    try {
      if (!_agoraChatService.isLoggedIn) {
        debugPrint('UserAttributeService: Not logged in to Agora Chat');
        return null;
      }

      debugPrint('UserAttributeService: Fetching attributes for $username');
      final attributes = await _agoraChatService.fetchUserAttributes(username);

      debugPrint(
          'UserAttributeService: Retrieved attributes for $username: $attributes');
      return attributes;
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error fetching attributes for $username: $e');
      return null;
    }
  }

  /// Get user display name by username
  Future<String> getDisplayName(String username) async {
    try {
      final displayName = await _agoraChatService.getUserDisplayName(username);
      debugPrint(
          'UserAttributeService: Display name for $username: $displayName');
      return displayName;
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error getting display name for $username: $e');
      return username;
    }
  }

  /// Get user avatar URL by username
  Future<String?> getAvatarUrl(String username) async {
    try {
      final avatarUrl = await _agoraChatService.getUserAvatarUrl(username);
      debugPrint('UserAttributeService: Avatar URL for $username: $avatarUrl');
      return avatarUrl;
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error getting avatar URL for $username: $e');
      return null;
    }
  }

  /// Get user contact info (email and phone) by username
  Future<Map<String, String?>> getContactInfo(String username) async {
    try {
      final contactInfo = await _agoraChatService.getUserContactInfo(username);
      debugPrint(
          'UserAttributeService: Contact info for $username: $contactInfo');
      return contactInfo;
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error getting contact info for $username: $e');
      return {'email': null, 'phone': null};
    }
  }

  /// Get complete user profile by username
  Future<AgoraUser> getUserProfile(String username) async {
    try {
      final profile = await _agoraChatService.getUserProfile(username);
      debugPrint('UserAttributeService: Profile for $username: $profile');
      return profile;
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error getting profile for $username: $e');
      return AgoraUser(
        username: username,
        displayName: username,
      );
    }
  }

  /// Get multiple user profiles at once
  Future<List<AgoraUser>> getMultipleUserProfiles(
      List<String> usernames) async {
    final profiles = <AgoraUser>[];

    for (final username in usernames) {
      try {
        final profile = await getUserProfile(username);
        profiles.add(profile);
      } catch (e) {
        debugPrint(
            'UserAttributeService: Error getting profile for $username: $e');
        // Add fallback profile
        profiles.add(AgoraUser(
          username: username,
          displayName: username,
        ));
      }
    }

    return profiles;
  }

  /// Check if user has complete profile information
  Future<bool> hasCompleteProfile(String username) async {
    try {
      final profile = await getUserProfile(username);
      return profile.hasEmail && profile.hasPhone && profile.hasAvatar;
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error checking profile completeness for $username: $e');
      return false;
    }
  }

  /// Get user's preferred contact method (email or phone)
  Future<String?> getPreferredContactMethod(String username) async {
    try {
      final contactInfo = await getContactInfo(username);

      // Prefer email over phone if both are available
      if (contactInfo['email']?.isNotEmpty == true) {
        return contactInfo['email'];
      } else if (contactInfo['phone']?.isNotEmpty == true) {
        return contactInfo['phone'];
      }

      return null;
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error getting preferred contact for $username: $e');
      return null;
    }
  }

  /// Format user display info for UI
  Future<String> getFormattedDisplayInfo(String username) async {
    try {
      final profile = await getUserProfile(username);

      if (profile.hasEmail) {
        return '${profile.displayName} (${profile.email})';
      } else if (profile.hasPhone) {
        return '${profile.displayName} (${profile.phone})';
      } else {
        return profile.displayName;
      }
    } catch (e) {
      debugPrint(
          'UserAttributeService: Error formatting display info for $username: $e');
      return username;
    }
  }
}
