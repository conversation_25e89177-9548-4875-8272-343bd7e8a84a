import 'dart:async';

import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class SessionTimeoutService extends GetxService {
  final NavigationService _navigationService;
  final AuthLocalDataSource _authLocalDataSource;
  final _sessionTimeout = const Duration(minutes: 6);
  DateTime? _backgroundStartTime;
  final _isInBackground = false.obs;
  RxBool isSessionTimeout = false.obs;
  bool _isChecking = false;
  Timer? _timeoutTimer;

  SessionTimeoutService({
    required NavigationService navigationService,
    required AuthLocalDataSource authLocalDataSource,
  })  : _navigationService = navigationService,
        _authLocalDataSource = authLocalDataSource;

  void startBackgroundTimer() {
    if (_isInBackground.value) return;

    _isInBackground.value = true;
    _backgroundStartTime = DateTime.now();

    // Cancel any existing timer
    _timeoutTimer?.cancel();

    // Create a one-shot timer for the session timeout
    _timeoutTimer = Timer(_sessionTimeout, () {
      checkAndHandleTimeout();
    });
  }

  void stopBackgroundTimer() {
    if (!_isInBackground.value) return;

    _timeoutTimer?.cancel();
    _timeoutTimer = null;
    _isInBackground.value = false;
    _backgroundStartTime = null;
    isSessionTimeout.value = false;
  }

  Future<bool> checkAndHandleTimeout() async {
    if (_backgroundStartTime == null || _isChecking || !_isInBackground.value) {
      return false;
    }

    _isChecking = true;

    try {
      final now = DateTime.now();
      final timeInBackground = now.difference(_backgroundStartTime!);

      if (timeInBackground >= _sessionTimeout) {
        // Clear auth token first
        await _authLocalDataSource.clearAuthToken();

        // Use Go Router for navigation through NavigationService
        isSessionTimeout.value = true;
        final context = Get.context;
        if (context != null && context.mounted) {
          await _navigationService
              .handleRedirect(AppRouteName.tokenDeviceLogin);
          GoRouter.of(context).go(AppRouteName.tokenDeviceLogin);
        }

        stopBackgroundTimer();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint("⏰ Error checking timeout: $e");
      return false;
    } finally {
      _isChecking = false;
    }
  }

  @override
  void onClose() {
    _timeoutTimer?.cancel();
    super.onClose();
  }
}
