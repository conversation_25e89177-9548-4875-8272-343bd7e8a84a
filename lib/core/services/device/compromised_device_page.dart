import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:cbrs/core/services/device/device_security_service.dart';
import 'package:google_fonts/google_fonts.dart';

class CompromisedDevicePage extends StatelessWidget {
  const CompromisedDevicePage({
    super.key,
    this.compromiseType = CompromiseType.unknown,
  });
  final CompromiseType compromiseType;

  String _getTitle() {
    switch (compromiseType) {
      case CompromiseType.emulator:
        return 'Emulator Detected';
      case CompromiseType.simulator:
        return 'Simulator Detected';
      case CompromiseType.rooted:
        return 'Rooted Device Detected';
      case CompromiseType.developerMode:
        return 'Developer Mode Enabled';
      case CompromiseType.unknown:
      default:
        return 'Security Warning';
    }
  }

  String _getMessage() {
    switch (compromiseType) {
      case CompromiseType.emulator:
        return 'This app cannot run on Android emulators for security reasons. Please use a physical Android device.';
      case CompromiseType.simulator:
        return 'This app cannot run on iOS simulators for security reasons. Please use a physical iOS device.';
      case CompromiseType.rooted:
        return 'This device appears to be rooted or jailbroken. For security reasons, access is restricted. Please use a non-rooted device.';
      case CompromiseType.developerMode:
        return 'Developer mode (USB debugging) is enabled on this device. For security reasons, please disable developer options in your device settings.';
      case CompromiseType.unknown:
      default:
        return 'This device appears to be compromised. For security reasons, access is restricted.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),

              // Developer Mode Icon

              Center(
                child: Image.asset(
                  MediaRes.compromizedDeviceIcon,
                  width: 120,
                  height: 120,
                ),
              ),

              const SizedBox(height: 16),

              // Title
              // Text(
              //   _getTitle(),
              //   style: const TextStyle(
              //     fontSize: 28,
              //     fontWeight: FontWeight.w700,
              //     color: Color(0xFF212121),
              //     letterSpacing: -0.5,
              //   ),
              //   textAlign: TextAlign.center,
              // ),

              // const SizedBox(height: 24),

              // // Description
              // Text(
              //   _getMessage(),
              //   style: const TextStyle(
              //     fontSize: 16,
              //     color: Color(0xFF757575),
              //     height: 1.6,
              //     letterSpacing: 0.1,
              //   ),
              //   textAlign: TextAlign.center,
              // ),
              Text(
                textAlign: TextAlign.center,
                _getTitle(),
                style: GoogleFonts.outfit(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              SizedBox(height: 8),
              Text(
                _getMessage(),
                textAlign: TextAlign.center,
                style: GoogleFonts.outfit(
                  fontSize: 18,
                  color: const Color(0xFF595959),
                ),
              ),

              const Spacer(flex: 3),
              Container(
                width: double.infinity,
                height: 56,
                margin: const EdgeInsets.only(bottom: 32),
                child: ElevatedButton(
                  onPressed: DeviceSecurityService.secureExit,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0D451B),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: const Text(
                    'Exit',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
