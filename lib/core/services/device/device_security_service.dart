import 'dart:io';
import 'package:cbrs/core/services/device/compromised_device_page.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/main.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum CompromiseType {
  none,
  emulator,
  rooted,
  developerMode,
  simulator,
  unknown,
}

class DeviceSecurityService {
  DeviceSecurityService({
    DeviceInfoPlugin? deviceInfo,
  }) : _deviceInfo = deviceInfo ?? DeviceInfoPlugin();
  final DeviceInfoPlugin _deviceInfo;

  Future<void> validateDevice(BuildContext context) async {
    try {
      if (kDebugMode) {
        debugPrint('Checking device security...');
      }

      final compromiseType = await getCompromiseType();
      if (compromiseType != CompromiseType.none) {
        debugPrint('Device compromised! Type: $compromiseType');
        await _showCompromisedDevicePage(compromiseType);
      }
    } catch (e) {
      debugPrint('Security check failed: $e');
      await _showCompromisedDevicePage(CompromiseType.unknown);
    }
  }

  Future<void> _forceCloseApp() async {
    try {
      if (Platform.isAndroid) {
        await SystemNavigator.pop();
        await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      }
      // Force kill for both platforms
      exit(0);
    } catch (e) {
      exit(0);
    }
  }

  Future<bool> isDeviceCompromised() async {
    final compromiseType = await getCompromiseType();
    return compromiseType != CompromiseType.none;
  }

  Future<CompromiseType> getCompromiseType() async {
    try {
      final isEmulator = await _isEmulator();
      if (isEmulator) {
        return Platform.isIOS
            ? CompromiseType.simulator
            : CompromiseType.emulator;
      }

      final isRooted = await _isRooted();
      if (isRooted) {
        return CompromiseType.rooted;
      }

      final isDevMode = await _isDeveloperModeEnabled();
      if (isDevMode) {
        return CompromiseType.developerMode;
      }

      return CompromiseType.none;
    } catch (e) {
      return CompromiseType.unknown;
    }
  }

  Future<bool> _isDeveloperModeEnabled() async {
    if (!Platform.isAndroid) return false;
    try {
      const platform = MethodChannel('cbrs/device_security');
      final isDevMode = await platform.invokeMethod<bool>('isDeveloperMode');
      if (kDebugMode) {
        debugPrint('Developer mode check result: $isDevMode');
      }
      return isDevMode ?? false;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Developer mode check failed: $e');
      }
      return false;
    }
  }

  Future<bool> _isRooted() async {
    if (Platform.isAndroid) {
      try {
        final androidInfo = await _deviceInfo.androidInfo;

        // Check for common root indicators
        return androidInfo.tags.contains('test-keys') == true ||
            await _checkRootFiles() ||
            await _checkRootPackages();
      } catch (e) {
        return true;
      }
    }

    if (Platform.isIOS) {
      try {
        final iosInfo = await _deviceInfo.iosInfo;
        return await _checkJailbreakFiles() || !iosInfo.isPhysicalDevice;
      } catch (e) {
        return true;
      }
    }

    return false;
  }

  Future<bool> _checkRootFiles() async {
    final rootFiles = [
      '/system/app/Superuser.apk',
      '/system/xbin/su',
      '/system/bin/su',
      '/sbin/su',
      '/system/su',
      '/system/bin/.ext/.su',
    ];

    for (final file in rootFiles) {
      if (await File(file).exists()) {
        return true;
      }
    }
    return false;
  }

  Future<bool> _checkJailbreakFiles() async {
    final jailbreakFiles = [
      '/Applications/Cydia.app',
      '/Library/MobileSubstrate/MobileSubstrate.dylib',
      '/bin/bash',
      '/usr/sbin/sshd',
      '/etc/apt',
    ];

    for (final file in jailbreakFiles) {
      if (await File(file).exists()) {
        return true;
      }
    }
    return false;
  }

  Future<bool> _checkRootPackages() async {
    try {
      final result = await Process.run('pm', ['list', 'packages']);
      final output = result.stdout.toString().toLowerCase();

      return output.contains('supersu') ||
          output.contains('magisk') ||
          output.contains('rootcloak');
    } catch (e) {
      return false;
    }
  }

  Future<bool> _isEmulator() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;

        return androidInfo.isPhysicalDevice == false ||
            androidInfo.brand.toLowerCase().contains('generic') ||
            androidInfo.manufacturer.toLowerCase().contains('genymotion') ||
            androidInfo.model.toLowerCase().contains('sdk') ||
            androidInfo.model.toLowerCase().contains('emulator') ||
            androidInfo.model.toLowerCase().contains('android_sdk') ||
            androidInfo.product.toLowerCase().contains('sdk') ||
            androidInfo.hardware.toLowerCase().contains('goldfish') ||
            androidInfo.hardware.toLowerCase().contains('ranchu');
      }

      if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return !iosInfo.isPhysicalDevice ||
            iosInfo.model.toLowerCase().contains('simulator');
      }

      return true;
    } catch (e) {
      return true;
    }
  }

  Future<void> _showCompromisedDeviceDialog() async {
    final context = mainNavigatorKey.currentContext;
    if (context == null) return;

    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Let navigation settle

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => WillPopScope(
        onWillPop: () async => false,
        child: AlertDialog(
          title: const Text('Security Warning'),
          content: const Text(
            'This device appears to be compromised (rooted or emulator). For security reasons, access is restricted.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                // Navigator.of(context).pop();
                // // Optionally navigate to login or exit manually
                // // exit(0);
              },
              child: const Text('OK'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showCompromisedDevicePage(CompromiseType compromiseType) async {
    final navigator = mainNavigatorKey.currentState;
    if (navigator == null) return;

    mainNavigatorKey.currentContext?.go('/compromised-device');
  }

  /// Securely exit the app when device is compromised
  static void secureExit() {
    if (kDebugMode) {
      debugPrint('Securely exiting app due to compromised device');
    }
    exit(0);
  }
}
