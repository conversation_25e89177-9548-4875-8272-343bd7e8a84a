import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class DeviceSecurityService {
  final DeviceInfoPlugin _deviceInfo;

  DeviceSecurityService({
    required BuildContext context,
    DeviceInfoPlugin? deviceInfo,
  }) : _deviceInfo = deviceInfo ?? DeviceInfoPlugin();

  Future<void> validateDevice() async {
    try {
      if (kDebugMode) {
        debugPrint('Checking device security...');
      }
      
      final isCompromised = await isDeviceCompromised();
      if (isCompromised) {
        debugPrint('Device compromised! Closing app...');
        await _forceCloseApp();
      }
    } catch (e) {
      debugPrint('Security check failed: $e');
      await _forceCloseApp();
    }
  }

  Future<void> _forceCloseApp() async {
    try {
      if (Platform.isAndroid) {
        await SystemNavigator.pop();
        await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      }
      // Force kill for both platforms
      exit(0);
    } catch (e) {
      exit(0);
    }
  }

  Future<bool> isDeviceCompromised() async {
    try {
      final isEmulator = await _isEmulator();
      final isRooted = await _isRooted();
      
      return isEmulator || isRooted;
    } catch (e) {
      return true;
    }
  }

  Future<bool> _isRooted() async {
    if (Platform.isAndroid) {
      try {
        final androidInfo = await _deviceInfo.androidInfo;
        
        // Check for common root indicators
        return androidInfo.tags?.contains('test-keys') == true ||
            await _checkRootFiles() ||
            await _checkRootPackages();
      } catch (e) {
        return true;
      }
    }
    
    if (Platform.isIOS) {
      try {
        final iosInfo = await _deviceInfo.iosInfo;
        return await _checkJailbreakFiles() || !iosInfo.isPhysicalDevice;
      } catch (e) {
        return true;
      }
    }
    
    return false;
  }

  Future<bool> _checkRootFiles() async {
    final rootFiles = [
      '/system/app/Superuser.apk',
      '/system/xbin/su',
      '/system/bin/su',
      '/sbin/su',
      '/system/su',
      '/system/bin/.ext/.su',
    ];

    for (final file in rootFiles) {
      if (await File(file).exists()) {
        return true;
      }
    }
    return false;
  }

  Future<bool> _checkJailbreakFiles() async {
    final jailbreakFiles = [
      '/Applications/Cydia.app',
      '/Library/MobileSubstrate/MobileSubstrate.dylib',
      '/bin/bash',
      '/usr/sbin/sshd',
      '/etc/apt',
    ];

    for (final file in jailbreakFiles) {
      if (await File(file).exists()) {
        return true;
      }
    }
    return false;
  }

  Future<bool> _checkRootPackages() async {
    try {
      final result = await Process.run('pm', ['list', 'packages']);
      final output = result.stdout.toString().toLowerCase();
      
      return output.contains('supersu') ||
          output.contains('magisk') ||
          output.contains('rootcloak');
    } catch (e) {
      return false;
    }
  }

  Future<bool> _isEmulator() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        
        return androidInfo.isPhysicalDevice == false ||
            androidInfo.brand.toLowerCase().contains('generic') ||
            androidInfo.manufacturer.toLowerCase().contains('genymotion') ||
            androidInfo.model.toLowerCase().contains('sdk') ||
            androidInfo.model.toLowerCase().contains('emulator') ||
            androidInfo.model.toLowerCase().contains('android_sdk') ||
            androidInfo.product.toLowerCase().contains('sdk') ||
            androidInfo.hardware.toLowerCase().contains('goldfish') ||
            androidInfo.hardware.toLowerCase().contains('ranchu');
      } 
      
      if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return !iosInfo.isPhysicalDevice ||
            iosInfo.model.toLowerCase().contains('simulator');
      }
      
      return true;
    } catch (e) {
      return true;
    }
  }
}
