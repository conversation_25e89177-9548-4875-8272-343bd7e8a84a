import 'dart:async';

import 'package:cbrs/core/services/connectivity/connectivity_service.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/routes/route.dart' show router;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart';

class ConnectivityController extends GetxController {
  final ConnectivityService _connectivityService = ConnectivityService();
  NavigationService? _navigationService;
  final _isConnected = true.obs;
  final _isCheckingConnection = false.obs;
  bool _isNavigating = false;
  Timer? _debounceTimer;
  Timer? _retryTimer;

  // Enhanced constants for more tolerant connection checks
  static const _connectionCheckDelay = Duration(seconds: 3);
  static const _connectionRetryDelay = Duration(seconds: 8);
  static const _requiredFailedChecks = 3; // Increased tolerance
  static const _maxRetryAttempts = 5; // More retry attempts
  static const _connectionTimeout = Duration(seconds: 5);

  int _consecutiveFailedChecks = 0;
  int _retryAttempts = 0;
  final _lastKnownConnection = true.obs;

  bool get isConnected => _isConnected.value;
  bool get isCheckingConnection => _isCheckingConnection.value;

  NavigationService get navigationService {
    _navigationService ??= Get.find<NavigationService>();
    return _navigationService!;
  }

  @override
  void onInit() {
    super.onInit();
    _initConnectivity();
    _setupConnectivityStream();
  }

  Future<void> _initConnectivity() async {
    try {
      _isCheckingConnection.value = true;
      final connectivityResult = await _connectivityService.checkConnectivity();
      await _verifyAndUpdateConnectionStatus(connectivityResult);
    } catch (e) {
      debugPrint('Init connectivity error: $e');
      if (_lastKnownConnection.value) {
        _scheduleRetryCheck();
      } else {
        _updateConnectionStatus(ConnectivityResult.none);
      }
    } finally {
      _isCheckingConnection.value = false;
    }
  }

  Future<ConnectivityResult> checkConnectivity() async {
  if (_debounceTimer?.isActive ?? false) {
    return _isConnected.value ? (ConnectivityResult.wifi) : ConnectivityResult.none;
  }

    _isCheckingConnection.value = true;
     Completer<ConnectivityResult> completer = Completer();
    _debounceTimer = Timer(_connectionCheckDelay, () async {
      try {
        final result = await _connectivityService.checkConnectivity();
        await _verifyAndUpdateConnectionStatus(result);
              completer.complete(result);

      } catch (e) {
        debugPrint('Connectivity check error: $e');
        // More tolerant error handling
        if (_lastKnownConnection.value &&
            _consecutiveFailedChecks < _requiredFailedChecks) {
          _scheduleRetryCheck();
        } else {
          _updateConnectionStatus(ConnectivityResult.none);
        }

          completer.complete(ConnectivityResult.none);
      } finally {
        _isCheckingConnection.value = false;
      }
    });
      return completer.future;
  }

Future<bool> checkConnectivityAndReturnStatus() async {
  final result = await checkConnectivity();
  return result != ConnectivityResult.none;
}

  Future<void> _verifyAndUpdateConnectionStatus(
    ConnectivityResult result,
  ) async {
    if (result != ConnectivityResult.none) {
      // Double-check internet connectivity before declaring no connection
      final hasRealConnection =
          await _connectivityService.hasInternetConnection();

      if (!hasRealConnection && _lastKnownConnection.value) {
        // If we had connection before, be more tolerant
        _consecutiveFailedChecks++;
        if (_consecutiveFailedChecks < _requiredFailedChecks) {
          _scheduleRetryCheck();
          return;
        }
      }

      result = hasRealConnection ? result : ConnectivityResult.none;
    }

    _updateConnectionStatus(result);
  }

  void _setupConnectivityStream() {
    _connectivityService.connectivityStream
        .debounce((_) => TimerStream(true, const Duration(seconds: 2)))
        .listen(
      (ConnectivityResult result) async {
        await _verifyAndUpdateConnectionStatus(result);
      },
      onError: (_) {
        if (_lastKnownConnection.value) {
          _scheduleRetryCheck();
        } else {
          _updateConnectionStatus(ConnectivityResult.none);
        }
      },
    );
  }

  void _updateConnectionStatus(ConnectivityResult connectivityResult) {
    final wasConnected = _isConnected.value;
    final isNowConnected = connectivityResult != ConnectivityResult.none;

    if (wasConnected == isNowConnected) {
      _consecutiveFailedChecks = 0;
      _retryAttempts = 0;
      return;
    }

    if (!isNowConnected && wasConnected) {
      _consecutiveFailedChecks++;
      if (_consecutiveFailedChecks < _requiredFailedChecks) {
        _scheduleRetryCheck();
        return;
      }
    } else {
      _consecutiveFailedChecks = 0;
      _retryAttempts = 0;
    }

    _lastKnownConnection.value = isNowConnected;
    _isConnected.value = isNowConnected;

    if (!_isNavigating) {
      _handleConnectivityChange(isNowConnected);
    }
  }

  void _scheduleRetryCheck() {
    _retryTimer?.cancel();
    if (_retryAttempts >= _maxRetryAttempts) {
      _handleConnectivityChange(false);
      return;
    }

    _retryAttempts++;
    _retryTimer = Timer(_connectionRetryDelay, () async {
      try {
        _isCheckingConnection.value = true;
        final result = await _connectivityService.checkConnectivity();
        _updateConnectionStatus(result);
      } finally {
        _isCheckingConnection.value = false;
      }
    });
  }

  void _handleConnectivityChange(bool isConnected) {
    _isNavigating = true;
    try {
      if (!isConnected) {
        _navigateToConnectionLost();
      } else {
        _handleConnectionRestored();
      }
    } finally {
      Future.delayed(const Duration(milliseconds: 500), () {
        _isNavigating = false;
      });
    }
  }

  void _navigateToConnectionLost() {
    final context = router.routerDelegate.navigatorKey.currentContext;
    if (context == null) return;

    final currentLocation =
        router.routerDelegate.currentConfiguration.uri.toString();
    if (currentLocation == '/connection-lost') return;

    router.push('/connection-lost').onError((error, stackTrace) {
      debugPrint('Navigation error: $error');
      return null;
    });
  }

  Future<void> _handleConnectionRestored() async {
    final currentLocation =
        router.routerDelegate.currentConfiguration.uri.toString();

    if (currentLocation == '/connection-lost') {
      await Future.delayed(_connectionCheckDelay);
      if (!_isConnected.value) return;

      try {
        _isCheckingConnection.value = true;
        final deviceController = Get.find<DeviceCheckController>();

        deviceController.resetInitialization();
        await deviceController.checkDeviceOnStartup();

        final redirectPath = await navigationService.handleRedirect('/');

        if (redirectPath == null && router.canPop()) {
          router.pop();
        } else if (redirectPath != null) {
          router.go(redirectPath);
        } else {
          router.go('/');
        }
      } catch (e) {
        debugPrint('Error during device check after connection restored: $e');
        if (e.toString().toLowerCase().contains('network') ||
            e.toString().toLowerCase().contains('connection')) {
          _updateConnectionStatus(ConnectivityResult.none);
        }
      } finally {
        _isCheckingConnection.value = false;
      }
    }
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    _retryTimer?.cancel();
    super.onClose();
  }

  Future<bool> hasRealConnectivity() async {
    try {
      _isCheckingConnection.value = true;
      final result = await _connectivityService.hasInternetConnection();
      return result;
    } catch (e) {
      return false;
    } finally {
      _isCheckingConnection.value = false;
    }
  }
}
