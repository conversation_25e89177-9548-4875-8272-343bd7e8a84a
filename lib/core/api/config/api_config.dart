// import 'package:approov_service_flutter_httpclient/approov_service_flutter_httpclient.dart';
import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/interceptors/auth_interceptor.dart';
import 'package:cbrs/core/api/interceptors/connectivity_interceptor.dart';
import 'package:cbrs/core/api/interceptors/error_interceptor.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';

class ApiConfig {
  ApiConfig({required AuthLocalDataSource localDataSource})
      : _localDataSource = localDataSource;
  static Dio? _dio;
  final AuthLocalDataSource _localDataSource;
  final Connectivity _connectivity = Connectivity();

  Dio get client {
    _dio ??= _createDio();
    return _dio!;
  }

  Dio _createDio() {
    final dio = Dio(
      BaseOptions(
        baseUrl: _getBaseUrl(),
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: defaultHeaders,
        validateStatus: (status) => status != null && status < 500,
      ),
    );

    final tempDio = Dio(dio.options);

    debugPrint('😳😳😳😳😳😳😳😳 fdio options ${tempDio.options.extra}');

    dio.interceptors.addAll([
      ConnectivityInterceptor(_connectivity),
      AuthInterceptor(_localDataSource, tempDio),
      ErrorInterceptor(),
    ]);

    // (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (client) {
    //   //
    //   return ApproovHttpClient(ApiConstants.approovConfig);
    // };

    return dio;
  }

  String _getBaseUrl() {
    // if (kReleaseMode) {
    //   return ApiConstants.baseUrl;
    // } else if (kProfileMode) {
    //   return ApiConstants.baseUrlStaging;
    // }
    return ApiConstants.baseUrl;
  }

  static Map<String, String> get defaultHeaders => {
        ApiConstants.contentType: ApiConstants.applicationJson,
        ApiConstants.accept: ApiConstants.applicationJson,
        ApiConstants.apiKey: ApiConstants.apiKeyValue,
      };

  void dispose() {
    if (_dio != null) {
      _dio!.close();
      _dio = null;
    }
  }
}
