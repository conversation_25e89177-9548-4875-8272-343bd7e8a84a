import 'package:cbrs/core/api/constants/api_constants.dart';

class ApiEndpoints {
  ApiEndpoints._();

  static const String baseUrl = ApiConstants.baseUrl;

  //   'https://staging.eaglelionsystems.com/v1.0/cbrs-api';

  // Auth endpoints

  static const String login = '$baseUrl/members/login';
  static const String register = '$baseUrl/members/create';
  static const String verifyEmail = '$baseUrl/members/verify';
  static const String refreshToken = '$baseUrl/members/refresh-token';
  static const String createPin = '$baseUrl/members/setpin';
  static const String verifyPin = '$baseUrl/members/verify-pin';
  static const String forgetPin = '$baseUrl/members/forgot-pin';
  static const String unlinkDevice = '$baseUrl/members/unlink-device';
  static const String deviceCheck = '$baseUrl/members/device-check';

  // Bank Transfer endpoints
  static const String accountLookup =
      '$baseUrl/member-transactions/otherbank/account-enquiry';
  static const String bankTransfer =
      '$baseUrl/member-transactions/otherbank/transfer-to-bank';
  static const String supportedBanks = '$baseUrl/banks/fetch-all';
  static const String validateAmount =
      '$baseUrl/member-transactions/validate-amount';
  static const String transferStatus = '$baseUrl/member-transactions/status';
  static const String walletDetails = '$baseUrl/members';
  static const String transferLimit =
      '$baseUrl/transfer-limits/get-transfer-limit';

  static String getWalletDetailsEndpoint(String memberId) =>
      '$walletDetails/$memberId';

  // User endpoints
  static const String userProfile = '/user/profile';
  static const String changePassword = '/user/change-password';

  // Transaction endpoints
  static const String paginatedBanks = '$baseUrl/banks/paginate';
  static const String transactions = '/transactions';
  static const String transactionDetails = '/transactions/';
  static const String createTransaction = '/transactions/create';

  // Contact endpoints
  static const String contacts = '/contacts';
  static const String contactDetails = '/contacts/';
  static const String addContact = '/contacts/add';

  // Contact Lookup endpoint
  static const String contactLookup = '$baseUrl/members/contact-list-lookup';

  // Profile
  static const String verifyEmailOrPhone = '/members/verify-email-or-phone';
  static const String verifyEmailOrPhoneOtp = '/members/verify';
  static const String resendPhoneOtp = '/members/resend-otp';
  static const String resendEmailVerification =
      '/profile/resend-email-verification';
  static const String getUserData = '/profile';
  static const String updateProfile = '/members/update-profile';
  static const String updatePin = '/members/change-pin';
  static const String deletePicture = '/members/deletepicture';
  static const String uploadPicture = '/members/upload/picture';

  // Wallet Transfer endpoints
  static const String walletTransfer =
      '$baseUrl/member-transactions/wallet/wallet-to-wallet-transfer';

  static const String memberLookup = '/members/member-lookup';
  static const String validateTransferAmount =
      '/member-transactions/transfer/check-transfer-rules';

  // Package endpoints
  static const String packages = '$baseUrl/packages';
  static const String categories = '$baseUrl/categories';
  static const String packageSearch = '$packages/search';
  static const String packagePurchase = '$packages/purchase';

  // Repayment endpoints
  static const String myLoans = '$baseUrl/car-loans';

  static String fetchLoans = '$myLoans/my-loans';

  static String getLoanInfoDetails(String loanID) => '$myLoans/$loanID/details';
  static String getPayUpfront(String loanID) =>
      '$myLoans/$loanID/upfront-payment';
  static String getRepayment(String loanID) =>
      '$myLoans/$loanID/monthly-repayment';

  static String getPaymentHistory(String loanID) =>
      '$myLoans/$loanID/payment-history';
  // pakages
  static String getAllGiftpackages = '$packages/all';
  static String getPackageDetail(String id) => '$packages/$id/fetch';

  static String getPackageCategories = '$packages/categories/all';
  static String getBanners = '$packages/campaigns/all';

  static String getBannerPackages(String bannerId) =>
      '$packages/campaigns/$bannerId/packages';

  static String getBannerCategories(String bannerId) =>
      '$packages/campaigns/$bannerId/categories';

  static String getPackageById(String id) => '$packages/$id';
  static String getCategoryById(String id) => '$categories/$id';

  static const String unseenNotifications = '$baseUrl/inapps/unseen/all';
  static const String seenAllnotifications = '$baseUrl/inapps/see-all';

  static String markNotificationAsRead(String notificationId) =>
      '$baseUrl/inapps/seen/$notificationId';

  static String getInvoiceUrl(String billRefNo) =>
      '$baseUrl/invoices/$billRefNo';

  static const String generateVoucher =
      '$baseUrl/vouchers/member/generate-voucher';

  static const String guestAccountEnquiry =
      '$baseUrl/member-transactions/otherbank/guest-account-enquiry';
  static const String guestLoadFromCard =
      '$baseUrl/member-transactions/otherbank/guest-load-from-card';
  static const String guestConfirmPayment =
      '$baseUrl/member-transactions/otherbank/guest-confirm-payment';

  static String getGuestInvoiceUrl(String billRefNo) =>
      '$baseUrl/invoices/guest/$billRefNo';
  static const String checkVersion = '$baseUrl/versions/check-version';

  static const String linkedAccountsPaginate =
      '$baseUrl/link-accounts/mobile/paginate';
  static const String checkLinkedAccountBalance =
      '$baseUrl/link-accounts/check-balance';
  static const String generateBill =
      '$baseUrl/member-transactions/generate-bill';
  static const String generateLinkCode = '$baseUrl/link-accounts/generate';
  static const String fetchLinkedAccount =
      '$baseUrl/link-accounts/mobile/paginate';

// merchant payment endpoints
  static const String merchantPayment =
      '$baseUrl/member-transactions/wallet/pay-merchant';
  static const String parseMerchantQr = '$baseUrl/qr/parse';
  static const String generateQr = '$baseUrl/qr/generate';

  static const String linkedAccountTransactions =
      '$baseUrl/member-transactions/paginate';

  static const String getTopUpProviders =
      '$baseUrl/merchants/airtime-providers';

  static const String createTopUp =
      '$baseUrl/member-transactions/wallet/pay-merchant';
  static const String internalError = '$baseUrl/members/internal';

  static const String transferResendOtp =
      '$baseUrl/member-transactions/transfer/resend-otp';
  static const String transferConfirmPayment =
      '$baseUrl/member-transactions/confirm-payment';

  // Money Lookup
  static const String sendMoneyRequest = '/member-transactions/generate-bill';
  static String getMoneyRequestList({
    required int page,
    int limit = 100,
    String? currency,
  }) {
    final url =
        '/member-transactions/paginate?transactionType=money_request&limit=$limit&page=$page';
    if (currency != null) {
      return '$url&currency=$currency';
    }

    return url;
  }

  static const String acceptOrRejectOrCancelRequest =
      '/member-transactions/accept-reject-money-request';
  static const String confirmRequest = '/member-transactions/confirm-payment';
  static String requestTransactionsDetail(String id) =>
      '/member-transactions/$id';

  static const String transferConfirmOtp =
      '/member-transactions/transfer/verify-otp';

  //utility
  static const String utlilityPaginate = '$baseUrl/miniapps/apps/paginate';
  static const String utilityPayment =
      '$baseUrl/miniapp-transactions/create-order';
  static const String utilityPrepareOrder =
      '/miniapps/apps/prepare-order-payload';
  static String getAgoraToken(int userId, String chatRoomId) =>
      'https://agoraservertokenconnect-d031c4eaf798.herokuapp.com/rtc/$chatRoomId/publisher/userAccount/$userId/';

  // Add missing endpoints for transactions
  static const String memberTransactionsPaginate =
      '$baseUrl/member-transactions/paginate';

  static String memberTransactionDetails(String id) =>
      '$baseUrl/member-transactions/$id';

  static String validateTransaction(String id) =>
      '$baseUrl/member-transactions/$id/validate';

  static String unredeemedMessage =
      '$baseUrl/member-transactions/wallet/unredeemed-count';

  static String invoice(String billRefNo) => '$baseUrl/invoices/$billRefNo';

  static String getOrderReceipt(String billRefNo) =>
      '$baseUrl/invoices/guest/$billRefNo';

  static const String userAvatars = '$baseUrl/members/avatars';
  static const String bulkMemberLookUp = '$baseUrl/members/bulk-member-lookup';

  // Transaction OTP and Payment endpoints
  static const String confirmPayment =
      '$baseUrl/member-transactions/confirm-payment';
  static const String verifyOtp =
      '$baseUrl/member-transactions/transfer/verify-otp';
  static const String resendOtp =
      '$baseUrl/member-transactions/transfer/resend-otp';

  // car loans api
  static const String carLoanBanks = '$baseUrl/car-loans/banks';
  static const String carBodiesPaginate = '$baseUrl/carBodies/paginate';
  static const String carsPaginate = '$baseUrl/cars/paginate';
  static const String carLoansPaymentInfo = '$baseUrl/car-loans/payment-info';
  static const String calculateLoanPayment = '$baseUrl/loans/calculate-payment';

  static const String carLoansApply = '$baseUrl/car-loans/apply';

  static String generateApplicationTransaction(String loanApplicationId) =>
      '$baseUrl/car-loans/$loanApplicationId/application-fee';

  static const String confirmLoanPayment = '$baseUrl/car-loans/confirm-payment';

  static String getLoanTerms(String bankId) =>
      '$baseUrl/car-loans/loan-terms/latest/$bankId';

  static String getCarById = '$baseUrl/cars/car';

  // agent
  static String agentsSearch = '$baseUrl/agents/search';

  // change to birr
  static String changeToBirr =
      '$baseUrl/member-transactions/wallet/change-to-birr';

// load to wallet
  static String loadToWallet =
      '$baseUrl/member-transactions/wallet/load-to-wallet';

  static String loadToWalletStatus =
      '$baseUrl/member-transactions/wallet/load-to-wallet-status';

  static String getLoadWalletDetails(String billRefNo) =>
      '$baseUrl/member-transactions/wallet/load-to-wallet-status/$billRefNo';

  static String getMerchantByTillNumber(String tillNumber) =>
      '$baseUrl/merchants/$tillNumber/fetch';

  // hoosue
  static String getHouses = '$baseUrl/houses/all';
  static String getHouseTypes = '$baseUrl/houses/types/all';

  // Identity Verification endpoints
  static const String uploadDocument = '$baseUrl/members/upload-documents';

  // ComplyCube SDK Integration endpoints
  static const String complyCubeCreateClient = '$baseUrl/kyc/client/create';
  static const String complyCubeGenerateToken =
      '$baseUrl/kyc/sdk/generate-token';
  static const String complyCubeProcessVerification =
      '$baseUrl/kyc/check-status';
  static const String complyCubeUploadLivePhoto =
      '$baseUrl/kyc/upload-live-photo';
  static const String complyCubeVerifyHook =
      '$baseUrl/kyc/identity/check/verify/hook';

  // ComplyCube identity check endpoints
  static String complyCubeCreateIdentityCheck(
    String clientId,
    String checkId,
  ) =>
      '$baseUrl/kyc/identity/check/create/$clientId/$checkId';

  // New endpoint for creating identity check with document and live photo IDs
  static String complyCubeCreateIdentityCheckWithIds(
    String clientId,
    String livePhotoId,
    String documentId,
  ) =>
      '$baseUrl/kyc/identity/check/create/$clientId/$livePhotoId/$documentId';

  static const String miniStatements = '$baseUrl/invoices/ministatement/fetch';
  // My_connect
  static const String myConnect = '$baseUrl/members/get-connect';
  static String sendConnectionRequest(String recepientId) =>
      '$baseUrl/members/send-connect/$recepientId';
  static String acceptConnectionRequest(String requestId) =>
      '$baseUrl/members/accept-decline-request/$requestId';

  static String updateQrStatus(String qrId) => '$baseUrl/qr/$qrId/update';

  static String dashenExchangeRate = '$baseUrl/exchanges/dashen-exchange-rate';
}
