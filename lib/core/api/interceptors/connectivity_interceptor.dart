import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';

class ConnectivityInterceptor extends Interceptor {

  ConnectivityInterceptor(this._connectivity);
  final Connectivity _connectivity;

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final connectivityResult = await _connectivity.checkConnectivity();
    
    if (connectivityResult == ConnectivityResult.none) {
      return handler.reject(
        DioException(
          requestOptions: options,
          type: DioExceptionType.connectionError,
          error: const NetworkException(
            message: ApiConstants.connectionError,
          ),
        ),
      );
    }
    
    return handler.next(options);
  }
}
