class Result<T> {
  final T? data;
  final ApiError? error;
  final bool isSuccess;

  Result._({this.data, this.error, required this.isSuccess});

  factory Result.success(T data) {
    return Result._(data: data, isSuccess: true);
  }

  factory Result.failure(ApiError error) {
    return Result._(error: error, isSuccess: false);
  }

  R fold<R>(
    R Function(T data) onSuccess,
    R Function(ApiError error) onFailure,
  ) {
    return isSuccess ? onSuccess(data as T) : onFailure(error!);
  }
}

class ApiError {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? errors;
  final ErrorType type;

  ApiError({
    required this.message,
    this.statusCode,
    this.errors,
    this.type = ErrorType.unknown,
  });

  factory ApiError.network() {
    return ApiError(
      message:
          'No internet connection. Please check your connection and try again.',
      type: ErrorType.network,
    );
  }

  factory ApiError.timeout() {
    return ApiError(
      message: 'Request timed out. Please try again.',
      type: ErrorType.timeout,
    );
  }

  factory ApiError.server(String message, int statusCode) {
    return ApiError(
      message: message,
      statusCode: statusCode,
      type: ErrorType.server,
    );
  }

  factory ApiError.unauthorized() {
    return ApiError(
      message: 'Your session has expired. Please login again.',
      statusCode: 401,
      type: ErrorType.unauthorized,
    );
  }
}

enum ErrorType {
  network,
  timeout,
  server,
  unauthorized,
  validation,
  unknown,
}
