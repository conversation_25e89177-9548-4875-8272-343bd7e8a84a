import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:go_router/go_router.dart';

class SessionTimeoutConfig {
  SessionTimeoutConfig._();

  static const Duration defaultTimeout = Duration(minutes: 2);

  static const Duration complyCubeTimeout = Duration(minutes: 30);

  static const Set<String> extendedTimeoutRoutes = {
    AppRouteName.identityVerification,
    AppRouteName.complyCubeVerification,
    AppRouteName.faceScanCamera,
    AppRouteName.documentScan,
  };

  static Duration getTimeoutForRoute(String? currentRoute) {
    if (currentRoute == null) return defaultTimeout;

    for (final route in extendedTimeoutRoutes) {
      if (currentRoute.contains(route)) {
        return complyCubeTimeout;
      }
    }

    return defaultTimeout;
  }

  static Duration getTimeoutForContext(GoRouterState? state) {
    if (state == null) return defaultTimeout;

    final currentLocation = state.fullPath ?? state.path ?? '';
    return getTimeoutForRoute(currentLocation);
  }

  static bool isExtendedTimeoutRoute(String? currentRoute) {
    return getTimeoutForRoute(currentRoute) == complyCubeTimeout;
  }
}
