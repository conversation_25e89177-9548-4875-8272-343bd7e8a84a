import 'package:flutter/foundation.dart';

class GetInitialNames {
  static String getInitials(String name,
      {bool showInitialDebug = false, bool shouldReturnLowerCase = false}) {
    if (name.isEmpty) {
      return '?';
    }
    if (showInitialDebug) debugPrint("getInitials ⛑️ $name");

    // Split by space and filter out empty strings
    final nameParts =
        name.trim().split(' ').where((part) => part.isNotEmpty).toList();
    if (showInitialDebug) debugPrint("filtered parts ⛑️ $nameParts");

    if (nameParts.length >= 2) {
      // Safely get first character of first two non-empty parts
      final firstInitial = nameParts[0].isNotEmpty ? nameParts[0][0] : '?';
      final secondInitial = nameParts[1].isNotEmpty ? nameParts[1][0] : '?';

      if (shouldReturnLowerCase) {
        return '${firstInitial.toLowerCase()}${secondInitial.toLowerCase()}';
      }
      return '${firstInitial}${secondInitial}'.toUpperCase();
    }

    // Single name or fallback
    if (nameParts.isNotEmpty && nameParts[0].isNotEmpty) {
      final singleName = nameParts[0];
      if (shouldReturnLowerCase) {
        return singleName
            .substring(0, singleName.length >= 2 ? 2 : 1)
            .toLowerCase();
      }
      return singleName
          .substring(0, singleName.length >= 2 ? 2 : 1)
          .toUpperCase();
    }

    // Ultimate fallback
    return '?';
  }
}
