String getNameInitial(String firstName, String lastName) =>
    '${firstName.isNotEmpty ? firstName[0] : ''}'
    '${lastName.isNotEmpty ? lastName[0] : ''}';

String separateNames(String value) {
  if (value.isEmpty) return '';

  final names =
      value.trim().split(' ').where((name) => name.isNotEmpty).toList();

  if (names.isEmpty) return '';

  if (names.length > 1 && names.first.isNotEmpty && names.last.isNotEmpty) {
    return '${names.first[0]}${names.last[0]}'.toUpperCase();
  }

  if (names.isNotEmpty && names.first.isNotEmpty) {
    return names.first[0].toUpperCase();
  }

  return '';
}
