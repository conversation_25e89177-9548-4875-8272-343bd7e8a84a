import 'package:flutter/material.dart';
import 'package:phone_form_field/phone_form_field.dart';

class ExtractedPhoneNumberData {
  final String dialCode, isoCode, phoneNumber, selectedCountryIsoCode;

  ExtractedPhoneNumberData(
      {required this.dialCode,
      required this.isoCode,
      required this.phoneNumber,
      required this.selectedCountryIsoCode});
}

class ExtractPhoneNumber {
  static Future<ExtractedPhoneNumberData> extractCountryCode(
      String phoneNumber) async {
    try {
      final parsedNumber = await PhoneNumber.parse(
        phoneNumber,
      );

    return  ExtractedPhoneNumberData(
          dialCode: parsedNumber.countryCode,
          isoCode: parsedNumber.isoCode.toString(),
          phoneNumber: parsedNumber.nsn,
          selectedCountryIsoCode:
              parsedNumber.isoCode.toString().split('.')[1]);

      // print('Country Code: $parsedNumber.countryCode');
    } catch (e) {
     return ExtractedPhoneNumberData(
          dialCode: "",
          isoCode: '',
          phoneNumber: '',
          selectedCountryIsoCode: '');

      print('Error parsing phone number: $e');
    }
  }
}
