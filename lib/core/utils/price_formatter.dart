class PriceFormatter {
  static String formatPrice(String price) {
    // Remove the dollar sign
    String numStr = price.replaceAll('\$', '');

    // Parse the string to double
    double number = double.parse(numStr);

    // Format the number with comma separators
    RegExp reg = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    String formatted = number
        .toStringAsFixed(2)
        .replaceAllMapped(reg, (Match match) => '${match[1]},');

    return '\$$formatted';
  }

  static double parsePrice(String price) {
    String numStr = price.replaceAll('\$', '').replaceAll(',', '');
    return double.parse(numStr);
  }

  static String formatCompactPrice(String price) {
    String numStr = price.replaceAll('\$', '');
    double number = double.parse(numStr);

    if (number >= 1000000) {
      return '\$${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '\$${(number / 1000).toStringAsFixed(1)}K';
    }
    return formatPrice(price);
  }
}
