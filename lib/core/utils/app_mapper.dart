import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

class AppMapper {
  /// Returns an empty string if the value is null or not a valid string.

  static String safeString(dynamic value) {
    if (value == null) return '';
    return value.toString();
  }

  static String safeName(dynamic value) {
    if (value == null || value.toString().trim().isEmpty) return '';

    final name = value.toString().trim();
    return name[0].toUpperCase() + name.substring(1).toLowerCase();
  }

  /// Returns `0` if the value is null, not an int, or not a parsable string.
  static int safeInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      final intValue = int.tryParse(value);
      return intValue ?? 0;
    }
    if(value is double){
           final intValue = value.toInt();
      return intValue ?? 0;
    }
    return 0;
  }

  /// Returns `0.0` if the value is null, not a double, or not a parsable number.
  static double safeDouble(dynamic value) {
    if (value == null) return 0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      final doubleValue = double.tryParse(value);
      return doubleValue ?? 0.0;
    }
    return 0;
  }

  /// Returns `false` if the value is null or not a valid bool.
  /// Also handles strings like "true", "false" and integers like 1 or 0.
  static bool safeBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is String) return value.toLowerCase() == 'true';
    if (value is int) return value != 0;
    return false;
  }

  /// Returns an empty list if the value is null or not a valid list.
  static List<T> safeList<T>(dynamic value) {
    if (value == null || value is! List) {
      return [];
    }
    return value.cast<T>();
  }

  /// Returns an empty map if the value is null or not a valid map.
  static Map<String, dynamic> safeMap(dynamic value) {
    if (value == null || value is! Map) {
      return {};
    }
    return value.map((key, val) => MapEntry(key.toString(), val));
  }

  /// Returns a default DateTime if the value is null or not a valid date.
  static DateTime safeDateTime(dynamic value) {
    if (value == null) return DateTime.fromMillisecondsSinceEpoch(0);
    if (value is DateTime) return value;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (_) {
        return DateTime.fromMillisecondsSinceEpoch(0);
      }
    }
    return DateTime.fromMillisecondsSinceEpoch(0);
  }

  /// /// Returns date in the format of abbreviated Jan 12, 2024
  static String safeFormattedDate(dynamic value, {bool hasTimeStamp = true}) {
    try {
      if (safeString(value).isNotEmpty) {
        final date = DateTime.parse(value.toString()).toLocal();
        return DateFormat(
          hasTimeStamp ? 'MMM dd, yyyy - hh:mm a' : 'MMM dd, yyyy',
        ).format(date);
      }
      return '';
    } catch (e) {
      return '';
    }
  }

  /// this method return string of number in the format 1200,09.19
  static String safeFormattedNumberWithDecimal(dynamic value) {
    if (safeString(value).isEmpty) return '';
    try {
      final number = double.parse(value.toString());

      final valueData = NumberFormat('#,##0.00').format(number);
      return valueData;
    } catch (e) {
      return '';
    }
  }

  /// this method return string of number in the format 1200

  static String safeFormattedNumber(dynamic value) {
    if (safeString(value).isEmpty) return '';

    try {
      final number = int.parse(value.toString());
      return NumberFormat('#,##0').format(number);
    } catch (e) {
      return '';
    }
  }
}
