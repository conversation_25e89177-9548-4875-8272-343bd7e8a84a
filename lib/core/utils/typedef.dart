import 'package:cbrs/core/errors/failures.dart';
// import 'package:cbrs/features/gift_packages/domain/entities/package_item.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

typedef ResultFuture<T> = Future<Either<Failure, T>>;
typedef ResultVoid = ResultFuture<void>;
typedef ResultStream<T> = Stream<Either<Failure, T>>;
typedef DataMap = Map<String, dynamic>;
typedef ApiResponse = Response<DataMap>;
typedef ApiCall<T> = Future<Response<T>> Function();
// typedef PackageActionCallback = void Function(PackageItem item);

