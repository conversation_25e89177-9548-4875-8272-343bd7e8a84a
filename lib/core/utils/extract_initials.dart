class ExtractInitials {
    static String extractInitials(String name) {
    final trimmedName = name.trim();
    if (trimmedName.isEmpty) return '??';

    final words = trimmedName.split(RegExp(r'\s+'));
    final firstInitial = words.isNotEmpty ? words[0][0] : '';
    final secondInitial = words.length > 1 ? words[1][0] : '';

    return (firstInitial + secondInitial)
        .toUpperCase()
        .padRight(2, '?')
        .substring(0, 2);
  }
}