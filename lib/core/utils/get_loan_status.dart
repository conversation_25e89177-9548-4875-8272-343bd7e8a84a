import 'package:flutter/material.dart';

String getLoanStatusMessage(String status) {
  // debugPrint(' 🚫Loan status: $status');
  switch (status) {
    // case "initiated":
    // case "pending":
    // case "application_fee_pending":
    //   return "pending";

    case 'approved':
    case 'upfront_payment_paid':
      return 'preparing';
    case 'upfront_payment_pending':
      return 'approved';
    case 'rejected':
      return 'rejected';
    case 'active':
      return 'active';
    case 'completed':
      return 'completed';
    default:
      return 'pending';
  }
}
