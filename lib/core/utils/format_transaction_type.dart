class FormatTransactionType {
  /*
  
  String getTransactionTypeIcon(String transactionType) {
    switch (transactionType) {
      case 'bank_transfer':
        return MediaRes.bankIcon;
      case 'wallet_transfer':
        return MediaRes.walletTransferIcon;
      case 'cash_pickup':
        return MediaRes.ordersIcon;
      case 'top_up':
        return MediaRes.mobileTopUpIcon;

      case 'wallet_prefund':
        return MediaRes.walletTransferIcon;
      case 'load_to_wallet':
        return MediaRes.loadToWalletIcon;
      case 'add_money':
        return MediaRes.addMoneyIcon;
      case 'change_to_birr':
        return MediaRes.changeToBirrIcon;

      case 'guest_send_money':
        return MediaRes.walletTransferIcon;

      case 'gift_package':
        return MediaRes.sentGiftCardIcon;

      case 'redeem':
        return MediaRes.sentGiftCardIcon;

      case 'money_request':
        return isCurrentUserSender
            ? MediaRes.walletTransferIcon
            : MediaRes.walletTransferReceivedIcon;

      default:
        return MediaRes.changeToBirrIcon;
    }
  }
*/
  static  String getTransactionTypeLabel(String transactionType) {
    switch (transactionType) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'wallet_transfer':
        return 'Wallet Transfer';
      case 'cash_pickup':
        return 'Cash Pickup';
      case 'top_up':
        return 'Top Up';

      case 'wallet_prefund':
        return 'Wallet Prefund';
      case 'load_to_wallet':
        return 'Load to Wallet';
      case 'add_money':
        return 'Add Money';
      case 'change_to_birr':
        return 'Change to Birr';

      case 'guest_send_money':
        return 'Guuest Send Money';

      case 'gift_package':
        return 'Gift Package';

      case 'redeem':
        return 'Redeem';

      case 'money_request':
        return 'Money Request';

      default:
        return '';
    }
  }
}
