import 'dart:ui';

import 'package:flutter/material.dart';

class CustomBottomSheet extends StatelessWidget {
  const CustomBottomSheet({
    required this.child,
    super.key,
    this.maxHeight,
    this.minHeight = 50,
    this.backgroundColor,
    this.borderRadius,
  });
  final Widget child;
  final double minHeight;
  final BorderRadius? borderRadius;
  final double? maxHeight;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final calculatedHeight = maxHeight != null
        ? (maxHeight! > screenHeight ? screenHeight : maxHeight)
        : null;

    return Container(
      constraints: BoxConstraints(
        minHeight: minHeight,
        maxHeight: calculatedHeight ?? screenHeight * 0.9,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        borderRadius: borderRadius ??
            const BorderRadius.vertical(
              top: Radius.circular(16),
            ),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: child,
    );
  }
}

void showCustomBottomSheet(
  BuildContext context,
  Widget child, {
  double? maxHeight,
  Color? backgroundColor,
  BorderRadius? borderRadius,
  EdgeInsets? margin,
}) {
  showModalBottomSheet<void>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => CustomBottomSheet(
      maxHeight: maxHeight,
      borderRadius: borderRadius,
      backgroundColor: backgroundColor,
      child: child,
    ),
  );
}
