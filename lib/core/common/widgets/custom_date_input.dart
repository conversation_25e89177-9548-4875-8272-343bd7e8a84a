import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomDateInput extends StatefulWidget {
  final TextEditingController controller;
  final ThemeData theme;
  final bool isRequired;
  final String label;
  final FormFieldValidator<String>? validator;

  const CustomDateInput({
    super.key,
    required this.controller,
    required this.theme,
    this.isRequired = false,
    this.label = 'Date of Birth (G.C)',
    this.validator,
  });

  @override
  State<CustomDateInput> createState() => _CustomDateInputState();
}

class _CustomDateInputState extends State<CustomDateInput> {
  final _dateFormat = DateFormat('dd/MM/yyyy');
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    if (widget.controller.text.isNotEmpty) {
      try {
        _selectedDate = _dateFormat.parse(widget.controller.text);
      } catch (e) {
      }
    }
  }

  void _showDatePicker() {
    final now = DateTime.now();

    final eighteenYearsAgo = DateTime(now.year - 18, now.month, now.day);
    final hundredYearsAgo = DateTime(now.year - 100, now.month, now.day);
    int _selectedYear = (DateTime.now().year - 18);
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.5,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Handle and header
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20.r)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle
                    Container(
                      width: 36.w,
                      height: 4.h,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2.r),
                      ),
                    ),
                    SizedBox(height: 16.h),
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Select Date of Birth (G.C)',
                          style: GoogleFonts.outfit(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(
                            Icons.close,
                            size: 24.sp,
                            color: Colors.black54,
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Date Picker
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Theme(
                    data: ThemeData(
                      platform: TargetPlatform.iOS,
                      colorScheme: ColorScheme.light(
                        primary: widget.theme.primaryColor,
                        onSurface: Colors.black87,
                      ),
                    ),
                    child: CupertinoTheme(
                      data: CupertinoThemeData(
                        primaryColor: widget.theme.primaryColor,
                        brightness: Brightness.light,
                        textTheme: CupertinoTextThemeData(
                          dateTimePickerTextStyle: GoogleFonts.outfit(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      child: CupertinoDatePicker(
                        
                        mode: CupertinoDatePickerMode.date,
                        // initialDateTime: _selectedDate ?? now,
                        // minimumDate: hundredYearsAgo,
        
                        initialDateTime: DateTime(_selectedYear),
                        minimumYear: 1900,
                        maximumYear: (DateTime.now().year - 18),
                        onDateTimeChanged: (DateTime newDate) {

                          debugPrint('New data ${newDate}');
                          setState(() {
                            if (newDate.isAfter(eighteenYearsAgo)) {
                              _selectedDate = eighteenYearsAgo;
                              widget.controller.text =
                                  _dateFormat.format(eighteenYearsAgo);
                            } else {
                              _selectedDate = newDate;
                              widget.controller.text =
                                  _dateFormat.format(newDate);
                            }
                          });
                        },
                        dateOrder: DatePickerDateOrder.dmy,
                        backgroundColor: Colors.transparent,
                      ),
                    ),
                  ),
                ),
              ),
        
              // Action button
              SafeArea(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: SizedBox(
                    width: double.infinity,
                    child: CustomRoundedBtn(btnText: "Confirm", isLoading: false, onTap: () => Navigator.pop(context),),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: widget.label,
                style: GoogleFonts.plusJakartaSans(
                  color: Colors.grey.shade600,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (widget.isRequired)
                TextSpan(
                  text: ' *',
                  style: GoogleFonts.plusJakartaSans(
                    color: Colors.red,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: widget.controller,
          readOnly: true,
          onTap: _showDatePicker,
          style: GoogleFonts.plusJakartaSans(
            color: widget.theme.colorScheme.onSurface,
            fontSize: 15.sp,
          ),
          decoration: InputDecoration(
            hintText: 'Select Date of Birth',
            hintStyle: GoogleFonts.outfit(
              fontSize: 15.sp,
              color: widget.theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            suffixIcon: InkWell(
              onTap: _showDatePicker,
              child: Container(
                padding: EdgeInsets.all(16.w),
                child: Image.asset(
                  MediaRes.calendarIcon,
                  width: 4.w,
                  height: 4.h,
                  fit: BoxFit.contain,
                ),
              ),
            ),
            filled: true,
            fillColor: widget.theme.colorScheme.onTertiary,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide.none,
            ),
            contentPadding: EdgeInsets.symmetric(
              vertical: 16.h,
              horizontal: 20.w,
            ),
          ),
          validator: widget.validator,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        ),
      ],
    );
  }
}
