// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

class CustomPagePadding extends StatelessWidget {
  const CustomPagePadding({
    required this.child,
    super.key,
    this.decoration,
    this.padding,
    this.margin,
    this.bottom = 0,
  });
  final Widget child;
  final Decoration? decoration;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  final double bottom;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: decoration,
      margin: margin,
      padding: padding ??
          EdgeInsets.only(top: 16, left: 16, right: 16, bottom: bottom),
      child: child,
    );
  }
}
