import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';

class CustomMenuContainer extends StatelessWidget {
  const CustomMenuContainer({
    required this.onTap,
    required this.title,
    required this.description,
    required this.containerIcon,
    this.iconColor,
    super.key,
  });

  final VoidCallback onTap;
  final String title;
  final String description;
  final String containerIcon;
  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(14),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x0F000000),
                    blurRadius: 24,
                  ),
                ],
              ),
              child: Row(
                children: [
                  // icons
                  Container(
                    padding: const EdgeInsets.all(12),
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(.15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Image.asset(
                      containerIcon,
                      width: 32,
                      height: 32,
                      color: iconColor,
                    ),
                  ),
                  const SizedBox(width: 10),
                  //column
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomBuildText(
                          text: title,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        const SizedBox(height: 4),
                        CustomBuildText(
                          text: description,
                          color: Colors.black.withOpacity(0.4),
                          fontSize: 12,
                          fontWeight: FontWeight.w300,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Colors.black.withOpacity(0.3),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
