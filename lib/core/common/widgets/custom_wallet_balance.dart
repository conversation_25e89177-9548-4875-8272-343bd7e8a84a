import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_event.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';

class CustomWalletBalance extends StatefulWidget {
  const CustomWalletBalance({
    this.alwaysShowETBColor = false,
    this.balanceLabel = 'Wallet Balance',
    this.customBalance,
    this.customWalletType,
    super.key,
  });

  final bool alwaysShowETBColor;
  final String balanceLabel;

  final double? customBalance;
  final String? customWalletType;

  @override
  State<CustomWalletBalance> createState() => _CustomWalletBalanceState();
}

class _CustomWalletBalanceState extends State<CustomWalletBalance> {
  bool showBalance = false;

  void handleShowBalance() {
    setState(() => showBalance = !showBalance);
    if (!showBalance && widget.customBalance == null) {
      context.read<WalletBalanceBloc>().add(
            FetchWalletEvent(
              isUsdWallet: GlobalVariable.currentlySelectedWallet == 'USD',
            ),
          );
    }
  }

  String formatAmount(double amount, String currencyCode) {
    final symbol = _getCurrencySymbol(currencyCode);
    final formatter =
        NumberFormat.currency(locale: 'en_US', symbol: '$symbol ');
    return formatter.format(amount);
  }

  String _getCurrencySymbol(String code) {
    switch (code.toUpperCase()) {
      case 'USD':
        return r'$';
      case 'ETB':
        return 'ETB';
      default:
        return code;
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    final state = context.read<WalletBalanceBloc>().state;

    if (state is! WalletLoadedState) {
      context.read<WalletBalanceBloc>().add(
            FetchWalletEvent(
              isUsdWallet: GlobalVariable.currentlySelectedWallet == 'USD',
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    // final walletState = context.watch<WalletBalanceBloc>().state;

    return BlocBuilder<WalletBalanceBloc, HomeState>(
      builder: (context, walletState) {
        // if (9 > 0) return Text("Balance");

        if (walletState is WalletLoadingState) {
          return const _BalanceShimmer();
        }

        if (walletState is HomeError) {
          return const Icon(Icons.refresh); // Add a retry button if needed
        }

        if (walletState is WalletLoadedState) {
          final balance = widget.customBalance != null
              ? widget.customBalance!
              : GlobalVariable.currentlySelectedWallet == 'USD'
                  ? walletState.usdBalance
                  : walletState.etbBalance;
          final currency = widget.customWalletType != null
              ? widget.customWalletType!
              : GlobalVariable.currentlySelectedWallet == 'USD'
                  ? 'USD'
                  : 'ETB';
          final formattedBalance = formatAmount(balance, currency);

          return Center(
            child: GestureDetector(
              onTap: handleShowBalance,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(32.r),
                  color: widget.alwaysShowETBColor
                      ? LightModeTheme().primaryColorBirr.withOpacity(0.2)
                      : Theme.of(context).secondaryHeaderColor,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${widget.balanceLabel}: ',
                      style: GoogleFonts.outfit(
                        fontSize: 14.sp,
                        color: Colors.black87,
                      ),
                    ),
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomBuildText(
                            text: showBalance ? formattedBalance : '  ********',
                            style: GoogleFonts.outfit(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: Image.asset(
                        showBalance ? MediaRes.eyeClose : MediaRes.eyeOpen,
                        width: 16,
                        height: 16,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}

class _BalanceShimmer extends StatelessWidget {
  const _BalanceShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 40,
        width: 200,
        decoration: BoxDecoration(
          color: Colors.grey,
          borderRadius: BorderRadius.circular(32),
        ),
      ),
    );
  }
}
