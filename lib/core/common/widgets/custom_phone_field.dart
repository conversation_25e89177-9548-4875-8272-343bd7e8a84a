import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:phone_form_field/phone_form_field.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Enum representing different app release platforms
enum ReleasePlatform { playStore, huawei, other }

/// List of country codes to exclude for Huawei release
const List<String> _huaweiExcludedCountries = ['HK', 'MO', 'TW', 'AC'];

class CustomPhoneInput extends StatefulWidget {
  final String initialCountryCode;
  final TextEditingController phoneController;
  final Function(String, String) onCountrySelected;
  final FormFieldValidator<String>? validator;
  final ThemeData theme;
  final bool isRequired;
  final bool readOnly;
  final String selectedCountryIsoCode;
  final bool validateOnChange;
  final bool ethiopiaOnly;
  final bool disableCountrySelection;
  final void Function(String)? onChange;
  final List<String> excludedCountries;
  final ReleasePlatform releasePlatform;

  const CustomPhoneInput(
      {super.key,
      required this.initialCountryCode,
      required this.phoneController,
      required this.onCountrySelected,
      this.validator,
      required this.theme,
      this.isRequired = false,
      this.readOnly = false,
      this.selectedCountryIsoCode = 'US',
      this.validateOnChange = false,
      this.ethiopiaOnly = false,
      this.disableCountrySelection = false,
      this.excludedCountries = const [],
      this.releasePlatform = ReleasePlatform.other,
      this.onChange});

  @override
  State<CustomPhoneInput> createState() => CustomPhoneInputState();
}

class CustomPhoneInputState extends State<CustomPhoneInput> {
  late PhoneController _phoneFieldController;
  bool _isBottomSheetOpen = false;
  TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _countries = [];
  List<Map<String, String>> _filteredCountries = [];
  List<String>? _cachedExcludedCountries;

  List<String> get _effectiveExcludedCountries {
    if (_cachedExcludedCountries == null) {
      _cachedExcludedCountries = [...widget.excludedCountries];
      if (widget.releasePlatform == ReleasePlatform.huawei) {
        for (final code in _huaweiExcludedCountries) {
          if (!_cachedExcludedCountries!.contains(code)) {
            _cachedExcludedCountries!.add(code);
          }
        }
      }
    }
    return _cachedExcludedCountries!;
  }

  PhoneController get phoneFieldController => _phoneFieldController;

  void closeBottomSheet() {
    if (mounted && Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  @override
  void initState() {
    super.initState();
    try {
      _phoneFieldController = PhoneController(
        initialValue: widget.phoneController.text.isNotEmpty
            ? PhoneNumber.parse(
                '${widget.initialCountryCode}${widget.phoneController.text}',
              )
            : PhoneNumber.parse(
                widget.initialCountryCode,
              ),
      );
    } catch (e) {
      _phoneFieldController = PhoneController();
    }

    _phoneFieldController.addListener(_onPhoneChanged);
  }

  @override
  void didUpdateWidget(CustomPhoneInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.excludedCountries != widget.excludedCountries ||
        oldWidget.releasePlatform != widget.releasePlatform) {
      _cachedExcludedCountries = null;
    }
  }

  void _onPhoneChanged() {
    if (_phoneFieldController.value != null) {
      final phoneNumber = _phoneFieldController.value!;
      widget.phoneController.text = phoneNumber.nsn;
      widget.onCountrySelected(
        '+${phoneNumber.countryCode}',
        phoneNumber.isoCode.name,
      );
      setState(() {});
    }
  }

  void _showCountryPicker() {
    setState(() => _isBottomSheetOpen = true);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Bottom sheet handle
              Container(
                margin: const EdgeInsets.only(top: 8),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Title and close button
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 24, 16, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Country List',
                      style: GoogleFonts.plusJakartaSans(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, size: 24),
                    ),
                  ],
                ),
              ),
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (query) {
                      setModalState(() {
                        if (query.isEmpty) {
                          _filteredCountries = _countries;
                        } else {
                          _filteredCountries = _countries.where((country) {
                            final name = country['name']!.toLowerCase();
                            final code = country['code']!.toLowerCase();
                            final dialCode = country['dialCode']!.toLowerCase();
                            return name.contains(query.toLowerCase()) ||
                                code.contains(query.toLowerCase()) ||
                                dialCode.contains(query.toLowerCase());
                          }).toList();
                        }
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Search country',
                      prefixIcon: const Icon(Icons.search, size: 20),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
              ),
              // Country list
              Expanded(
                child: ListView.separated(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _filteredCountries.length,
                  separatorBuilder: (_, __) => const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final country = _filteredCountries[index];
                    return ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: CountryFlag.fromCountryCode(
                        country['code']!,
                        height: 24.h,
                        width: 36.w,
                      ),
                      title: Text(
                        country['name']!,
                        style: GoogleFonts.plusJakartaSans(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      trailing: Text(
                        country['dialCode']!,
                        style: GoogleFonts.plusJakartaSans(
                          fontSize: 16.sp,
                          color: widget.theme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      onTap: () {
                        widget.onCountrySelected(
                          country['dialCode']!,
                          country['code']!,
                        );
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    ).whenComplete(() {
      setState(() {
        _isBottomSheetOpen = false;
        _searchController.clear();
        _filteredCountries = _countries;
      });
    });
  }

  @override
  void dispose() {
    _phoneFieldController.removeListener(_onPhoneChanged);
    _phoneFieldController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.isRequired)
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Phone Number',
                  style: GoogleFonts.plusJakartaSans(
                    color: Colors.grey.shade600,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: ' *',
                  style: GoogleFonts.plusJakartaSans(
                    color: Colors.red,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        SizedBox(height: 4.h),
        PhoneFormField(
          controller: _phoneFieldController,
          enabled: !widget.readOnly,
          autovalidateMode: widget.validateOnChange
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.onUnfocus,
          countryButtonStyle: CountryButtonStyle(
            flagSize: 24.h,
          ),
          inputFormatters: [
            LengthLimitingTextInputFormatter(15),
          ],
          countrySelectorNavigator: CountrySelectorNavigator.modalBottomSheet(
            height: MediaQuery.of(context).size.height * 0.9,
            countries: _effectiveExcludedCountries.isEmpty
                ? null
                : IsoCode.values
                    .where((code) =>
                        !_effectiveExcludedCountries.contains(code.name))
                    .toList(),
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: widget.theme.colorScheme.onTertiary,
            hintText: 'Enter phone number',
            hintStyle: GoogleFonts.outfit(
              color: widget.theme.colorScheme.onSurface.withOpacity(0.5),
              fontSize: 15.sp,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 14.h,
            ),
            errorStyle: const TextStyle(height: 0),
          ),
          validator: (phoneNumber) {
            // Skip validation if field is empty and not required
            if (!widget.isRequired &&
                (_phoneFieldController.value == null ||
                    widget.phoneController.text.isEmpty)) {
              return null;
            }

            if (widget.phoneController.text.isNotEmpty) {
              if (phoneNumber == null || !phoneNumber.isValid()) {
                return 'Invalid phone number';
              }

              if (widget.ethiopiaOnly && phoneNumber.countryCode != '251') {
                return 'Only Ethiopian phone numbers are allowed';
              }

              // Special validation for Ethiopian numbers
              if (phoneNumber.countryCode == '251') {
                final nationalNumber = phoneNumber.nsn;
                if (nationalNumber.length < 9 || nationalNumber.length > 12) {
                  return 'Ethiopian number must be between 9 and 12 digits';
                }
                if (!nationalNumber.startsWith('9') &&
                    !nationalNumber.startsWith('7')) {
                  return 'Ethiopian number must start with 9 or 7';
                }
              }

              // Additional custom validation if provided
              if (widget.validator != null) {
                final validatorResult = widget.validator!(phoneNumber.nsn);
                if (validatorResult != null) {
                  return validatorResult;
                }
              }
            }

            return null;
          },
          onChanged: (phoneNumber) {
            if (phoneNumber != null) {
              widget.phoneController.text = phoneNumber.nsn;
              widget.onCountrySelected(
                '+${phoneNumber.countryCode}',
                phoneNumber.isoCode.name,
              );

              // Check if phone number is valid and dismiss keyboard
              if (phoneNumber.isValid()) {
                if (phoneNumber.countryCode == '251') {
                  final nationalNumber = phoneNumber.nsn;
                  if (nationalNumber.length >= 9 &&
                      nationalNumber.length <= 12 &&
                      (nationalNumber.startsWith('9') ||
                          nationalNumber.startsWith('7'))) {
                    FocusManager.instance.primaryFocus?.unfocus();
                  }
                } else {
                  FocusManager.instance.primaryFocus?.unfocus();
                }
              }

              if (widget.onChange != null) {
                widget
                    .onChange!('+${phoneNumber.countryCode}${phoneNumber.nsn}');
              }
              setState(() {});
            }
          },
        ),
      ],
    );
  }
}
