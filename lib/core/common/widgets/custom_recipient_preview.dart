import 'package:cached_network_image/cached_network_image.dart';
import 'package:cbrs/core/utils/get_initial_names.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomRecipientPreview extends StatelessWidget {
  const CustomRecipientPreview(
      {super.key,
      required this.recipentAvatar,
      required this.recipientName,
      this.recipientEmail = '',
      this.recipientPhone = '',
      this.onTap,
      this.borderColor,
      this.leadingBgColor,
      this.subTitleColor});
  final VoidCallback? onTap;
  final String recipentAvatar, recipientEmail, recipientName, recipientPhone;
  final Color? borderColor, leadingBgColor, subTitleColor;
  @override
  Widget build(BuildContext context) {
    return buildRecipent(context);
  }

  Widget buildRecipent(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding:  EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color:
                borderColor ?? Theme.of(context).primaryColor.withOpacity(0.3),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
            
              ),
              child: CircleAvatar(
                backgroundColor:
                    leadingBgColor ?? Theme.of(context).primaryColor,
                radius: 20,
                backgroundImage: recipentAvatar.isNotEmpty
                    ? CachedNetworkImageProvider(recipentAvatar)
                    : null,
                child: recipentAvatar.isEmpty
                    ? Text(
                        GetInitialNames.getInitials(recipientName),
                        style: GoogleFonts.outfit(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 20.sp
                        ),
                      )
                    : null,
              ),
            ),
             SizedBox(width: 8.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    recipientName,
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                   SizedBox(height: 2.h),
                  Text(
                    recipientEmail.isNotEmpty ? recipientEmail : recipientPhone,

                    
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: subTitleColor ?? Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
