import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomMenuScreenCards extends StatelessWidget {
  const CustomMenuScreenCards({
    required this.onTap,
    required this.title,
    required this.description,
    this.suffixIcon,
    required this.containerIcon,
    super.key,
    this.iconColor,
    this.trailingWidget,
    this.iconHeight =40,this.iconwidth=40
  });
  final VoidCallback onTap;
  final String title;
  final String description;
  final String containerIcon;
  final String? suffixIcon;
  final Color? iconColor;
  final double iconwidth, iconHeight;
  final Widget? trailingWidget;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 16.h),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(14),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x0F000000),
                    blurRadius: 24,
                  ),
                ],
              ),
              child: Row(
                children: [
                  // icons
                  Container(
                    padding: EdgeInsets.all(8.h),
                    width: 56.w,
                    height: 56.h,
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: Theme.of(context).secondaryHeaderColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(32),
                      child: Image.asset(containerIcon,
                          width: iconwidth.w, height: iconHeight.h, color: iconColor,
                          fit: BoxFit.contain,
                          // color: Theme.of(context).primaryColor,
                          ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  //column
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomBuildText(
                          text: title,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                        SizedBox(height: 4.h),
                        CustomBuildText(
                          text: description,
                          caseType: '',
                          style: GoogleFonts.outfit(
                              color: Colors.black.withOpacity(0.4),
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w300,
                              letterSpacing: 0,
                              height: 1.2),
                        ),
                      ],
                    ),
                  ),

                  // 1. title
                  // description

                  // trailing icon
                  trailingWidget ??
                      Image.asset(suffixIcon ?? MediaRes.forwardIcon,
                          width: 20, color: Color(0xFFAAAAAA))
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
