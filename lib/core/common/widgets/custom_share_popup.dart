import 'package:cbrs/core/common/widgets/custom_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/enum/shared_item_type.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/contact_service.dart';
import 'package:cbrs/core/utils/name_initial.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class CustomSharePopup extends StatelessWidget {
  CustomSharePopup({
    required this.sharedItemType,
    required this.sharedItemDetail,
    super.key,
  });

  final SharedItemType sharedItemType;
  final dynamic sharedItemDetail;

  static void show({
    required BuildContext context,
    required SharedItemType sharedItemType,
    required dynamic sharedItemDetail,
  }) =>
      showCustomBottomSheet(
        context,
        CustomSharePopup(
          sharedItemDetail: sharedItemDetail,
          sharedItemType: sharedItemType,
        ),
        backgroundColor: const Color(0xFF999999).withOpacity(0.97),
      );

  final contactService = Get.find<ContactSyncService>();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 10.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: Colors.white,
              ),
              width: 64.w,
              height: 64.h,
              child: Image.asset(MediaRes.connectLogo),
            ),
            SizedBox(width: 8.w),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBuildText(
                    text: 'Connect',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  CustomBuildText(
                    text: 'EagleLion system technology.',
                    color: Color(0x993C3C43),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => context.pop(),
              icon: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  color: Colors.white,
                ),
                child: const Icon(
                  Icons.close,
                  size: 18,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 10.h),
        const Divider(
          color: Color(0x5C3C3C43),
        ),
        SizedBox(height: 10.h),
        SizedBox(
          height: 85,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: contactService.members.length,
            itemBuilder: (context, index) {
              final user = contactService.members[index];
              return GestureDetector(
                onTap: () {
               
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.w),
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor: Theme.of(context).primaryColor,
                            backgroundImage: user.avatar.isNotEmpty
                                ? NetworkImage(user.avatar)
                                : null,
                            child: user.avatar.isEmpty
                                ? Text(
                                    getNameInitial(
                                        user.firstName, user.lastName),
                                  )
                                : null,
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                vertical: 4.h,
                                horizontal: 4.w,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.r),
                                color: Theme.of(context).primaryColor,
                              ),
                              width: 22.w,
                              height: 22.h,
                              child: Image.asset(
                                MediaRes.connectLogo,
                                color: Colors.white,
                              ),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${user.firstName} ${user.lastName}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        SizedBox(height: 10.h),
        const Divider(
          color: Color(0x5C3C3C43),
        ),
        SizedBox(height: 10.h),
      ],
    );
  }
}
