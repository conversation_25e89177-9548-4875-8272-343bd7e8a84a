import 'dart:ui';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/global_variable.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';

import 'package:country_flags/country_flags.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shimmer/shimmer.dart';

class CustomDropBalance extends StatefulWidget {
  const CustomDropBalance({
    required this.selectedWallet,
    required this.onTap,
    this.boxShadow,
    super.key,
    this.bgColor,
    this.textColor,
    this.hasCustomOnTap = false,
    this.fetchTransferLimits,
    this.onWalletSwitched,
  });

  final String selectedWallet;
  final VoidCallback onTap;
  final Function(double, String)? fetchTransferLimits;
  final List<BoxShadow>? boxShadow;
  final Color? bgColor;
  final Color? textColor;
  final bool hasCustomOnTap;
  final void Function(String)? onWalletSwitched;

  @override
  State<CustomDropBalance> createState() => _CustomDropBalanceState();
}

class _CustomDropBalanceState extends State<CustomDropBalance> {
  late String _selectedWallet;

  bool isWalletSelected = false;
  double walletBalance = 0;

  @override
  void initState() {
    _selectedWallet = widget.selectedWallet;
  }

  void handleOnTap(String wallet, double newWalletBalance) {
    setState(() {
      _selectedWallet = wallet;
      isWalletSelected = false;
      walletBalance = newWalletBalance;
    });
    if (widget.hasCustomOnTap) {
      widget.fetchTransferLimits!(walletBalance, wallet);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // handleOnTap();
        widget.hasCustomOnTap
            ? _navigateToQuickTransfer(context)
            : widget.onTap();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        decoration: BoxDecoration(
          color: widget.bgColor ?? Colors.white.withOpacity(0.24),
          borderRadius: BorderRadius.circular(60.r),
          boxShadow: widget.boxShadow ?? const [],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: CountryFlag.fromCountryCode(
                widget.hasCustomOnTap
                    ? _selectedWallet == 'ETB'
                        ? 'ET'
                        : 'US'
                    : widget.selectedWallet == 'ETB'
                        ? 'ET'
                        : 'US',
                height: 19.h,
                width: 19.w,
              ),
            ),
            SizedBox(width: 8.w),
            // Text(_selectedWallet),
            // SizedBox(
            //   width: 4,
            // ),
            Text(
              '${widget.hasCustomOnTap ? _selectedWallet : widget.selectedWallet} Wallet',
              style: GoogleFonts.outfit(
                color: widget.textColor ?? Colors.white,
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(width: 4.w),
            Icon(
              Icons.keyboard_arrow_down,
              color: widget.textColor ?? Colors.white,
              size: 20.r,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> showWalletSelection(BuildContext context) async {
    final blocValue = context.read<WalletBalanceBloc>();

    final modal = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      // barrierColor: Colors.grey.withOpacity(0.2),
      elevation: 0,
      useSafeArea: true,

      builder: (context) => BlocProvider.value(
        // Provide the existing TransactionBloc to the bottom sheet
        value: blocValue,
        child: BlocConsumer<WalletBalanceBloc, HomeState>(
          listener: (context, state) {
            debugPrint('listned to state $state');
          },
          builder: (context, state) {
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                debugPrint('hello');
              },
              child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: 2,
                  sigmaY: 2,
                ),
                child: Container(
                  padding: const EdgeInsets.only(
                    top: 16,
                    bottom: 16,
                    left: 16,
                    right: 16,
                  ),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: state is WalletLoadedState
                      ? Container()
                      : _buildShimmerLoading(),

                  // state is UserLoaded
                  //     ? _buildContent(
                  //         wallets: state.user.wallets,
                  //       )
                  //     : _buildShimmerLoading(),
                ),
              ),
            );
          },
        ),
      ),
    ).then((value) {
      setState(() {
        isWalletSelected = false;
      });
    });

    debugPrint('Cheeekcl');
  }

  Future<void> _navigateToQuickTransfer(
    BuildContext context,
  ) async {
    final bloc = context.read<WalletBalanceBloc>();

    var isUsdSelected = widget.selectedWallet == 'USD';
    final tempIsUsdSelected = isUsdSelected;

    // context.read<WalletBalanceBloc>().state is WalletLoadedState &&
    //     (context.read<WalletBalanceBloc>().state as WalletLoadedState)
    //         .isUsdWallet;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      useSafeArea: true,
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.opaque,
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 2,
            sigmaY: 2,
          ),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
            ),
            child: BlocProvider.value(
              value: bloc,
              child: StatefulBuilder(
                builder: (context, useSetState) {
                  debugPrint('yess ser');
                  return BlocBuilder<WalletBalanceBloc, HomeState>(
                    builder: (context, state) {
                      if (state is! WalletLoadedState) {
                        // Return a loading or empty widget when the state is not loaded
                        return const SizedBox.shrink();
                      }

                      // Extract balances
                      final etbBalance = state.etbBalance;
                      final usdBalance = state.usdBalance;

                      return ColoredBox(
                        color: Colors.white,
                        child: SafeArea(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Center(
                                child: Container(
                                  width: 60.w,
                                  height: 3.h,
                                  decoration: BoxDecoration(
                                    color:
                                        const Color.fromARGB(255, 19, 12, 12),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ),
                              SizedBox(height: 16.h),
                              Text(
                                'Select Wallet',
                                style: GoogleFonts.outfit(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                'Select your preferred wallet to initiate a seamless and fast money transfer.',
                                style: GoogleFonts.outfit(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFFAAAAAA),
                                ),
                              ),
                              _buildWalletRow(
                                isSelected: isUsdSelected,
                                walletBalance: usdBalance,
                                isUSDWallet: true,
                                icon: !tempIsUsdSelected
                                    ? MediaRes.birrWalletIcon
                                    : MediaRes.walletIcon,
                                onTap: () {
                                  useSetState(() {
                                    isUsdSelected = true;
                                  });
                                },
                              ),
                              _buildWalletRow(
                                isSelected: !isUsdSelected,
                                walletBalance: etbBalance,
                                isUSDWallet: false,
                                icon: !tempIsUsdSelected
                                    ? MediaRes.birrWalletIcon
                                    : MediaRes.walletIcon,
                                onTap: () {
                                  useSetState(() {
                                    isUsdSelected = false;
                                  });
                                },
                              ),
                              const SizedBox(height: 16),
                              CustomRoundedBtn(
                                btnText: 'Continue',
                                isLoading: false,
                                onTap: () {
                                  handleOnTap(
                                    !isUsdSelected ? 'ETB' : 'USD',
                                    !isUsdSelected ? etbBalance : usdBalance,
                                  );
                                  Navigator.pop(context);
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWalletRow({
    required bool isUSDWallet,
    required bool isSelected,
    required double walletBalance,
    required VoidCallback onTap,
    required String icon,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 16.h,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isSelected
            ? Theme.of(context).primaryColor.withOpacity(0.08)
            : const Color(0xFFF8F8F8),
        border: isSelected
            ? Border.all(
                color: const Color(0xFF224D2D).withOpacity(0.08),
              )
            : null,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.check_circle : Icons.circle_outlined,
              color: Theme.of(context).primaryColor,
              size: 24,
            ),
            SizedBox(width: 12.w),
            Image.asset(
              icon,
              width: 40,
              height: 40,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBuildText(
                    text: '${isUSDWallet ? 'USD' : 'ETB'} Wallet',
                    fontSize: 16.sp,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w800,
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    children: [
                      CustomBuildText(
                        text: '$walletBalance',
                        fontSize: 12.sp,
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(width: 4.w),
                      CustomBuildText(
                        text: isUSDWallet ? 'USD' : 'ETB',
                        fontSize: 12.sp,
                        color: Theme.of(context).primaryColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

/*
  Widget _buildContent({required List<Wallet> wallets}) {
    return StatefulBuilder(
      builder: (context, setState) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 60.w,
                  height: 2.h,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'Select Wallet',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                'Select your preferred wallet to initiate a seamless and fast money transfer.',
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xFFAAAAAA),
                ),
              ),
              SizedBox(height: 14.h),
              ...List.generate(
                wallets.length ?? 0,
                (index) => InkWell(
                  onTap: () {
                    debugPrint('Selecteddd');
                    setState(() {
                      for (final e in wallets) {
                        wallets[wallets.indexOf(e)] =
                            e.copyWith(isSelected: false);
                      }
                      isWalletSelected = true;

                      wallets[index] = wallets[index].copyWith(
                        isSelected: true,
                      );

                      widget.onWalletSwitched?.call(wallets[index].currency);
                    });
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 6.h),
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.5.w,
                      vertical: 12.h,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      // color: wallets[index].isSelected
                      //     ? Theme.of(context).secondaryHeaderColor
                      //     : const Color(0xFFF8F8F8),

                      color: !isWalletSelected
                          ? _selectedWallet == wallets[index].currency
                              ? Theme.of(context).secondaryHeaderColor
                              : const Color(0xFFF8F8F8)
                          : wallets[index].isSelected
                              ? Theme.of(context).secondaryHeaderColor
                              : const Color(0xFFF8F8F8),

                      border: !isWalletSelected
                          ? _selectedWallet == wallets[index].currency
                              ? Border.all(
                                  color: Theme.of(context).primaryColor,
                                  width: 0.2,
                                )
                              : null
                          : wallets[index].isSelected
                              ? Border.all(
                                  color: Theme.of(context).primaryColor,
                                  width: 0.2,
                                )
                              : null,
                    ),
                    child: Row(
                      children: [
                        Icon(
                          !isWalletSelected
                              ? _selectedWallet == wallets[index].currency
                                  ? Icons.check_circle
                                  : Icons.circle_outlined
                              : wallets[index].isSelected
                                  ? Icons.check_circle
                                  : Icons.circle_outlined,
                          // wallets[index].isSelected
                          //     ? Icons.check_circle
                          //     : wallets[index].isSelected &&
                          //             _selectedWallet == wallets[index].currency
                          //         ? Icons.check_circle
                          //         : Icons.circle_outlined,

                          color: Theme.of(context).primaryColor,

                          size: 24,
                        ),
                        SizedBox(width: 12.w),
                        Image.asset(
                          _selectedWallet == 'ETB'
                              ? MediaRes.walletIcon
                              : MediaRes.dollarWalletIcon,
                          width: 40,
                          height: 40,
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomBuildText(
                                text: '${wallets[index].currency} Wallet',
                                fontSize: 16.sp,
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w800,
                              ),
                              SizedBox(height: 4.h),
                              Row(
                                children: [
                                  CustomBuildText(
                                    text: wallets[index]
                                        .balance
                                        .toStringAsFixed(2),
                                    fontSize: 12.sp,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                  SizedBox(width: 4.w),
                                  CustomBuildText(
                                    text: wallets[index].currency,
                                    fontSize: 12.sp,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(height: 10.h),
              CustomRoundedBtn(
                btnText: 'Continue',
                isLoading: false,

                // isBtnActive: wallets.any((e) => e.isSelected),
                // onTap: () {
                //   if (!wallets.any((e) {
                //     if (e.isSelected) {
                //       handleOnTap(e.currency, e.balance.toString());
                //       // setState(() {});
                //       // _selectedWallet = e.currency;
                //       GlobalVariable.currentlySelectedWallet = e.currency;
                //       Navigator.pop(context);
                //     }

                //     return e.isSelected;
                //   })) return;
                // },

              ),
              const SizedBox(
                height: 4,
              ),
            ],
          ),
        );
      },
    );
  }
*/
  Widget _buildShimmerLoading() {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: 2,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Shimmer.fromColors(
            baseColor: const Color(0xFFE8ECF4),
            highlightColor: const Color(0xFFF5F7FA),
            child: Row(
              children: [
                // Content area with varying widths for realistic shimmer
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        height: 17.h,
                        width: 150,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        height: 14.h,
                        width: 100.w - (index % 3) * 10.w,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(3.r),
                        ),
                      ),
                    ],
                  ),
                ),

                Icon(
                  Icons.chevron_right_rounded,
                  size: 24.r,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
