import 'package:cbrs/core/common/widgets/custom_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_bottom_sheet_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:local_auth/local_auth.dart';

typedef PinSubmittedFunction = void Function(String pin);

class CustomPinBottomSheet extends StatefulWidget {
  const CustomPinBottomSheet({
    required this.context,
    required this.onPinSubmitted,
    required this.onBiometricAuth,
    required this.isLoading,
    required this.hasBiometricSupport,
    super.key,
    this.preferredBiometric,
  });

  final PinSubmittedFunction onPinSubmitted;
  final Function? onBiometricAuth;
  final bool isLoading;
  final bool hasBiometricSupport;
  final BiometricType? preferredBiometric;
  final BuildContext context;

  @override
  State<CustomPinBottomSheet> createState() => _CustomPinBottomSheetState();

  void show() => showCustomBottomSheet(
        context,
        CustomPinBottomSheet(
          context: context,
          onPinSubmitted: onPinSubmitted,
          onBiometricAuth: onBiometricAuth,
          isLoading: isLoading,
          hasBiometricSupport: hasBiometricSupport,
          preferredBiometric: preferredBiometric,
        ),
      );
}

class _CustomPinBottomSheetState extends State<CustomPinBottomSheet>
    with SingleTickerProviderStateMixin {
  final _pinController = TextEditingController();
  String _enteredPin = '';
  bool _obscurePin = true;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _animationController.forward();
  }

  void _onKeyPressed(String value) {
    if (_enteredPin.length >= 6) return;

    setState(() {
      _enteredPin += value;
      _pinController.text = _enteredPin;
    });

    if (_enteredPin.length == 6) {
      widget.onPinSubmitted(_enteredPin);
    }
  }

  void _onBackspace() {
    if (_enteredPin.isEmpty) return;

    setState(() {
      _enteredPin = _enteredPin.substring(0, _enteredPin.length - 1);
      _pinController.text = _enteredPin;
    });
  }

  Widget _buildKeypadButton({
    required String label,
    required VoidCallback? onPressed,
    bool isGo = false,
  }) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 12.w),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            width: 124.w,
            height: 64.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: isGo ? const Color(0xFF065234) : Colors.grey[50],
            ),
            child: Center(
              child: widget.isLoading && isGo
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      label,
                      style: GoogleFonts.outfit(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w600,
                        color: isGo ? Colors.white : Colors.black,
                      ),
                    ),
            ),
          ),
        ),
      ).animate().scale(duration: 200.ms),
    );
  }

  Widget _buildNumericKeypad() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          [1, 2, 3],
          [4, 5, 6],
          [7, 8, 9],
          ['⌫', 0, '→'],
        ].map((row) {
          return Padding(
            padding: EdgeInsets.only(bottom: 13.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: row.map((item) {
                if (item == '⌫') {
                  return _buildKeypadButton(
                    label: item.toString(),
                    onPressed: _onBackspace,
                  );
                } else if (item == '→') {
                  return _buildKeypadButton(
                    label: _enteredPin.length == 6 ? '' : item.toString(),
                    onPressed: _enteredPin.length == 6
                        ? null
                        : () => _onKeyPressed(item.toString()),
                    isGo: true,
                  );
                } else {
                  return _buildKeypadButton(
                    label: item.toString(),
                    onPressed: () => _onKeyPressed(item.toString()),
                  );
                }
              }).toList(),
            ),
          );
        }).toList(),
      ),
    ).animate().fadeIn(duration: 800.ms, delay: 400.ms);
  }

  Widget _buildPinDisplay() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(6, (index) {
        bool isFilled = index < _enteredPin.length;
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 6.w),
          width: 48.w,
          height: 48.w,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              isFilled ? (_obscurePin ? '*' : _enteredPin[index]) : '',
              style: GoogleFonts.outfit(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 32.h,
        bottom: MediaQuery.of(context).viewInsets.bottom + 32.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(32.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [

          CustomBottomSheetHeader(title: "PIN Confirmation"),
          Text(
            'Enter PIN to Verify Transfer',
            style: GoogleFonts.outfit(
              fontSize: 24.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Please enter your PIN to verify this transfer.',
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 32.h),
          _buildPinDisplay(),
          TextButton(
            onPressed: () => setState(() => _obscurePin = !_obscurePin),
            child: Text(
              _obscurePin ? 'Show PIN' : 'Hide PIN',
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: const Color(0xFF065234),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(height: 24.h),
          _buildNumericKeypad(),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    _animationController.dispose();
    super.dispose();
  }
}
