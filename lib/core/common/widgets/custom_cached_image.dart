import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class CustomCachedImage extends StatelessWidget {
  const CustomCachedImage({
    required this.url,
    super.key,
    this.borderRadius,
    this.boxFit,
    this.isProfile = false,
    this.width,
    this.height,
    this.name,
  });
  final String url;
  final double? borderRadius;
  final BoxFit? boxFit;
  final bool isProfile;
  final double? width;
  final double? height;
  final String? name;

  @override
  Widget build(BuildContext context) {
    if (isProfile) {
      return _buildProfileImage();
    }
    return _buildRegularImage();
  }

  Widget _buildProfileImage() {
    return CachedNetworkImage(
      imageUrl: url,
      width: width,
      height: height,
      fit: boxFit ?? BoxFit.cover,
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          image: DecorationImage(
            image: imageProvider,
            fit: boxFit ?? BoxFit.cover,
          ),
        ),
      ),
      placeholder: (context, url) => _buildProfilePlaceholder(),
      errorWidget: (context, url, error) => _buildProfileError(),
    );
  }

  Widget _buildRegularImage() {
    return 
    CachedNetworkImage(
      imageUrl: url,

      /// TODO - working on this
      // memCacheWidth: 50, // Reduce memory usage
      // memCacheHeight: 50, // Reduce memory usage
      // maxWidthDiskCache: 50, // Optional: Reduce disk cache size
      // maxHeightDiskCache: 50, // Optional: Reduce disk cache size

      placeholder: (context, url) => Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey,
            borderRadius: BorderRadius.circular(borderRadius ?? 12.0),
          ),
        ),
      ),
      imageBuilder: (context, imageProvider) => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius ?? 12.0),
          image: DecorationImage(
            image: imageProvider,
            fit: boxFit ?? BoxFit.cover,
          ),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius ?? 12.0),
          image: DecorationImage(
            image: const AssetImage(MediaRes.imagePlaceHolder),
            fit: boxFit ?? BoxFit.cover,
          ),
        ),
      ),
    );
 
  }

  Widget _buildProfilePlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: width,
        height: height,
        decoration: const BoxDecoration(
          color: Colors.grey,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildProfileError() {
    if (name != null && name!.isNotEmpty) {
      return Container(
        width: width,
        height: height,
        decoration: const BoxDecoration(
          color: Color(0xFF065234),
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            name!.substring(0, 1).toUpperCase(),
            style: TextStyle(
              fontSize: (width ?? 48.0) * 0.4,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    }
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.person,
        color: Colors.grey[400],
        size: (width ?? 48.0) * 0.5,
      ),
    );
  }
}
