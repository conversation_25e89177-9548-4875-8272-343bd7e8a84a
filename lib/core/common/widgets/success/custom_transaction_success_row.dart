import 'package:flutter/material.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTransactionSuccessRow extends StatelessWidget {
  const CustomTransactionSuccessRow({
    super.key,
    this.label,
    this.value,
    this.isLast = false,
    this.hasCurrency = false,
    this.isBirrTransfer = false,
    this.caseType = 'eachWord',
    this.labelCaseType = 'default',

  });
  final String? label;
  final String? value;
  final bool isLast;
  final bool hasCurrency;
  final bool isBirrTransfer;

  final String caseType;
  final String labelCaseType;

  @override
  Widget build(BuildContext context) {
    return value == null || value!.isEmpty
        ? Container()
        : _buildTransactionDetail();
  }

  Widget _buildTransactionDetail() {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomBuildText(
            text: "${label!}:",
            fontSize: isLast ? 16.sp : 12.sp,
            color: isLast ? Colors.black : const Color(0xFF6E6E6E),
            fontWeight: isLast ? FontWeight.w700 : FontWeight.w400,
            caseType: labelCaseType,
          ),
          SizedBox(width: 12.w),
          Flexible(
            child: CustomBuildText(
              text: hasCurrency
                  ? isBirrTransfer
                      ? '${value!} ETB'
                      : '\$${value!}'
                  : value!,
              caseType: isLast ? 'all' : caseType,
              fontSize: isLast ? 18.sp : 14.sp,
              fontWeight: isLast ? FontWeight.w700 : FontWeight.w400,
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
