import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/download_reciept_url.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/data/models/wallet_transfer_response.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cbrs/core/common/widgets/dotted_line_painter.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:ui' as ui;

class CustomTransactionSuccessScreen extends StatelessWidget {
  CustomTransactionSuccessScreen({
    required this.pageDescription,
    required this.totalAmount,
    required this.child,
    this.isGuestFlow = false,
    this.isBirrTransfer = true,
    this.alwaysShowETBColor = false,
    this.hasPageTitle = false,
    this.showGetReceiptButton = true,
    super.key,
    this.onQrTap,
    this.onShareTap,
    this.onGetRecieptTap,
    this.billRefNo = '',
    this.useCustomBillGenerator = false,
    this.loanReceipt = LoanReceipt.repayment,
  });

  final String pageDescription;
  final String billRefNo;

  final double totalAmount;
  final bool isBirrTransfer;
  final bool alwaysShowETBColor;
  final bool isGuestFlow;
  final bool hasPageTitle;
  final bool showGetReceiptButton;
  final bool useCustomBillGenerator;
  final LoanReceipt loanReceipt;
  final Widget child;
  final void Function()? onQrTap;
  final void Function()? onShareTap;
  final void Function()? onGetRecieptTap;

  void _downloadReceipt(BuildContext context) {
    if (useCustomBillGenerator) {
      context.read<TransactionBloc>().add(
            GetLoanInvoiceEvent(
              billRefNo: billRefNo,
              loanReceipt: loanReceipt,
            ),
          );
    } else {
      context.read<WalletTransferBloc>().add(
            GenerateRecieptEvent(
              billRefNo: billRefNo,
            ),
          );
    }
  }

  final GlobalKey _globalKey = GlobalKey();

  Future<void> _captureAndShare() async {
    try {
      final boundary = _globalKey.currentContext!.findRenderObject()
          as RenderRepaintBoundary;

      // Capture the image
      final image = await boundary.toImage(pixelRatio: 3);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();

      // Save the image to temporary storage
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/screenshot.png').create();
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Check out my transaction details!',
      );

      await file.delete();
    } catch (e) {
      print('Error capturing and sharing: $e');
   
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: const Color(0xFFD3F3CD),
        body: useCustomBillGenerator
            ? BlocConsumer<TransactionBloc, TransactionState>(
                listener: (context, state) {
                  if (state is TransactionError) {
                    CustomToastification(context, message: state.message);
                  }
                  if (state is LoanInvoiceLoaded) {
                    HandleDownloadReciept.downloadReceipt(
                      context,
                      state.invoiceUrl,
                    );
                  }
                },
                builder: (context, state) {
                  return _buildContent(context, state is TransactionLoading);
                },
              )
            : BlocConsumer<WalletTransferBloc, WalletTransferState>(
                listener: (context, state) {
                  if (state is WalletTransferFailure) {
                    CustomToastification(context, message: state.message);
                  }
                  if (state is GeneratedReceiptState) {
                    HandleDownloadReciept.downloadReceipt(
                      context,
                      state.receiptUrl,
                    );
                  }
                },
                builder: (context, state) {
                  return _buildContent(context, state is WalletTransferLoading);
                },
              ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, bool isLoading) {
    return SafeArea(
      bottom: false,
      child: ColoredBox(
        color: Colors.white,
        child: Column(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return ConstrainedBox(
                    constraints: const BoxConstraints(),
                    child: SingleChildScrollView(
                      physics: const ClampingScrollPhysics(),
                      child: IntrinsicHeight(
                        child: SafeArea(
                          child: Column(
                            children: [
                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  ColoredBox(
                                    color: const Color(0xFFD3F3CD),
                                    child: Column(
                                      children: [
                                        // Top header section
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 24.w,
                                            vertical: 12.h,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: 4.w,
                                                  vertical: 4.h,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                    32.r,
                                                  ),
                                                ),
                                                child: Image.asset(
                                                  'assets/images/connect_logo.png',
                                                  height: 32.h,
                                                ),
                                              ),
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: 16.w,
                                                  vertical: 8.h,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                    32.r,
                                                  ),
                                                ),
                                                child: Row(
                                                  children: [
                                                    GestureDetector(
                                                      onTap: onQrTap,
                                                      child: Image.asset(
                                                        MediaRes.successQrIcon,
                                                        width: 24.w,
                                                        height: 24.h,
                                                        color:
                                                            alwaysShowETBColor
                                                                ? LightModeTheme()
                                                                    .primaryColorBirr
                                                                : Theme.of(
                                                                    context,
                                                                  ).primaryColor,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 16.w,
                                                    ),
                                                    InkWell(
                                                      onTap: _captureAndShare,
                                                      // onShareTap,
                                                      child: Image.asset(
                                                        MediaRes
                                                            .successShareIcon,
                                                        width: 24.w,
                                                        height: 24.h,
                                                        color:
                                                            alwaysShowETBColor
                                                                ? LightModeTheme()
                                                                    .primaryColorBirr
                                                                : Theme.of(
                                                                    context,
                                                                  ).primaryColor,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),

                                        SizedBox(height: 76.h),

                                        // Success content section
                                        RepaintBoundary(
                                          key: _globalKey,
                                          child: Container(
                                            width: double.infinity,
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 24.w,
                                              vertical: 8.h,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(
                                                  32.r,
                                                ),
                                                topRight: Radius.circular(
                                                  32.r,
                                                ),
                                              ),
                                            ),
                                            child: Column(
                                              children: [
                                                SizedBox(height: 64.h),
                                                Text(
                                                  isGuestFlow
                                                      ? '${totalAmount.toStringAsFixed(2)} USD'
                                                      : isBirrTransfer
                                                          ? '${totalAmount.toStringAsFixed(2)} ETB'
                                                          : '\$${totalAmount.toStringAsFixed(2)}',
                                                  style: GoogleFonts.outfit(
                                                    fontSize: 32.sp,
                                                    fontWeight: FontWeight.w700,
                                                    color:
                                                        // Color(0xFF3AB73A) :

                                                        alwaysShowETBColor
                                                            ? LightModeTheme()
                                                                .primaryColorBirr
                                                            : Theme.of(
                                                                context,
                                                              ).primaryColor,
                                                  ),
                                                ),

                                                SizedBox(height: 8.h),
                                                Text(
                                                  pageDescription,
                                                  // 'Your transfer has been completed successfully.',
                                                  textAlign: TextAlign.center,
                                                  style: GoogleFonts.outfit(
                                                    fontSize: 14.sp,
                                                    color: const Color(
                                                      0xFF6D6D6D,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(height: 24.h),
                                                Row(
                                                  children: [
                                                    Text(
                                                      'Transaction Details ',
                                                      style: GoogleFonts.outfit(
                                                        fontSize: 16.sp,
                                                        fontWeight:
                                                            FontWeight.w700,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 18.h),

                                                child, // transaction details conttent - colun passed to here
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Centered overlapping success image
                                  Positioned(
                                    top: 64.h,
                                    left: 0,
                                    right: 0,
                                    child: Center(
                                      child: Container(
                                        decoration: const BoxDecoration(
                                          shape: BoxShape.circle,
                                        ),
                                        child: Image.asset(
                                          'assets/images/success_image.png',
                                          height: 144.h,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            // Fixed bottom buttons section
            Container(
              width: double.infinity,
              padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 24.h),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.06),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: SafeArea(
                bottom: false,
                top: false,
                child: Row(
                  children: [
                    if (showGetReceiptButton) ...[
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(32.r),
                            border: Border.all(
                              color: alwaysShowETBColor
                                  ? LightModeTheme().primaryColorBirr
                                  : Theme.of(context).primaryColor,
                            ),
                          ),
                          child: TextButton(
                            onPressed:
                                //  onGetRecieptTap ??
                                () => _downloadReceipt(context),
                            // Get Receipt functionality

                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(
                                vertical: 16.h,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(32.r),
                              ),
                            ),
                            child: isLoading
                                ? SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      color: Theme.of(context).primaryColor,
                                      strokeWidth: 3,
                                    ),
                                  )
                                : Text(
                                    'Get Receipt',
                                    style: GoogleFonts.outfit(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color: alwaysShowETBColor
                                          ? LightModeTheme().primaryColorBirr
                                          : Theme.of(context).primaryColor,
                                    ),
                                  ),
                          ),
                        ),
                      ),
                      SizedBox(width: 16.w),
                    ],
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: alwaysShowETBColor
                              ? LightModeTheme().primaryColorBirr
                              : Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(32.r),
                        ),
                        child: TextButton(
                          onPressed: () => context.go(AppRouteName.home),
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 16.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(32.r),
                            ),
                          ),
                          child: Text(
                            'Back To Home',
                            style: GoogleFonts.outfit(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // SizedBox(height: 10,)
          ],
        ),
      ),
    );
  }
}
