import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:cbrs/core/common/models/transaction_success_model.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_page_padding.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/enum/loan_receipt.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/download_reciept_url.dart';
import 'package:cbrs/features/chat/domain/entities/user_attributes.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transfer_to_wallet/application/bloc/wallet_transfer_bloc.dart';
import 'package:confetti/confetti.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import 'dart:math';

import 'dart:convert';

import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

class CustomSuccessTransactionBottomSheet extends StatefulWidget {
  /// Creates a customizable transaction success bottom sheet
  ///
  /// [data] contains the transaction details
  /// [transactionType] defines the type of transaction
  /// [onContinue] is called when the user taps "Back To Home"
  const CustomSuccessTransactionBottomSheet({
    required this.onContinue,
    required this.data,
    required this.originalCurrency,
    required this.totalAmount,
    required this.billAmount,
    required this.transactionId,
    required this.billRefNo,
    this.qrId,
    this.status = 'Paid',
    this.isFromChat = false,
    this.transactionType = 'bank_transfer',
    super.key,
    this.title = '',
    this.showActionButtons = true,
    this.buttonText,
    this.isFromLoan = false,
    this.isGuestMode = false,
    this.loanReceipt = LoanReceipt.applicationFee,
  });

  final VoidCallback onContinue;

  final Map<String, dynamic> data;
  final String transactionType;
  final String title;
  final bool showActionButtons;
  final String? buttonText;

  final bool isFromChat;
  final bool isGuestMode;
  final String status;
  final String originalCurrency;
  final double totalAmount; // TODO,
  final double billAmount; // TODO,
  final String transactionId;
  final String billRefNo;
  final LoanReceipt loanReceipt;

  final String? qrId;

  final bool isFromLoan;

  @override
  State<CustomSuccessTransactionBottomSheet> createState() =>
      _CustomSuccessTransactionBottomSheetState();
}

class _CustomSuccessTransactionBottomSheetState
    extends State<CustomSuccessTransactionBottomSheet>
    with TickerProviderStateMixin {
  late final AnimationController _controller;

  double _heightFraction = 0.85;
  late ConfettiController _confettiController;
  bool _isDownloading = false;
  final GlobalKey _qrKey = GlobalKey();

  String generatedQRData = '';
  final randomCharLength = 8;
  String invoiceUrl = '';
  bool _isLoadingGenerateReciept = false;
  bool _isLoadingShare = false;
  final GlobalKey _screenshotKey = GlobalKey();

  final GlobalKey _globalKey = GlobalKey();
  File? _cachedImageFile;

  String generateRandomString() {
    final random = Random();
    const allChars =
        'AaBbCcDdlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1EeFfGgHhIiJjKkL234567890';

    final randomString = List.generate(
      randomCharLength,
      (index) => allChars[random.nextInt(allChars.length)],
    ).join();
    return randomString;
  }

  Future<void> qrCodeGenerator() async {
    final base64Encoded = base64Encode(
      utf8.encode(
        '${generateRandomString()}-${widget.transactionId}-${generateRandomString()}'
            .toLowerCase(),
      ),
    );

    setState(() {
      generatedQRData = base64Encoded;
    });
  }

  Future<void> _generateReciept() async {
    setState(() {
      _isLoadingGenerateReciept = true;
    });

    try {
      if (invoiceUrl.isEmpty) {
        debugPrint('😂shjdhjdsjhsd sd ${widget.isFromLoan}');
        if (widget.isFromLoan) {
          context.read<TransactionBloc>().add(
                GetLoanInvoiceEvent(
                  billRefNo: widget.billRefNo,
                  loanReceipt: widget.loanReceipt,
                ),
              );
          // loan recipte bloc
        } else
          context.read<WalletTransferBloc>().add(
                GenerateRecieptEvent(
                  billRefNo: widget.billRefNo,
                ),
              );
      } else {
        await HandleDownloadReciept.downloadReceipt(context, invoiceUrl);
      }
    } finally {
      // setState(() {
      //   _isLoadingGenerateReciept = false;
      // });
    }
  }

  bool permissionGranted = false;

  Future<void> _getStoragePermission() async {
    if (Platform.isAndroid) {
      final plugin = DeviceInfoPlugin();
      final android = await plugin.androidInfo;
      if (android.version.sdkInt < 33) {
        if (await Permission.storage.request().isGranted) {
          setState(() {
            permissionGranted = true;
          });
        } else if (await Permission.storage.request().isPermanentlyDenied) {
          await openAppSettings();
        }
      }
    }
  }

  // Full page screenshot functionality
  Future<void> _takeFullPageScreenshot() async {
    try {
      // Request appropriate permissions based on platform and Android version
      // PermissionStatus? status;

      // if (Platform.isAndroid) {
      //   if (await Permission.photos.request().isGranted) {
      //     status = await Permission.photos.status;
      //   } else {
      //     status = await Permission.storage.request();
      //   }
      // } else {
      //   status = await Permission.photos.request();
      // }

      await _getStoragePermission().then((values) {
        if (!permissionGranted) {
          if (mounted) {
            // CustomToastification(
            //   context,
            //   message: 'Storage permission is required to save screenshot',
            // );
          }
          return;
        }
      });

      // Capture the full page as image
      final boundary = _screenshotKey.currentContext?.findRenderObject()
          as RenderRepaintBoundary?;
      if (boundary == null) {
        if (mounted) {
          CustomToastification(
            context,
            message: 'Failed to capture screenshot',
          );
        }
        return;
      }

      final image = await boundary.toImage(pixelRatio: 3);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        if (mounted) {
          CustomToastification(
            context,
            message: 'Failed to process screenshot',
          );
        }
        return;
      }

      // Save to gallery
      final result = await ImageGallerySaverPlus.saveImage(
        byteData.buffer.asUint8List(),
        name: 'transaction_receipt_${DateTime.now().millisecondsSinceEpoch}',
        quality: 100,
      );

      if (mounted) {
        if (result['isSuccess'] == true) {
          CustomToastification(
            context,
            message: 'Screenshot saved to gallery successfully',
            isError: false,
          );
        } else {
          CustomToastification(
            context,
            message: 'Failed to save screenshot to gallery',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        CustomToastification(
          context,
          message: 'Error taking screenshot: $e',
        );
      }
    }
  }

  Future<void> _captureAndShare() async {
    try {
      // ✅ If already cached, use it directly

      setState(() {
        _isLoadingShare = true;
        // _cachedImageFile = null;
      });
      if (_cachedImageFile != null && await _cachedImageFile!.exists()) {
        await Share.shareXFiles(
          [XFile(_cachedImageFile!.path)],
          text: 'Check out my transaction details!',
        );
        return;
      }

      final boundary = _globalKey.currentContext!.findRenderObject()
          as RenderRepaintBoundary;

      final image = await boundary.toImage(pixelRatio: 3);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      _cachedImageFile = await File('${tempDir.path}/screenshot.png').create();
      await _cachedImageFile!.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(_cachedImageFile!.path)],
        text: 'Check out my transaction details!',
      );
    } catch (e) {
      CustomToastification(
        context,
        message: 'Error in sharing. Please take screenshot',
      );
    } finally {
      setState(() {
        _isLoadingShare = false;
      });
    }
  }

  Future<void> _updateQrStatus() async {
    if (widget.qrId != null) {
      context.read<WalletTransferBloc>().add(
            UpdateQrEvent(
              qrId: widget.qrId!,
            ),
          );
    }
  }

  @override
  void initState() {
    super.initState();
    _updateQrStatus();
    qrCodeGenerator();
    _controller = AnimationController(vsync: this);

    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _heightFraction = 1.0; // Expand to full screen
      });
    });

    // Only auto-close if explicitly from chat
    // For money requests and other transactions, we want the user to manually close
    // Disable auto-close for wallet_transfer as it's handled manually
    if (widget.isFromChat &&
        widget.transactionType != 'money_request' &&
        widget.transactionType != 'mortgage_loan' &&
        widget.transactionType != 'wallet_transfer') {
      debugPrint('Setting up auto-close for ${widget.transactionType}');
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          debugPrint('Auto-closing bottom sheet and calling onContinue');
          // Navigator.pop(context);
          widget.onContinue();
        }
      });
    } else {
      debugPrint(
        'NOT auto-closing (isFromChat=${widget.isFromChat}, transactionType=${widget.transactionType})',
      );
    }
  }

  Future<void> _handleDownload() async {
    if (widget.isGuestMode) {
      CustomToastification(
        context,
        message: 'Receipt downloading',
        isError: false,
      );
      return;
    }
    await HandleDownloadReciept.downloadReceipt(context, invoiceUrl);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return BlocListener<TransactionBloc, TransactionState>(
      listener: (context, state) {
        if (state is LoanInvoiceLoaded) {
          setState(() {
            invoiceUrl = state.invoiceUrl;
            _isLoadingGenerateReciept = false;
          });
          _handleDownload();
        }
        if (state is TransactionError) {
          setState(() {
            _isLoadingGenerateReciept = false;
          });
        }
        if (state is ReceiptDownloadLoading) {
          CustomToastification(
            context,
            message: 'Downloading receipt...',
            successTitle: 'WAITING',
            isError: false,
          );
        } else if (state is ReceiptDownloadError) {
          setState(() {
            _isLoadingGenerateReciept = false;
          });
          CustomToastification(
            context,
            message: 'Error downloading receipt: ${state.message}',
          );
        }
      },
      child: PopScope(
        // Allow popping but navigate to home when popped
        onPopInvokedWithResult: (bool didPop, dynamic result) {
          if (didPop) {
            context.goNamed(AppRouteName.home);
          }
        },
        child: BlocConsumer<WalletTransferBloc, WalletTransferState>(
          listener: (context, state) {
            debugPrint('GeneratedReceiptStateff');

            if (state is GeneratedReceiptState) {
              setState(() {
                invoiceUrl = state.receiptUrl;
                _isLoadingGenerateReciept = false;
              });
              _handleDownload();

              // CustomToastification(
              //   context,
              //   message: state.receiptUrl,
              // );
            }

            if (state is WalletTransferFailure) {
              setState(() {
                _isLoadingGenerateReciept = false;
              });

              CustomToastification(context, message: state.message);
            }
          },
          builder: (context, state) {
            return RepaintBoundary(
              key: _screenshotKey,
              child: AnimatedContainer(
                clipBehavior: Clip.antiAlias,
                decoration: const ShapeDecoration(
                  color: Color(0xFFFCFCFC),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(36),
                      topRight: Radius.circular(36),
                    ),
                  ),
                ),
                duration: const Duration(milliseconds: 600),
                curve: Curves.easeInOut,
                height: screenHeight * _heightFraction,
                child: Column(
                  children: [
                    Flexible(
                      child: Column(
                        children: [
                          if (_heightFraction < 1)
                            Center(
                              child: Container(
                                width: 64,
                                height: 6,
                                margin: EdgeInsets.only(
                                  top: 16.h,
                                  left: 16.w,
                                  right: 16.w,
                                  bottom: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFBCBCBC),
                                  borderRadius: BorderRadius.circular(40),
                                ),
                              ),
                            ),
                          const SizedBox(
                            height: 30,
                          ),
                          Flexible(
                            child: SingleChildScrollView(
                              physics: const ClampingScrollPhysics(),
                              child: Container(
                                color: Colors.white,
                                padding: EdgeInsets.only(
                                  left: 16.w,
                                  right: 16.w,
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    if (_heightFraction < 1)
                                      const SizedBox(
                                        height: 8,
                                      )
                                    else
                                      SizedBox(
                                        height: Platform.isAndroid ? 40 : 56,
                                      ),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            padding: EdgeInsets.only(
                                              top: 11.h,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(32),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withAlpha(15),
                                                  blurRadius: 100,
                                                ),
                                              ],
                                            ),
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                Positioned.fill(
                                                  child: SizedBox.expand(
                                                    child: Lottie.asset(
                                                      MediaRes.confettiFile,
                                                      fit: BoxFit.fill,
                                                      repeat: false,
                                                      onLoaded: (composition) {
                                                        _controller
                                                          ..duration =
                                                              composition
                                                                  .duration
                                                          ..forward();
                                                      },
                                                    ),
                                                  ),
                                                ),
                                                Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Image.asset(
                                                      'assets/images/success_image.png',
                                                      height: 112.h,
                                                      width: 112.w,
                                                    ),
                                                    Container(
                                                      margin: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 12,
                                                      ),
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                        vertical: 8.w,
                                                      ),
                                                      decoration:
                                                          const BoxDecoration(
                                                        color: Colors.white,
                                                      ),
                                                      child: Column(
                                                        children: [
                                                          CustomBuildText(
                                                            text:
                                                                '${AppMapper.safeFormattedNumberWithDecimal(widget.billAmount)} ${widget.originalCurrency}',
                                                            textAlign: TextAlign
                                                                .center,
                                                            fontSize: 30.sp,
                                                            fontWeight:
                                                                FontWeight.w800,
                                                            color: const Color(
                                                              0xFF3AB73A,
                                                            ),
                                                            caseType: 'default',
                                                          ),
                                                          SizedBox(
                                                            height: 4.h,
                                                          ),
                                                          Padding(
                                                            padding:
                                                                EdgeInsets.only(
                                                              left: 16.w,
                                                              right: 16.w,
                                                            ),
                                                            child:
                                                                CustomBuildText(
                                                              text:
                                                                  widget.title,
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                              fontSize: 14.sp,
                                                              caseType:
                                                                  'default',
                                                              color:
                                                                  const Color(
                                                                0xFF6D6D6D,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 16,
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: 12.h,
                                    ),
                                    RepaintBoundary(
                                      key: _globalKey,
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 10.w,
                                          vertical: 16.h,
                                        ),
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFF8F8F8),
                                          borderRadius:
                                              BorderRadius.circular(24),
                                        ),
                                        child: Column(
                                          children: [
                                            Row(
                                              children: [
                                                CustomBuildText(
                                                  text: 'Transaction Detail',
                                                  textAlign: TextAlign.center,
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.w700,
                                                  caseType: 'default',
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 10.h,
                                            ),
                                            Container(
                                              padding: const EdgeInsets.all(10),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(24),
                                              ),
                                              child: Column(
                                                spacing: 10,
                                                children: [
                                                  Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                      vertical: 4,
                                                      horizontal: 4,
                                                    ),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        CustomBuildText(
                                                          text: 'Status',
                                                          textAlign:
                                                              TextAlign.center,
                                                          fontSize: 14.sp,
                                                          caseType: 'default',
                                                          color: const Color(
                                                            0xFF00B235,
                                                          ),
                                                        ),
                                                        Container(
                                                          padding: EdgeInsets
                                                              .symmetric(
                                                            horizontal: 14.w,
                                                            vertical: 6.h,
                                                          ),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: const Color(
                                                              0xFFE0FFE1,
                                                            ),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                              32,
                                                            ),
                                                          ),
                                                          child:
                                                              CustomBuildText(
                                                            text: widget.status,
                                                            textAlign: TextAlign
                                                                .center,
                                                            fontSize: 13.sp,
                                                            caseType: 'default',
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            color: const Color(
                                                              0xFF00B235,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),

                                                  // Dynamic details from model (filter out navigation keys)
                                                  ...widget.data.entries
                                                      .where((entry) =>
                                                          entry.key !=
                                                              'conversationId' &&
                                                          entry.key !=
                                                              'userAttributes')
                                                      .map(
                                                        (entry) =>
                                                            _buildTransactionList(
                                                          label: entry.key,
                                                          value: entry.value
                                                              .toString(),
                                                        ),
                                                      ),
                                                ],
                                              ),
                                            ),
                                            SizedBox(
                                              height: 14.h,
                                            ),
                                            Container(
                                              padding: const EdgeInsets.all(10),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(14),
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  CustomBuildText(
                                                    text: 'Total Amount',
                                                    textAlign: TextAlign.center,
                                                    fontSize: 16.sp,
                                                    fontWeight: FontWeight.w700,
                                                    caseType: 'default',
                                                  ),
                                                  CustomBuildText(
                                                    text:
                                                        '${AppMapper.safeFormattedNumberWithDecimal(widget.totalAmount)} ${widget.originalCurrency}',
                                                    textAlign: TextAlign.center,
                                                    fontSize: 16.sp,
                                                    fontWeight: FontWeight.w700,
                                                    caseType: 'default',
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 12.h,
                                    ),
                                    if (_heightFraction > 0.9 &&
                                        widget.showActionButtons)
                                      _buildActionButtons(context),
                                    SizedBox(
                                      height: 38.h,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(
                        top: 12.h,
                        left: 16.w,
                        right: 16.w,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(15),
                            blurRadius: 8,
                            offset: const Offset(0, -1),
                          ),
                        ],
                      ),
                      child: SafeArea(
                        child: CustomRoundedBtn(
                          btnText: widget.buttonText ??
                              (widget.isFromChat
                                  ? 'Back To Chat'
                                  : 'Back To Home '),
                          isLoading: false,
                          onTap: () {
                            print(
                              '🔥 Success screen button tapped - isFromChat: ${widget.isFromChat}',
                            );

                            if (widget.isGuestMode) {
                              context.pop();

                              context.goNamed(AppRouteName.guestHomePage);
                              // context.pop();
                              // context.goNamed(AppRouteName.guestSendMoney);
                              return;
                            }

                            // Handle navigation directly without popping first
                            if (widget.isFromChat) {
                              print(
                                '🔥 Navigating back to chat from success screen',
                              );
                              // Extract conversation ID from transaction data if available
                              final transactionData = widget.data;
                              if (transactionData
                                  .containsKey('conversationId')) {
                                final conversationId =
                                    transactionData['conversationId'] as String;
                                print(
                                  '🔥 Found conversationId: $conversationId',
                                );

                                // Extract user attributes if available
                                final userAttributes = transactionData
                                        .containsKey('userAttributes')
                                    ? transactionData['userAttributes']
                                        as Map<String, dynamic>?
                                    : null;

                                // Use pushReplacementNamed to avoid redirect issues
                                context.pushReplacementNamed(
                                  AppRouteName.chatInterface,
                                  pathParameters: {
                                    'conversationId': conversationId,
                                  },
                                  extra: userAttributes != null
                                      ? {
                                          'userAttributes':
                                              UserAttributesEntity(
                                            userId: userAttributes['userId']
                                                    as String? ??
                                                '',
                                            firstName:
                                                userAttributes['firstName']
                                                    as String?,
                                            lastName: userAttributes['lastName']
                                                as String?,
                                            nickname:
                                                userAttributes['displayName']
                                                    as String?,
                                            phoneNumber:
                                                userAttributes['phoneNumber']
                                                    as String?,
                                            email: userAttributes['email']
                                                as String?,
                                            avatarUrl:
                                                userAttributes['avatarUrl']
                                                    as String?,
                                          ),
                                        }
                                      : null,
                                );
                              } else {
                                print(
                                  '🔥 No conversationId found, using callback',
                                );
                                // Fallback: call the callback
                                widget.onContinue();
                              }
                            } else {
                              print('🔥 Going to home from success screen');
                              context.go('/main/main');
                            }
                          },
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTransactionList({
    required String label,
    required String value,
  }) {
    // return value.isEmpty
    //     ? const SizedBox.shrink()
    //     :
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomBuildText(
          text: label,
          textAlign: TextAlign.center,
          fontSize: 14.sp,
          caseType: 'default',
          color: const Color(0xFF979797),
        ),
        const SizedBox(
          width: 20,
        ),
        Flexible(
          child: CustomBuildText(
            text: '$value ',
            textAlign: TextAlign.right,
            fontSize: 14.sp,
            caseType: 'default',
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: const Color(0xFFF9F9F9),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          _actionButton(
            icon: MediaRes.qrReceiptIcon,
            label: 'QR receipt',
            onTap: showQrCode,
          ),
          if (_isLoadingShare)
            Container(
              child: const Row(
                children: [
                  SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(),
                  ),
                ],
              ),
            )
          else
            _actionButton(
              icon: MediaRes.sharingIcon,
              label: 'Share',
              onTap: _captureAndShare,
            ),
          _actionButton(
            icon: MediaRes.screenshotIcon,
            label: 'Screenshot',
            onTap:
                // () {
                //   setState(() {
                //     _isLoadingShare = false;
                //   });
                // }
                _takeFullPageScreenshot,
          ),
          if (_isLoadingGenerateReciept)
            Container(
              child: const Row(
                children: [
                  SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(),
                  ),
                ],
              ),
            )
          else
            _actionButton(
              icon: MediaRes.getReceiptIcon,
              label: 'Get receipt',
              onTap: _generateReciept,
            ),
        ],
      ),
    );
  }

  Widget _actionButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            Image.asset(
              icon,
              width: 40,
            ),
            SizedBox(
              height: 3.h,
            ),
            CustomBuildText(text: label),
          ],
        ),
      ),
    );
  }

  void showQrCode() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) => Container(
        child: CustomPagePadding(
          bottom: 24,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(40),
              topRight: Radius.circular(40),
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  height: 16,
                ),
                CustomBuildText(
                  text: 'QR with Transaction Details',
                  fontWeight: FontWeight.w600,
                  fontSize: 14.sp,
                  caseType: '',
                ),
                const SizedBox(
                  height: 16,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    RepaintBoundary(
                      key: _qrKey,
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFF9F9F9),
                          borderRadius: BorderRadius.circular(24),
                          border:
                              Border.all(color: Colors.black.withOpacity(0.04)),
                        ),
                        child: QrImageView(
                          data: generatedQRData,
                          size: 223.w,
                          padding: const EdgeInsets.all(24),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: 140.r,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.shade100,
                        offset: const Offset(0, 2),
                        spreadRadius: 2,
                      ),
                    ],
                    borderRadius: BorderRadius.circular(100.r),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildActionButton(
                        icon: MediaRes.downloadIcon,
                        onTap: _saveQrToGallery,
                      ),
                      SizedBox(width: 16.w),
                      _buildActionButton(
                        icon: MediaRes.icRoundShare,
                        onTap: _shareQrCode,
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String icon,
    required VoidCallback onTap,
  }) {
    return Container(
      width: 36.w,
      height: 36.w,
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.02),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: InkWell(
        onTap: onTap,
        child: _isDownloading && icon == MediaRes.downloadIcon
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              )
            : Image.asset(
                icon,
                color: Theme.of(context).primaryColor,
              ),
      ),
    );
  }

  Future<void> _saveQrToGallery() async {
    if (_isDownloading) return; // Prevent multiple simultaneous downloads

    setState(() => _isDownloading = true);

    try {
      // Request appropriate permissions based on platform and Android version
      PermissionStatus? status;

      if (Platform.isAndroid) {
        if (await Permission.photos.request().isGranted) {
          status = await Permission.photos.status;
        } else {
          status = await Permission.storage.request();
        }
      } else {
        status = await Permission.photos.request();
      }

      if (!status.isGranted) {
        if (mounted) {
          CustomToastification(
            context,
            message: 'Storage permission is required to save QR code',
          );
        }
        return;
      }

      // Capture QR code as image
      final boundary =
          _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return;

      final image = await boundary.toImage(pixelRatio: 3);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) return;

      // Save to gallery
      final result = await ImageGallerySaverPlus.saveImage(
        byteData.buffer.asUint8List(),
        name: 'Transaction Receipt QR Code',
        quality: 100,
      );

      if (mounted) {
        if (result['isSuccess'] == true) {
          CustomToastification(
            context,
            message: 'QR Code saved to gallery successfully',
            isError: false,
          );
        } else {
          CustomToastification(
            context,
            message: 'Failed to save QR Code to gallery',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        CustomToastification(
          context,
          message: 'Error saving QR Code: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isDownloading = false);
      }
    }
  }

  Future<void> _shareQrCode() async {
    try {
      // Capture QR code as image
      final boundary =
          _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) return;

      final image = await boundary.toImage(pixelRatio: 3);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) return;

      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/qr_code.png');
      await file.writeAsBytes(byteData.buffer.asUint8List());

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Transaction Receipt QR Code',
      );
    } catch (e) {
      if (mounted) {
        CustomToastification(
          context,
          message: 'Failed to share QR Code',
        );
      }
    }
  }
}
