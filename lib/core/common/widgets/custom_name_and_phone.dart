// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomNameAndPhone extends StatelessWidget {
  const CustomNameAndPhone({
    required this.name,
    required this.phoneOrEmail,
    super.key,
  });

  final String name;
  final String phoneOrEmail;
  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomBuildText(
            text: name,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 4.h),
          CustomBuildText(
            text: phoneOrEmail,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            overflow: TextOverflow.ellipsis,
            caseType: '',
            color: Colors.black.withOpacity(0.4),
          ),
        ],
      ),
    );
  }
}
