// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';

class CustomNameOrAvatar extends StatelessWidget {
  const CustomNameOrAvatar({
    required this.name,
    required this.avatar,
    super.key,
    this.width,
    this.height,
  });
  final String name;
  final String avatar;

  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? 48.w,
      height: height ?? 48.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Theme.of(context).primaryColor,
      ),
      clipBehavior: Clip.antiAlias,
      child: avatar.isNotEmpty
          ? CustomCachedImage(
              url: avatar,
            )
          : Center(
              child: CustomBuildText(
                text: name.isNotEmpty
                    ? name.substring(0, min(2, name.length)).toUpperCase()
                    : '??',
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
                caseType: 'all',
              ),
            ),
    );
  }
}
