import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';

class TransactionPinDialog extends StatefulWidget {
  final String billRefNo;
  final bool requiresOtp;
  final String? otp;
  final Function(String pin) onPinSubmitted;

  const TransactionPinDialog({
    Key? key,
    required this.billRefNo,
    required this.requiresOtp,
    this.otp,
    required this.onPinSubmitted,
  }) : super(key: key);

  @override
  State<TransactionPinDialog> createState() => _TransactionPinDialogState();
}

class _TransactionPinDialogState extends State<TransactionPinDialog> {
  final _pinController = TextEditingController();
  final _otpController = TextEditingController();
  bool _obscurePin = true;

  bool validatePin(String pin) {
    final repeatingPattern = RegExp(r'^(\d)\1{5}$');
    final sequentialPattern = RegExp(
        r'(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)');

    if (repeatingPattern.hasMatch(pin)) {
      CustomToastification(context, message: 'PIN cannot contain repeating digits.');
      return false;
    }

    if (sequentialPattern.hasMatch(pin)) {
      CustomToastification(context, message: 'PIN cannot contain sequential numbers.');
      return false;
    }

    return true;
  }

  void _handleSubmit() {
    final pin = _pinController.text;
    final otp = _otpController.text;
    
    if (pin.length != 6) {
      CustomToastification(context, message: 'PIN must be exactly 6 digits.');
      return;
    }

    if (pin.contains(RegExp(r'\D'))) {
      CustomToastification(context, message: 'PIN must contain only numbers.');
      return;
    }

    if (!validatePin(pin)) {
      return;
    }

    if (widget.requiresOtp && otp.length != 6) {
      CustomToastification(context, message: 'OTP must be exactly 6 digits.');
      return;
    }

    widget.onPinSubmitted(pin);
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _pinController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Enter PIN to Verify Transfer',
              style: GoogleFonts.outfit(
                fontSize: 24.sp,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'Please enter your PIN${widget.requiresOtp ? ' and OTP' : ''} to verify this transfer.',
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                color: const Color(0xFFAAAAAA),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),
            Text(
              'Enter PIN',
              style: GoogleFonts.outfit(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8.h),
            Pinput(
              length: 6,
              controller: _pinController,
              obscureText: _obscurePin,
              defaultPinTheme: PinTheme(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                textStyle: GoogleFonts.outfit(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (widget.requiresOtp) ...[
              SizedBox(height: 24.h),
              Text(
                'Enter OTP',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8.h),
              Pinput(
                length: 6,
                controller: _otpController,
                defaultPinTheme: PinTheme(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  textStyle: GoogleFonts.outfit(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
            SizedBox(height: 16.h),
            TextButton(
              onPressed: () => setState(() => _obscurePin = !_obscurePin),
              child: Text(
                _obscurePin ? 'Show PIN' : 'Hide PIN',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            SizedBox(height: 24.h),
            CustomButton(
              text: 'Verify',
              onPressed: _handleSubmit,
              options: CustomButtonOptions(
                width: double.infinity,
                height: 56.h,
                color: Theme.of(context).primaryColor,
                textStyle: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                borderRadius: BorderRadius.circular(32.r),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 