import 'package:cbrs/core/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomBuildText extends StatelessWidget {
  const CustomBuildText({
    required this.text,
    super.key,
    this.color = Colors.black,
    this.fontWeight = FontWeight.w400,
    this.fontSize = 14,
    this.textAlign,
    this.overflow,
    this.caseType = 'eachWord',
    this.maxLines,
    this.softWrap,
    this.style,
  });
  final String text;
  final Color color;
  final FontWeight fontWeight;
  final double fontSize;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;
  final bool? softWrap;
  final String caseType;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return Text(
      getText(),
      textAlign: textAlign ?? TextAlign.left,
      overflow: overflow,
      maxLines: maxLines,
      softWrap: softWrap,
      style: style ??
          GoogleFonts.outfit(
            color: color,
            fontSize: fontSize,
            fontWeight: fontWeight,
          ),
    );
  }

  String getText() {
    // Special handling for currency codes like USD and ETB
   /*
    if (text.contains('USD') ||
        text.contains('USD'.toLowerCase()) ||
        text.contains('ETB') ||
        text.contains('ETB'.toLowerCase())) {
      final words = text.split(' ');
      return words.map((word) {
        if (word.toUpperCase() == 'USD' || word.toUpperCase() == 'ETB') {
          return word.toUpperCase();
        } else if (word.contains('USD') ||
            word.contains('USD'.toLowerCase()) ||
            word.contains('ETB') ||
            word.contains('ETB'.toLowerCase())) {
          // Handle currency amounts with attached currency code
          final numericPart = word.replaceAll(RegExp(r'[^\d.,]'), '');
          if (word.toUpperCase().contains('USD')) {
            return '$numericPart USD';
          } else if (word.toUpperCase().contains('ETB')) {
            return '$numericPart g ETB';
          }
          return word;
        }

        switch (caseType) {
          case 'eachWord':
            return text.capitalizeFirstofEach;
          case 'all':
            return word.toUpperCase();
          case 'default':
          default:
            return word;
        }
      }).join(' ');
    }
    */


    if (text.toUpperCase().contains('USD') || text.toUpperCase().contains('ETB')) {
  final words = text.split(' ');
  return words.map((word) {
    final upperWord = word.toUpperCase();

    if (upperWord == 'USD' || upperWord == 'ETB') {
      // Just the currency code, uppercase it
      return upperWord;
    } else if (upperWord.contains('USD') || upperWord.contains('ETB')) {
      // Currency amount with attached code
      final numericPart = word.replaceAll(RegExp(r'[^\d.,]'), '');

      if (upperWord.contains('USD')) {
        return '$numericPart USD';
      } else if (upperWord.contains('ETB')) {
        return '$numericPart ETB'; 
      }
      return word;
    }

    // Your existing caseType handling here
    switch (caseType) {
      case 'eachWord':
        return word.capitalizeFirstofEach;  // Assuming this works for the word
      case 'all':
        return word.toUpperCase();
      case 'default':
      default:
        return word;
    }
  }).join(' ');
}



if(text.toLowerCase().contains('USD') || text.toLowerCase().contains('ETB') ){

}
    switch (caseType) {
      case 'eachWord':
        return text.capitalizeFirstofEach;
      case 'all':
        return text.allInCaps;
      case 'default':
        return text;

      default:
        return text.inCaps;
    }
  }
}
