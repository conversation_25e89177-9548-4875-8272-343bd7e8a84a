import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomPageHeader extends StatelessWidget {
  const CustomPageHeader({
    required this.pageTitle,
    required this.description,
    super.key,
    this.descriptionColor,
    this.titleColor,
  });
  final String pageTitle;
  final String description;
  final Color? descriptionColor;
  final Color? titleColor;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          pageTitle,
          style: GoogleFonts.outfit(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: titleColor ?? Colors.black,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          description,
          style: GoogleFonts.outfit(
            fontSize: 14.sp,
            color: descriptionColor ?? const Color(0xFFAAAAAA),
            fontWeight: FontWeight.w400,
            letterSpacing: 0
          ),
        ),
      ],
    );
  }
}
