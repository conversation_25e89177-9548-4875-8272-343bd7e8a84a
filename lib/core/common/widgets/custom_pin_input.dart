import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';

class CustomPinInput extends StatefulWidget {
  const CustomPinInput({
    required this.controller,
    super.key,
    this.length = 6,
    this.width,
    this.height,
    this.textStyle,
    this.defaultDecoration,
    this.focusedDecoration,
    this.autoFocus = false,
    this.isObscured = true,
    this.pinFocusNode,
    this.onChange,
  });
  final int length;
  final TextEditingController controller;
  final double? width;
  final double? height;
  final TextStyle? textStyle;
  final BoxDecoration? defaultDecoration;
  final BoxDecoration? focusedDecoration;
  final bool autoFocus;
  final bool isObscured;
  final FocusNode? pinFocusNode;

  final void Function(String)? onChange;

  @override
  _CustomPinInputState createState() => _CustomPinInputState();
}

class _CustomPinInputState extends State<CustomPinInput> {
  List<FocusNode> _focusNodes = [];
  List<String> _pinValues = [];

  @override
  void initState() {
    super.initState();
    _focusNodes = List.generate(widget.length, (_) => FocusNode());
    _pinValues = List.generate(widget.length, (_) => '');

    // Add listener to maintain focus when text changes
    widget.controller.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    // Ensure focus is maintained when text changes (including backspace)
    if (widget.pinFocusNode != null && !widget.pinFocusNode!.hasFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && widget.pinFocusNode != null) {
          widget.pinFocusNode!.requestFocus();
        }
      });
    }
  }

//   @override
// void dispose(){
//   _pinFocusNode.dispose();
//   super.dispose();
// }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final defaultTheme = PinTheme(
      width: 56.w,
      height: 56.h,
      textStyle: GoogleFonts.outfit(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: theme.primaryColor,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFF2C2B34).withOpacity(0.04),
        borderRadius: BorderRadius.circular(12.r),
      ),
    );

    return Pinput(
      length: 6,
      autofocus: widget.autoFocus,
      focusNode: widget.pinFocusNode,
      preFilledWidget: const Icon(
        Icons.remove,
        color: Color(0xFFAAAAAA),
        size: 10,
      ),
      obscureText: widget.isObscured,
      obscuringCharacter: '*',
      onChanged: (value) {
        widget.onChange?.call(value);

        if (widget.pinFocusNode != null && !widget.pinFocusNode!.hasFocus) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && widget.pinFocusNode != null) {
              widget.pinFocusNode!.requestFocus();
            }
          });
        }
      },
      controller: widget.controller,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      defaultPinTheme: defaultTheme,
      focusedPinTheme: defaultTheme,
      submittedPinTheme: defaultTheme,
      onTapOutside: (event) {
        // Keep focus on the pin input when tapping outside
        if (widget.pinFocusNode != null) {
          widget.pinFocusNode!.requestFocus();
        }
      },
      // defaultPinTheme: PinTheme(
      //   width: widget.width?? 60.w,
      //   height: widget.width?? 56.h,
      //   textStyle: GoogleFonts.outfit(
      //     fontSize: 20.sp,
      //     fontWeight: FontWeight.w600,
      //     color: theme.primaryColor,
      //   ),
      //   decoration: BoxDecoration(
      //     color: theme.colorScheme.onTertiary,
      //     borderRadius: BorderRadius.circular(8),
      //     border: Border.all(color: Colors.grey.shade200, width: 1),
      //   ),
      // ),
      // focusedPinTheme: PinTheme(
      //   width: widget.width?? 60.w,
      //   height: widget.width?? 56.h,
      //   textStyle: GoogleFonts.outfit(
      //     fontSize: 20,
      //     fontWeight: FontWeight.w600,
      //     color: theme.primaryColor,
      //   ),
      //   decoration: BoxDecoration(
      //     color: theme.colorScheme.onTertiary,
      //     borderRadius: BorderRadius.circular(8),
      //     border: Border.all(color: theme.primaryColor),
      //     boxShadow: [
      //       BoxShadow(
      //         color: theme.primaryColor.withOpacity(0.1),
      //         blurRadius: 8,
      //         spreadRadius: 2,
      //       ),
      //     ],
      //   ),
      // ),
    );
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    for (final node in _focusNodes) {
      node.dispose();
    }
    super.dispose();
  }
}
