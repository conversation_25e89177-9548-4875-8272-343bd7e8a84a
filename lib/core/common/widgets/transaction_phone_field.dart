import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:phone_form_field/phone_form_field.dart';

class TransactionPhoneField extends StatelessWidget {
  final PhoneController controller;
  final FocusNode focusNode;
  final bool withLabel;
  final bool outlineBorder;
  final bool isCountryButtonPersistant;
  final bool mobileOnly;
  final VoidCallback? onEditingComplete;

  const TransactionPhoneField({
    Key? key,
    required this.controller,
    required this.focusNode,
    this.withLabel = true,
    this.outlineBorder = true,
    this.isCountryButtonPersistant = true,
    this.mobileOnly = true,
    this.onEditingComplete,
  }) : super(key: key);

  PhoneNumberInputValidator? _getValidator(BuildContext context) {
    List<PhoneNumberInputValidator> validators = [];
    if (mobileOnly) {
      validators.add(PhoneValidator.validMobile(context));
    } else {
      validators.add(PhoneValidator.valid(context));
    }
    return validators.isNotEmpty ? PhoneValidator.compose(validators) : null;
  }

  @override
  Widget build(BuildContext context) {
    return AutofillGroup(
      child: Builder(
        builder: (context) {
          final label = PhoneFieldLocalization.of(context).phoneNumber;
          return PhoneFormField(
            focusNode: focusNode,
            controller: controller,
            onEditingComplete: onEditingComplete,
            isCountryButtonPersistent: isCountryButtonPersistant,
            autofocus: false,
            autofillHints: const [AutofillHints.telephoneNumber],
            // countrySelectorNavigator: selectorNavigator,
            scrollPadding: const EdgeInsets.all(0),
            countryButtonStyle: CountryButtonStyle(
              flagSize: 24,
            ),
            inputFormatters: [
              LengthLimitingTextInputFormatter(
                  15), // this limits number of inputs to 15 chars
            ],
            countrySelectorNavigator: CountrySelectorNavigator.modalBottomSheet(
              height: MediaQuery.of(context).size.height * 0.8,
            ),
            decoration: InputDecoration(
              filled: true,
              fillColor: const Color(0xFFF5F5F5),
              hintText: 'Enter phone number',
              hintStyle:GoogleFonts.outfit(
                color: const Color(0xFF7C7C7C),
                fontSize: 16.sp,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
              errorStyle: const TextStyle(height: 0),
            ),

            enabled: true,
            // countryButtonStyle: const CountryButtonStyle(
            //   showFlag: true,
            //   showIsoCode: false,
            //   showDialCode: true,
            //   showDropdownIcon: true,
            // ),
            validator: _getValidator(context),
            autovalidateMode: AutovalidateMode.onUnfocus,
            cursorColor: Theme.of(context).colorScheme.primary,
            // ignore: avoid_print
            onSaved: (p) => print('saved $p'),
            // ignore: avoid_print
            onChanged: (p) => print('changed $p'),
          );
        },
      ),
    );
  }
}
