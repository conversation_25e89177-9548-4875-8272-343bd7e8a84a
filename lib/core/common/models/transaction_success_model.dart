import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:flutter/material.dart';

/// Class for individual success detail item
class SuccessDetailItem {
  const SuccessDetailItem({
    required this.label,
    required this.value,
  });
  final String label;
  final String value;
}

/// Field definition for configuring detail items
class FieldDef {
  const FieldDef({
    required this.label,
    required this.key,
    this.formatter,
    this.condition,
  });

  final String label;
  final String key;
  final String Function(dynamic value, String currency)? formatter;
  final bool Function(Map<String, dynamic> data)? condition;

  /// Evaluate if this field should be displayed
  bool shouldDisplay(Map<String, dynamic> data) {
    if (condition != null) {
      return condition!(data);
    }

    // For nested keys, try to navigate through the object
    if (key.contains('.')) {
      final value = _getNestedValue(data, key);
      return value != null;
    }

    return data[key] != null;
  }

  /// Get formatted value for this field
  String getValue(Map<String, dynamic> data, String currency) {
    dynamic value;

    // Handle nested property paths with dot notation
    if (key.contains('.')) {
      value = _getNestedValue(data, key);
    } else {
      value = data[key];
    }

    if (value == null) return '';

    if (formatter != null) {
      return formatter!(value, currency);
    }

    return value.toString();
  }

  /// Helper method to get value from nested objects using dot notation
  /// e.g., "transaction.billRefNo" will navigate to data["transaction"]["billRefNo"]
  dynamic _getNestedValue(Map<String, dynamic> data, String path) {
    final parts = path.split('.');
    dynamic current = data;

    for (final part in parts) {
      if (current is! Map<String, dynamic> && current is! Map) {
        return null;
      }

      current = current[part];
      if (current == null) {
        return null;
      }
    }

    return current;
  }
}

/// Transaction type configuration
class TransactionConfig {
  const TransactionConfig({
    required this.title,
    required this.fields,
    required this.summaryLabel,
    this.amountKey = 'totalAmount',
    this.currencyKey = 'originalCurrency',
    this.statusKey = 'status',
    this.defaultCurrency = 'ETB',
    this.defaultStatus = 'Paid',
    this.amountFormatter,
  });

  final String title;
  final List<FieldDef> fields;
  final String summaryLabel;
  final String amountKey;
  final String currencyKey;
  final String statusKey;
  final String defaultCurrency;
  final String defaultStatus;
  final String Function(dynamic value)? amountFormatter;
}

/// Main transaction success model
class TransactionSuccessModel {
  const TransactionSuccessModel({
    required this.title,
    required this.amount,
    required this.currency,
    required this.details,
    required this.summary,
    this.status,
    this.statusColor,
    this.statusBgColor,
    this.imageAsset,
    this.showActionButtons = true,
  });

  /// Factory method to create display model from transaction data
  factory TransactionSuccessModel.fromData({
    required Map<String, dynamic> data,
    required String transactionType,
  }) {
    // Get config for this transaction type
    final config = _getConfig(transactionType);

    // Extract common values
    final currency =
        data[config.currencyKey] as String? ?? config.defaultCurrency;
    final status = data[config.statusKey] as String? ?? config.defaultStatus;

    // Format amount based on config
    final amount = _formatAmount(data, config);

    // Build detail items from field definitions
    final details = <SuccessDetailItem>[];
    for (final field in config.fields) {
      if (field.shouldDisplay(data)) {
        // Special case for vehicle in car loan
        if (transactionType == 'car_loan' && field.key == '_vehicle') {
          final make = data['vehicleMake'] as String? ?? '';
          final model = data['vehicleModel'] as String? ?? '';
          if (make.isNotEmpty || model.isNotEmpty) {
            details.add(
              SuccessDetailItem(
                label: field.label,
                value: '$make $model'.trim(),
              ),
            );
          }
          continue;
        }

        details.add(
          SuccessDetailItem(
            label: field.label,
            value: field.getValue(data, currency),
          ),
        );
      }
    }

    return TransactionSuccessModel(
      title: config.title,
      amount: amount,
      currency: currency,
      status: status,
      statusColor: const Color(0xFF3AB73A),
      statusBgColor: const Color(0xFFE6F7E6),
      imageAsset: 'assets/images/success_image.png',
      details: details,
      summary: SuccessDetailItem(
        label: config.summaryLabel,
        value: '$amount $currency',
      ),
    );
  }

  /// Get amount formatted as string
  static String _formatAmount(
    Map<String, dynamic> data,
    TransactionConfig config,
  ) {
    final value = data[config.amountKey];

    if (config.amountFormatter != null) {
      return config.amountFormatter!(value);
    }

    if (value == null) {
      return '0.0';
    } else if (value is num) {
      return value.toStringAsFixed(2);
    } else if (value is String) {
      final num = double.tryParse(value);
      if (num != null) {
        return num.toStringAsFixed(2);
      }
      return value;
    }

    return AppMapper.safeFormattedNumberWithDecimal(value);
  }

  /// Get configuration for a transaction type
  static TransactionConfig _getConfig(String transactionType) {
    return _transactionConfigs[transactionType] ?? _createDefaultConfig();
  }

  /// Create default configuration for unknown transaction types
  static TransactionConfig _createDefaultConfig() {
    return const TransactionConfig(
      title: 'Your transaction was completed successfully.',
      summaryLabel: 'Total',
      fields: [],
    );
  }

  // Instance fields
  final String title;
  final String amount;
  final String currency;
  final String? status;
  final Color? statusColor;
  final Color? statusBgColor;
  final String? imageAsset;
  final List<SuccessDetailItem> details;
  final SuccessDetailItem summary;
  final bool showActionButtons;

  /// Map of transaction types to their configurations
  static final Map<String, TransactionConfig> _transactionConfigs = {
    'bank_transfer': TransactionConfig(
      title: 'Your bank transfer was completed successfully.',
      summaryLabel: 'Total',
      fields: [
        const FieldDef(label: 'Recipient Name', key: 'beneficiaryName'),
        const FieldDef(label: 'Recipient Account', key: 'beneficiaryAccountNo'),
        const FieldDef(label: 'Bank Name', key: 'bankName'),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        FieldDef(
          label: 'Payment Date',
          key: 'createdAt',
          formatter: (value, _) => AppMapper.safeFormattedDate(value),
        ),
        FieldDef(
          label: 'Service Fee',
          key: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'VAT(15%)',
          key: 'VAT',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Amount',
          key: 'billAmount',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
    ),

    'wallet_transfer': TransactionConfig(
      title: 'Your wallet transfer was completed successfully.',
      summaryLabel: 'Total',
      fields: [
        const FieldDef(label: 'Recipient Name', key: 'beneficiaryName'),
        const FieldDef(label: 'Recipient Phone', key: 'beneficiaryPhoneNumber'),
        const FieldDef(label: 'Recipient Email', key: 'beneficiaryEmail'),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        const FieldDef(label: 'Payment Date', key: 'createdAt'),
        FieldDef(
          label: 'Service Fee',
          key: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'VAT(15%)',
          key: 'VAT',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Amount',
          key: 'billAmount',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
    ),

    'change_to_birr': TransactionConfig(
      title: 'Your Change To Birr was completed successfully.',
      summaryLabel: 'Total',
      fields: [
        const FieldDef(label: 'Transaction Type', key: 'transactionType'),
        const FieldDef(label: 'Amount In USD', key: 'totalAmount'),
        const FieldDef(label: 'Amount In ETB', key: 'amountInUsd'),
        const FieldDef(label: 'Exchange Rate', key: 'exchangeRate'),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        const FieldDef(label: 'Date', key: 'createdAt'),
        FieldDef(
          label: 'Service Fee',
          key: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'VAT(15%)',
          key: 'VAT',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Amount',
          key: 'billAmount',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
    ),

    'utility_payment': TransactionConfig(
      title: 'Your utility payment was completed successfully.',
      summaryLabel: 'Total',
      fields: [
        const FieldDef(label: 'Utility', key: 'utilityName'),
        const FieldDef(label: 'Account Number', key: 'accountNumber'),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        const FieldDef(label: 'Payment Date', key: 'createdAt'),
        FieldDef(
          label: 'Service Fee',
          key: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'VAT(15%)',
          key: 'VAT',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Amount',
          key: 'billAmount',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
    ),

    'add_money': TransactionConfig(
      title: 'Money Added Successfully',
      summaryLabel: 'Total Amount',
      fields: [
        const FieldDef(label: 'Sender Name', key: 'senderName'),
        const FieldDef(label: 'Account Number', key: 'accountNumber'),
        const FieldDef(label: 'Bank Name', key: 'bankName'),
        FieldDef(
          label: 'Amount',
          key: 'billAmount',
          formatter: (value, currency) {
            if (value is num) {
              return '${value.toStringAsFixed(2)} $currency';
            }
            return '$value $currency';
          },
        ),
        FieldDef(
          label: 'Service Fee',
          key: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'VAT',
          key: 'VAT',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Date',
          key: 'createdAt',
          formatter: (value, _) => AppMapper.safeFormattedDate(value),
        ),
        const FieldDef(label: 'FT Number', key: 'walletFTNumber'),
        const FieldDef(label: 'Transaction Ref', key: 'billRefNo'),
      ],
    ),

    'load_to_wallet': TransactionConfig(
      title: 'Your Wallet is now Ready',
      summaryLabel: 'Total',
      fields: [
        FieldDef(
          label: 'Transaction Type',
          key: '_none',
          formatter: (_, __) => 'Load To Wallet',
          condition: (_) => true,
        ),
        const FieldDef(label: 'Card Number', key: 'cardNumber'),
        const FieldDef(label: 'Card Holder Name', key: 'cardHolderName'),
        const FieldDef(label: 'Transaction Ref.', key: 'billRefNo'),
        FieldDef(
          label: 'Date',
          key: 'createdAt',
          formatter: (value, _) => AppMapper.safeFormattedDate(value),
        ),
        FieldDef(
          label: 'Amount',
          key: 'totalAmount',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
      amountFormatter: AppMapper.safeFormattedNumberWithDecimal,
    ),

    'top_up': TransactionConfig(
      title: 'Your mobile top-up was completed successfully. '
          'Thank you for using our service!',
      summaryLabel: 'Total',
      amountKey: 'billAmount',
      fields: [
        const FieldDef(label: 'Transaction Type', key: 'transactionType'),
        const FieldDef(label: 'Sender Name', key: 'senderName'),
        const FieldDef(label: 'Sender Phone Number', key: 'recipentPhone'),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        const FieldDef(label: 'Payment Date', key: 'createdAt'),
      ],
      amountFormatter: AppMapper.safeFormattedNumberWithDecimal,
    ),

    'gift_package': TransactionConfig(
      title: 'Gift Package Purchase Successful',
      summaryLabel: 'Total Amount',
      amountKey: 'sellingPrice',
      defaultCurrency: 'USD',
      fields: [
        const FieldDef(label: 'Customer Name', key: 'customerName'),
        const FieldDef(label: 'Recipient Name', key: 'recipientName'),
        const FieldDef(label: 'Recipient Phone', key: 'recipientPhone'),
        const FieldDef(label: 'Package Name', key: 'packageName'),
        FieldDef(
          label: 'Package Price',
          key: 'unitPrice',
          formatter: (value, currency) => '$value $currency',
        ),
        const FieldDef(label: 'Quantity', key: 'quantity'),
        const FieldDef(label: 'Super Market', key: 'supermarketName'),
        const FieldDef(label: 'Order Code', key: 'orderCode'),
        const FieldDef(label: 'Payment Reference', key: 'paymentReference'),
        FieldDef(
          label: 'Payment Date',
          key: 'createdAt',
          formatter: (value, _) => AppMapper.safeFormattedDate(value),
        ),
        FieldDef(
          label: 'Service Fee',
          key: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'VAT',
          key: 'VAT',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
    ),

    'money_request': TransactionConfig(
      title: 'Your money request was sent successfully',
      summaryLabel: 'Total',
      amountKey: 'billAmount',
      currencyKey: 'currency',
      defaultStatus: 'WAITING',
      fields: [
        const FieldDef(label: 'Requested From', key: 'beneficiaryName'),
        const FieldDef(label: 'Requested Email', key: 'beneficiaryEmail'),
        const FieldDef(label: 'Requested Phone', key: 'beneficiaryPhone'),
        FieldDef(
          label: 'Amount',
          key: 'amount',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Date',
          key: 'createdAt',
          formatter: (value, _) => AppMapper.safeFormattedDate(value),
        ),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        FieldDef(
          label: 'Reason',
          key: 'reason',
          condition: (data) =>
              data['reason'] != null && (data['reason'] as String).isNotEmpty,
        ),
      ],
    ),

    // Create specialized configurations for more complex cases
    'car_loan': _createCarLoanConfig(),
    'mortgage_loan': _createMortgageLoanConfig(),
    'merchant_payment': _createMerchantPaymentConfig(),
  };

  /// Creates configuration for car loans
  static TransactionConfig _createCarLoanConfig() {
    return TransactionConfig(
      title: 'Your car loan application fee payment was successful.',
      summaryLabel: 'Application Fee',
      defaultCurrency: 'USD',
      defaultStatus: 'Pending',
      fields: [
        const FieldDef(label: 'Bank Name', key: 'bankName'),
        FieldDef(
          label: 'Vehicle',
          key: '_vehicle',
          condition: (data) =>
              data['vehicleMake'] != null || data['vehicleModel'] != null,
          formatter: (_, currency) {
            // This is a special case where we need to access the global data
            // We'll handle this in the fromData method instead
            return 'Vehicle';
          },
        ),
        FieldDef(
          label: 'Loan Amount',
          key: 'loanAmount',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Upfront Payment',
          key: 'upfrontPayment',
          formatter: (value, _) => '$value%',
        ),
        FieldDef(
          label: 'Upfront Amount',
          key: 'upfrontAmount',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Interest Rate',
          key: 'interestRate',
          formatter: (value, _) => '$value%',
        ),
        const FieldDef(label: 'Loan Term', key: 'loanTerm'),
        FieldDef(
          label: 'Application Fee',
          key: 'applicationFee',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Bill Amount',
          key: 'billAmount',
          formatter: (value, currency) => '$value $currency',
        ),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        FieldDef(
          label: 'Date',
          key: 'createdAt',
          formatter: (value, _) => AppMapper.safeFormattedDate(value),
        ),
      ],
      amountKey: 'applicationFee',
    );
  }

  /// Creates configuration for mortgage loans
  static TransactionConfig _createMortgageLoanConfig() {
    return TransactionConfig(
      title: 'Your mortgage loan application fee payment was successful.',
      summaryLabel: 'Application Fee',
      defaultCurrency: 'USD',
      defaultStatus: 'SUCCESS',
      amountKey: 'applicationFee',
      statusKey: 'status',
      amountFormatter: (value) {
        if (value == null) return '0.0';
        if (value is num) {
          return value.toStringAsFixed(2);
        } else if (value is String) {
          final num = double.tryParse(value);
          return num?.toStringAsFixed(2) ?? value;
        }
        return value.toString();
      },
      fields: [
        // Transaction details
        FieldDef(
          label: 'Transaction Reference',
          key: 'transaction.billRefNo',
          formatter: (value, _) => value is Map
              ? value['billRefNo']?.toString() ?? ''
              : value?.toString() ?? '',
          condition: (data) => data['transaction'] != null,
        ),

        // Bank details
        FieldDef(
          label: 'Bank',
          key: 'bank.name',
          formatter: (value, _) => value is Map
              ? value['name']?.toString() ?? ''
              : value?.toString() ?? '',
          condition: (data) =>
              data['bank'] != null && data['bank']['name'] != null,
        ),

        // Property details
        FieldDef(
          label: 'Property Type',
          key: 'productDetails.propertyType',
          formatter: (value, _) => value is Map
              ? value['propertyType']?.toString() ?? ''
              : value?.toString() ?? '',
          condition: (data) =>
              data['productDetails'] != null &&
              data['productDetails']['propertyType'] != null,
        ),
        FieldDef(
          label: 'Property Price',
          key: 'productDetails.price',
          formatter: (value, currency) {
            if (value is Map) {
              final price = value['price'];
              if (price is num) {
                return '${price.toStringAsFixed(2)} $currency';
              }
              return '${price?.toString() ?? ''} $currency';
            }
            return value?.toString() ?? '';
          },
          condition: (data) =>
              data['productDetails'] != null &&
              data['productDetails']['price'] != null,
        ),
        FieldDef(
          label: 'Property Address',
          key: 'productDetails.address',
          formatter: (value, _) => value is Map
              ? value['address']?.toString() ?? ''
              : value?.toString() ?? '',
          condition: (data) =>
              data['productDetails'] != null &&
              data['productDetails']['address'] != null,
        ),

        // Loan details
        FieldDef(
          label: 'Upfront Payment',
          key: 'loanDetails.upfrontPayment',
          formatter: (value, _) {
            if (value is Map) {
              final upfrontPayment = value['upfrontPayment'];
              return '$upfrontPayment%';
            }
            return value?.toString() ?? '';
          },
          condition: (data) =>
              data['loanDetails'] != null &&
              data['loanDetails']['upfrontPayment'] != null,
        ),
        FieldDef(
          label: 'Loan Period',
          key: 'loanDetails.loanPeriod',
          formatter: (value, _) {
            if (value is Map) {
              final loanPeriod = value['loanPeriod'];
              return '$loanPeriod years';
            }
            return value?.toString() ?? '';
          },
          condition: (data) =>
              data['loanDetails'] != null &&
              data['loanDetails']['loanPeriod'] != null,
        ),
        FieldDef(
          label: 'Application Fee',
          key: 'loanDetails.applicationFee',
          formatter: (value, currency) {
            if (value is Map) {
              final applicationFee = value['applicationFee'];
              if (applicationFee is num) {
                return '${applicationFee.toStringAsFixed(2)} $currency';
              }
              return '${applicationFee?.toString() ?? ''} $currency';
            }
            return value?.toString() ?? '';
          },
          condition: (data) =>
              data['loanDetails'] != null &&
              data['loanDetails']['applicationFee'] != null,
        ),
        FieldDef(
          label: 'Loan Amount',
          key: 'loanDetails.loanAmount',
          formatter: (value, currency) {
            if (value is Map) {
              final loanAmount = value['loanAmount'];
              if (loanAmount is num) {
                return '${loanAmount.toStringAsFixed(2)} $currency';
              }
              return '${loanAmount?.toString() ?? ''} $currency';
            }
            return value?.toString() ?? '';
          },
          condition: (data) =>
              data['loanDetails'] != null &&
              data['loanDetails']['loanAmount'] != null,
        ),
        FieldDef(
          label: 'Monthly Payment',
          key: 'loanDetails.monthlyPayment',
          formatter: (value, currency) {
            if (value is Map) {
              final monthlyPayment = value['monthlyPayment'];
              if (monthlyPayment is num) {
                return '${monthlyPayment.toStringAsFixed(2)} $currency';
              }
              return '${monthlyPayment?.toString() ?? ''} $currency';
            }
            return value?.toString() ?? '';
          },
          condition: (data) =>
              data['loanDetails'] != null &&
              data['loanDetails']['monthlyPayment'] != null,
        ),

        // Payment date
        FieldDef(
          label: 'Payment Date',
          key: 'transaction.paidDate',
          formatter: (value, _) {
            if (value is Map) {
              final paidDate = value['paidDate'];
              return AppMapper.safeFormattedDate(paidDate);
            }
            return AppMapper.safeFormattedDate(value);
          },
          condition: (data) =>
              data['transaction'] != null &&
              data['transaction']['paidDate'] != null,
        ),
      ],
    );
  }

  /// Creates configuration for merchant payments
  static TransactionConfig _createMerchantPaymentConfig() {
    return TransactionConfig(
      title: 'Your payment to merchant was completed successfully.',
      summaryLabel: 'Total',
      fields: [
        const FieldDef(label: 'Merchant', key: 'merchantName'),
        const FieldDef(label: 'Merchant ID', key: 'merchantId'),
        const FieldDef(label: 'Transaction Reference', key: 'billRefNo'),
        const FieldDef(label: 'Payment Date', key: 'createdAt'),
        FieldDef(
          label: 'Service Fee',
          key: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'VAT(15%)',
          key: 'VAT',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDef(
          label: 'Amount',
          key: 'amount',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
    );
  }

  // Removed unused _formatLabel method
}
