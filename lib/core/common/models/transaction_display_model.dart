import 'package:cbrs/core/common/models/transaction_types.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:cbrs/core/extensions/string_extensions.dart';

/// Model for individual transaction detail
class TransactionDetail {
  TransactionDetail({required this.label, required this.value});
  final String label;
  final String value;
}

/// Model for transaction summary (typically total)
class TransactionSummary {
  TransactionSummary({required this.label, required this.value});
  final String label;
  final String value;
}

/// Field definition for transaction details
class FieldDefinition {
  FieldDefinition({
    required this.label,
    required this.dataKey,
    this.defaultValue,
    this.showIfEmpty = false,
    this.includesCurrency = false,
    this.formatter,
  });
  final String label;
  final String dataKey;
  final String? defaultValue;
  final bool? showIfEmpty;
  final bool? includesCurrency;
  final String? Function(dynamic, String)? formatter;

  /// Get value from data map
  String getValue(Map<String, dynamic> data, String currency) {
    final dynamic value = data[dataKey];

    // Apply formatter if provided
    if (formatter != null && value != null) {
      return formatter!(value, currency) ?? defaultValue ?? '';
    }

    // Format based on value type
    if (value == null) {
      return defaultValue ?? '';
    } else if (value is num && includesCurrency == true) {
      return '$value $currency';
    } else {
      return value.toString();
    }
  }
}

/// A detailed model for a transaction display
class TransactionDisplayModel {
  // Constructor
  TransactionDisplayModel({
    required this.title,
    required this.details,
    required this.summary,
    this.imageAsset = defaultImageAsset,
    this.status,
    this.statusColor = defaultStatusColor,
    this.statusBgColor = defaultStatusBgColor,
  });

  /// Factory to create from raw data map for different transaction types
  factory TransactionDisplayModel.fromData({
    required Map<String, dynamic> data,
    required String transactionType,
  }) {
    final transactionTypeEnum = TransactionType.fromString(transactionType);
    final common = _extractCommonData(data);

    // Get configuration for this transaction type
    final config = _getTransactionConfig(transactionTypeEnum);

    // Create transaction details based on field definitions
    final details = _buildTransactionDetails(
      data: data,
      common: common,
      fieldDefinitions: config.fieldDefinitions,
      conditionalFields: config.conditionalFields,
    );

    // Create the transaction model
    return TransactionDisplayModel(
      title: config.title,
      details: details,
      summary: TransactionSummary(
        label: config.summaryLabel,
        value: '${common.totalAmount} ${common.currency}',
      ),
      status: common.displayStatus,
    );
  }
  // Static constants for colors and assets
  static const Color defaultStatusColor = Color(0xFFFF9000);
  static const Color defaultStatusBgColor = Color(0xFFFFFAE0);
  static const String defaultImageAsset = MediaRes.roundedBirrTransactionIcon;

  // Date formatter utility
  static String formatDate(dynamic dateData) {
    final date = dateData is DateTime ? dateData : DateTime.now();
    return DateFormat('MMM dd, yyyy').format(date);
  }

  final String title;
  final List<TransactionDetail> details;
  final TransactionSummary summary;
  final String? imageAsset;
  final String? status;
  final Color? statusColor;
  final Color? statusBgColor;

  /// Extract common data from transaction data
  static CommonData _extractCommonData(Map<String, dynamic> data) {
    final status = data['status'] as String?;
    final displayStatus = (status == 'WAITING') ? 'Unpaid' : (status ?? '');

    return CommonData(
      amount: (data['amount'] as num?)?.toDouble() ?? 0.0,
      billAmount: AppMapper.safeFormattedNumberWithDecimal(data['billAmount']),
      totalAmount:
          AppMapper.safeFormattedNumberWithDecimal(data['totalAmount']),
      currency: data['originalCurrency'] as String? ?? 'ETB',
      displayStatus: displayStatus,
      serviceCharge: (data['serviceCharge'] as num?)?.toDouble() ?? 0.0,
      vat: (data['VAT'] as num?)?.toDouble() ?? 0.0,
      formattedDate: formatDate(data['createdAt']),
      billRefNo: data['billRefNo'] as String? ?? '',
    );
  }

  /// Build transaction details from field definitions
  static List<TransactionDetail> _buildTransactionDetails({
    required Map<String, dynamic> data,
    required CommonData common,
    required List<FieldDefinition> fieldDefinitions,
    List<ConditionalField>? conditionalFields,
  }) {
    // Create merged data map with common fields
    final mergedData = Map<String, dynamic>.from(data);
    mergedData.addAll({
      'formattedDate': common.formattedDate,
      'serviceCharge': common.serviceCharge,
      'vat': common.vat,
      'billRefNo': common.billRefNo,
      'originalCurrency': common.currency,
    });

    // Add details from field definitions
    final details = <TransactionDetail>[];

    // Add main fields
    for (final field in fieldDefinitions) {
      // Special handling for vehicle field
      if (field.dataKey == '_vehicle') {
        final make = data['vehicleMake'] as String? ?? '';
        final model = data['vehicleModel'] as String? ?? '';
        if (make.isNotEmpty || model.isNotEmpty) {
          details.add(
            TransactionDetail(
              label: field.label,
              value: '$make $model'.trim(),
            ),
          );
        }
        continue;
      }

      final value = field.getValue(mergedData, common.currency);
      if (value.isNotEmpty || field.showIfEmpty == true) {
        details.add(TransactionDetail(label: field.label, value: value));
      }
    }

    // Add conditional fields
    if (conditionalFields != null) {
      for (final conditionalField in conditionalFields) {
        if (conditionalField.condition(data, common)) {
          for (final field in conditionalField.fields) {
            final value = field.getValue(mergedData, common.currency);
            if (value.isNotEmpty || field.showIfEmpty == true) {
              details.add(TransactionDetail(label: field.label, value: value));
            }
          }
        }
      }
    }

    return details;
  }

  /// Get transaction configuration based on transaction type
  static TransactionConfig _getTransactionConfig(TransactionType type) {
    // Default to generic if not found
    return _transactionConfigs[type] ??
        _transactionConfigs[TransactionType.generic]!;
  }

  /// Map of transaction types to configurations
  static final Map<TransactionType, TransactionConfig> _transactionConfigs = {
    TransactionType.walletTransfer: TransactionConfig(
      title: 'Wallet to Wallet Transfer',
      summaryLabel: 'Total Amount',
      fieldDefinitions: [
        FieldDefinition(
          label: 'Transaction Type',
          dataKey: '_transactionType',
          defaultValue: 'Wallet to Wallet Transfer',
        ),
        FieldDefinition(label: 'Recipient Name', dataKey: 'recipientName'),
        FieldDefinition(label: 'Recipient Email', dataKey: 'recipientEmail'),
        FieldDefinition(label: 'Recipient Phone', dataKey: 'recipientPhone'),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'amount',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'VAT', dataKey: 'vat', includesCurrency: true),
        FieldDefinition(label: 'Date', dataKey: 'formattedDate'),
      ],
    ),
    TransactionType.bankTransfer: TransactionConfig(
      title: 'Confirm Transfer',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(
          label: 'Transaction Type',
          dataKey: '_transactionType',
          defaultValue: 'Bank Transfer',
        ),
        FieldDefinition(
          label: 'Recipient Name',
          dataKey: 'beneficiaryName',
          formatter: (value, _) => value is String
              ? value.capitalizeFirstofEach
              : value?.toString() ?? '',
        ),
        FieldDefinition(
          label: 'Recipient Account',
          dataKey: 'beneficiaryAccountNo',
        ),
        FieldDefinition(label: 'Bank Name', dataKey: 'bankName'),
        FieldDefinition(label: 'Payment Reference', dataKey: 'billRefNo'),
        FieldDefinition(label: 'Payment Date', dataKey: 'formattedDate'),
        FieldDefinition(
          label: 'Amount In USD',
          dataKey: 'billAmount',
          formatter: (value, _) =>
              '${AppMapper.safeFormattedNumberWithDecimal(value)} USD',
        ),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'VAT', dataKey: 'vat', includesCurrency: true),
      ],
      conditionalFields: [
        ConditionalField(
          condition: (data, common) => common.currency == 'USD',
          fields: [
            FieldDefinition(
              label: 'Amount In ETBT',
              dataKey: 'amountInEtb',
              formatter: (value, _) =>
                  '${AppMapper.safeFormattedNumberWithDecimal(value)} ETB',
            ),
            FieldDefinition(
              label: 'Exchange Rate',
              dataKey: 'exchangeRate',
              formatter: (value, _) => '$value ETB',
            ),
          ],
        ),
      ],
    ),
    TransactionType.carLoan: TransactionConfig(
      title: 'Confirm Car Loan',
      summaryLabel: 'Total Amount',
      fieldDefinitions: [
        FieldDefinition(
          label: 'Vehicle',
          dataKey: '_vehicle',
          defaultValue: 'Vehicle',
        ),
        FieldDefinition(
          label: 'Loan Amount',
          dataKey: 'loanAmount',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Upfront Payment',
          dataKey: 'upfrontPayment',
          formatter: (value, _) => '$value%',
        ),
        FieldDefinition(
          label: 'Upfront Amount',
          dataKey: 'upfrontAmount',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Interest Rate',
          dataKey: 'interestRate',
          formatter: (value, _) => '$value%',
        ),
        FieldDefinition(label: 'Loan Term', dataKey: 'loanTerm'),
        FieldDefinition(
          label: 'Application Fee',
          dataKey: 'applicationFee',
          includesCurrency: true,
        ),
      ],
    ),
    TransactionType.mortgageLoan: TransactionConfig(
      title: 'Confirm Mortgage Loan',
      summaryLabel: 'Total Amount',
      fieldDefinitions: [
        FieldDefinition(label: 'Property Type', dataKey: 'propertyType'),
        FieldDefinition(
          label: 'Property Price',
          dataKey: 'propertyPrice',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Loan Amount',
          dataKey: 'loanAmount',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Upfront Payment',
          dataKey: 'upfrontPayment',
          formatter: (value, _) => '$value%',
        ),
        FieldDefinition(
          label: 'Upfront Amount',
          dataKey: 'upfrontAmount',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Interest Rate',
          dataKey: 'interestRate',
          formatter: (value, _) => '$value%',
        ),
        FieldDefinition(
          label: 'Monthly Payment',
          dataKey: 'monthlyPayment',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Loan Period',
          dataKey: 'loanPeriod',
          formatter: (value, _) => '$value years',
        ),
        FieldDefinition(
          label: 'Application Fee',
          dataKey: 'applicationFee',
          includesCurrency: true,
        ),
      ],
    ),
    TransactionType.billPayment: TransactionConfig(
      title: 'Confirm Bill Payment',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(label: 'Transaction Type', dataKey: 'transactionType'),
        FieldDefinition(label: 'Payment Date', dataKey: 'formattedDate'),
        FieldDefinition(label: 'Bill Reference', dataKey: 'billRefNo'),
        FieldDefinition(
          label: 'Amount in Birr',
          dataKey: 'amountInBirr',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Original Currency',
          dataKey: 'originalCurrency',
        ),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'VAT', dataKey: 'vat', includesCurrency: true),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'amount',
          includesCurrency: true,
        ),
      ],
    ),
    TransactionType.changeToBirr: TransactionConfig(
      title: 'Confirm Change to Birr',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(
          label: 'Transaction Type',
          dataKey: '_transactionType',
          defaultValue: 'Change to Birr',
        ),
        FieldDefinition(label: 'Payment Date', dataKey: 'formattedDate'),
        FieldDefinition(label: 'Reference Number', dataKey: 'billRefNo'),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'amount',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Amount in Birr',
          dataKey: 'amountInBirr',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'VAT', dataKey: 'vat', includesCurrency: true),
      ],
    ),
    TransactionType.utility: TransactionConfig(
      title: 'Confirm Payment',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(label: 'Transaction Type', dataKey: 'transactionType'),
        FieldDefinition(label: 'Customer Name', dataKey: 'customerName'),
        FieldDefinition(label: 'Recipient Name', dataKey: 'recipientName'),
        FieldDefinition(
          label: 'Recipient Account',
          dataKey: 'recipientAccount',
        ),
        FieldDefinition(label: 'Payment Date', dataKey: 'formattedDate'),
        FieldDefinition(label: 'Reference Number', dataKey: 'billRefNo'),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'billAmount',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          formatter: (value, currency) => '$value $currency',
        ),
        FieldDefinition(
          label: 'VAT',
          dataKey: 'vat',
          formatter: (value, currency) => '$value $currency',
        ),
      ],
    ),
    TransactionType.addMoney: TransactionConfig(
      title: 'Confirm Add Money',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(
          label: 'Transaction Type',
          dataKey: '_transactionType',
          defaultValue: 'Add Money',
        ),
        FieldDefinition(label: 'Sender Name', dataKey: 'senderName'),
        FieldDefinition(label: 'Bank Name', dataKey: 'bankName'),
        FieldDefinition(label: 'Account Number', dataKey: 'accountNumber'),
        FieldDefinition(label: 'Payment Reference', dataKey: 'billRefNo'),
        FieldDefinition(label: 'Payment Date', dataKey: 'formattedDate'),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'VAT', dataKey: 'vat', includesCurrency: true),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'amount',
          includesCurrency: true,
        ),
      ],
    ),
    TransactionType.giftPackage: TransactionConfig(
      title: 'Confirm Gift Package Purchase',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(label: 'Customer Name', dataKey: 'customerName'),
        FieldDefinition(label: 'Recipient Name', dataKey: 'recipientName'),
        FieldDefinition(label: 'Recipient Phone', dataKey: 'recipientPhone'),
        FieldDefinition(label: 'Package Name', dataKey: 'packageName'),
        FieldDefinition(
          label: 'Package Price',
          dataKey: 'unitPrice',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'Quantity', dataKey: 'quantity'),
        FieldDefinition(label: 'Super Market', dataKey: 'supermarketName'),
        FieldDefinition(label: 'Order Code', dataKey: 'orderCode'),
        FieldDefinition(label: 'Payment Reference', dataKey: 'billRefNo'),
        FieldDefinition(label: 'Payment Date', dataKey: 'formattedDate'),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'VAT', dataKey: 'vat', includesCurrency: true),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'amount',
          includesCurrency: true,
        ),
      ],
    ),
    TransactionType.merchantPayment: TransactionConfig(
      title: 'Confirm Merchant Payment',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(label: 'Sender Name', dataKey: 'senderName'),
        FieldDefinition(label: 'Merchant', dataKey: 'merchantName'),
        FieldDefinition(label: 'Merchant Code', dataKey: 'merchantCode'),
        FieldDefinition(label: 'Merchant ID', dataKey: 'merchantId'),
        FieldDefinition(label: 'Payment Reference', dataKey: 'billRefNo'),
        FieldDefinition(label: 'Payment Date', dataKey: 'formattedDate'),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'amount',
          includesCurrency: true,
        ),
        FieldDefinition(
          label: 'Service Fee',
          dataKey: 'serviceCharge',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'VAT', dataKey: 'vat', includesCurrency: true),
      ],
    ),
    TransactionType.moneyRequest: TransactionConfig(
      title: 'Confirm Money Request',
      summaryLabel: 'Total',
      fieldDefinitions: [
        FieldDefinition(
          label: 'Transaction Type',
          dataKey: '_transactionType',
          defaultValue: 'Money Request',
        ),
        FieldDefinition(label: 'Requested From', dataKey: 'beneficiaryName'),
        FieldDefinition(label: 'Requested Email', dataKey: 'beneficiaryEmail'),
        FieldDefinition(label: 'Requested Phone', dataKey: 'beneficiaryPhone'),
        FieldDefinition(label: 'Requested By', dataKey: 'senderName'),
        FieldDefinition(
          label: 'Amount',
          dataKey: 'amount',
          includesCurrency: true,
        ),
        FieldDefinition(label: 'Date', dataKey: 'formattedDate'),
        FieldDefinition(label: 'Reason', dataKey: 'reason'),
        FieldDefinition(label: 'Reference Number', dataKey: 'billRefNo'),
      ],
    ),
    TransactionType.generic: TransactionConfig(
      title: 'Confirm Transaction',
      summaryLabel: 'Total',
      useGenericBuilder: true,
      fieldDefinitions: [], // Will use the generic builder for details
    ),
  };
}

/// Data class to hold common transaction data
class CommonData {
  CommonData({
    required this.amount,
    required this.billAmount,
    required this.totalAmount,
    required this.currency,
    required this.displayStatus,
    required this.serviceCharge,
    required this.vat,
    required this.formattedDate,
    required this.billRefNo,
  });
  final double amount;
  final String billAmount;
  final String totalAmount;
  final String currency;
  final String displayStatus;
  final double serviceCharge;
  final double vat;
  final String formattedDate;
  final String billRefNo;
}

/// Configuration class for transaction types
class TransactionConfig {
  TransactionConfig({
    required this.title,
    required this.summaryLabel,
    required this.fieldDefinitions,
    this.conditionalFields,
    this.useGenericBuilder = false,
  });
  final String title;
  final String summaryLabel;
  final List<FieldDefinition> fieldDefinitions;
  final List<ConditionalField>? conditionalFields;
  final bool useGenericBuilder;
}

/// Class for defining conditional fields
class ConditionalField {
  ConditionalField({
    required this.condition,
    required this.fields,
  });
  final bool Function(Map<String, dynamic>, CommonData) condition;
  final List<FieldDefinition> fields;
}
