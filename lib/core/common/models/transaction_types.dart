/// Enum representing different types of transactions
enum TransactionType {
  /// Bank transfer transaction
  bankTransfer('bank_transfer'),

  /// Car loan transaction
  carLoan('car_loan'),

  /// change to birr
  changeToBirr('change_to_birr'),

  /// Bill payment transaction
  billPayment('bill_payment'),

  /// Wallet transfer transaction
  walletTransfer('wallet_transfer'),
  loadToWallet('load_to_wallet'),

  /// Add money to wallet transaction
  addMoney('add_money'),

  /// Mortgage loan transaction
  mortgageLoan('mortgage_loan'),
  utility('utility'),

  /// Gift package transaction
  giftPackage('gift_package'),

  /// Merchant payment transaction
  merchantPayment('merchant_payment'),

  /// Money request transaction
  moneyRequest('money_request'),

  /// Generic transaction type for other cases
  generic('generic');

  const TransactionType(this.value);
  final String value;

  /// Convert enum to string representation for API or display
  String get getValue {
    return value;
  }

  /// Create enum from string value
  static TransactionType fromString(String value) {
    return TransactionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TransactionType.generic,
    );
  }
}
