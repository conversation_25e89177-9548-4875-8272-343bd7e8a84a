import 'package:equatable/equatable.dart';

/// Agora Chat user model with user attributes
class Agora<PERSON><PERSON> extends Equatable {
  const AgoraUser({
    required this.username,
    required this.displayName,
    this.avatarUrl,
    this.email,
    this.phone,
    this.signature,
    this.birth,
    this.gender = 0,
    this.ext,
  });

  /// Agora username/user ID
  final String username;

  /// Display name (nickname)
  final String displayName;

  /// Avatar/profile picture URL
  final String? avatarUrl;

  /// Email address
  final String? email;

  /// Phone number
  final String? phone;

  /// User signature/bio
  final String? signature;

  /// Birth date
  final String? birth;

  /// Gender (0: unknown, 1: male, 2: female)
  final int gender;

  /// Extended information
  final String? ext;

  /// Check if user has avatar
  bool get hasAvatar => avatarUrl?.isNotEmpty == true;

  /// Check if user has email
  bool get hasEmail => email?.isNotEmpty == true;

  /// Check if user has phone
  bool get hasPhone => phone?.isNotEmpty == true;

  /// Get gender string
  String get genderString {
    switch (gender) {
      case 1:
        return 'Male';
      case 2:
        return 'Female';
      default:
        return 'Unknown';
    }
  }

  /// Copy with method
  AgoraUser copyWith({
    String? username,
    String? displayName,
    String? avatarUrl,
    String? email,
    String? phone,
    String? signature,
    String? birth,
    int? gender,
    String? ext,
  }) {
    return AgoraUser(
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      signature: signature ?? this.signature,
      birth: birth ?? this.birth,
      gender: gender ?? this.gender,
      ext: ext ?? this.ext,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'email': email,
      'phone': phone,
      'signature': signature,
      'birth': birth,
      'gender': gender,
      'ext': ext,
    };
  }

  /// Create from JSON
  factory AgoraUser.fromJson(Map<String, dynamic> json) {
    return AgoraUser(
      username: json['username'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      signature: json['signature'] as String?,
      birth: json['birth'] as String?,
      gender: json['gender'] as int? ?? 0,
      ext: json['ext'] as String?,
    );
  }

  @override
  List<Object?> get props => [
        username,
        displayName,
        avatarUrl,
        email,
        phone,
        signature,
        birth,
        gender,
        ext,
      ];

  @override
  String toString() {
    return 'AgoraUser(username: $username, displayName: $displayName, '
        'hasAvatar: $hasAvatar, hasEmail: $hasEmail, hasPhone: $hasPhone)';
  }
}
