import 'package:get/get.dart';

extension StringExtension on String {
  /// Capitalise like: Hello world
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1)}' : '';

  /// Capitalise like: Hello world from dart code

  String get inCaps =>
      isNotEmpty ? '${this[0].toUpperCase()}${substring(1)}' : '';

  /// Capitalise like: HELLO WORLD FROM DART CODES

  String get allInCaps => toUpperCase();

  /// Hello World From Dart Codes
  String get capitalizeFirstofEach =>
      split(" ").map((str) => str.capitalize).join(" ");
}
