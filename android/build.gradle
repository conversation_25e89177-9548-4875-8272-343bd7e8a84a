allprojects {
    repositories {
        google()
        mavenCentral()
        // ComplyCube SDK repository
        maven { url 'https://jitpack.io' }
    }
    configurations.all{
        exclude group:'io.agora.rtc', module: 'full-screen-sharing'

        // Force compatible Compose versions
        resolutionStrategy {
            force 'androidx.compose.animation:animation-core:1.6.1'
            force 'androidx.compose.material:material:1.6.1'
            force 'androidx.compose.ui:ui:1.6.1'
            force 'androidx.compose.animation:animation:1.6.1'
        }
    }

    tasks.withType(JavaCompile) {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
