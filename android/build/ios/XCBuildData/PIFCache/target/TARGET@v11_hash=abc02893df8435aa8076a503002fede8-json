{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981319c2d0fe09ac4826db4bd332a7c5aa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f60950b170eb7865be78feaa6faf3380", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f60950b170eb7865be78feaa6faf3380", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac9e6828aab71095d99afbb57b91ab4b", "guid": "bfdfe7dc352907fc980b868725387e98be2c41c899d1273e6d632a56ba8d7e17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0fbc79d14c08d407d145cc022eb7a5d", "guid": "bfdfe7dc352907fc980b868725387e9851643e713658d14d29d6829782cbb4b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebfc30826e0c9ad7db912028240fbb9c", "guid": "bfdfe7dc352907fc980b868725387e9817ba561fb18b74dd35e69503796de8d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a772d66530807fc3049a09911667f4c", "guid": "bfdfe7dc352907fc980b868725387e98e24f185acfcc7a37dc0388418630035d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812dc74e56b7e210f234ae98d3b10b2c0", "guid": "bfdfe7dc352907fc980b868725387e986fb04a840407641caacced9b7afd0e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98040abaf67f81cf7ab380e7b73fa1424e", "guid": "bfdfe7dc352907fc980b868725387e989e7a155999504dc0a9f264b88821207c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f61ab0da968b88a4f4e9bcc32d5ce3", "guid": "bfdfe7dc352907fc980b868725387e986e93e86c54e81b4099ac32b2f80331e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a0c489130e5e155c8a927335deca2bf", "guid": "bfdfe7dc352907fc980b868725387e9892de17e91461b157d2ff868c71245a5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b193db38064cb630a9e9939f2389b45", "guid": "bfdfe7dc352907fc980b868725387e98ccc08afea492b4a1e584294bb007c896", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ec7bf8385c415292b9bd063feb297ef", "guid": "bfdfe7dc352907fc980b868725387e983521b7cd1f6a9460255380c2e8bd3a76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f472c120ba041b0f19b8655d7e87f7", "guid": "bfdfe7dc352907fc980b868725387e9806f3c8f52df7d5d86b35f27ec804833b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aa9d56a8a1770272197974e377de2fb", "guid": "bfdfe7dc352907fc980b868725387e980481fd222799c6dbbb547e04619399d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835858c1617c7369381c8820abd7273ef", "guid": "bfdfe7dc352907fc980b868725387e986628c4eb4f0c01134872824bd0b57754", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8047fee15bd983a81b65240602019d", "guid": "bfdfe7dc352907fc980b868725387e981ffda07ad10fa61f9d8343f9e5802258", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98386c75fc4ab511456a30a9fe880b8239", "guid": "bfdfe7dc352907fc980b868725387e9884a940986487d110356e4ea121ddad2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa28fba9bde6a19da081717235e283ab", "guid": "bfdfe7dc352907fc980b868725387e98a035677a5f5af6b2626b66c3934bc772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981902c7b435db3076d6441bb1c2d95d3b", "guid": "bfdfe7dc352907fc980b868725387e980133e3458c89bb95849d196600f559e4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98130baa9b6b1b41e65be840e1bb74da88", "guid": "bfdfe7dc352907fc980b868725387e9869a01b04319795d3ba134280e5b3aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e31098000c3bc3f56e8758b3d77af1", "guid": "bfdfe7dc352907fc980b868725387e9887a3f9fbc3dc4cf61ce3bfd34cb48fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b146ebba73c1faf196d5600499bfb8", "guid": "bfdfe7dc352907fc980b868725387e985af5e7f6beaafbbd4b695fc9b31294b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a09963067574a6602a04f17a579f21a", "guid": "bfdfe7dc352907fc980b868725387e98fd5c5c15ff3e26167ba0a16259e0280d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5a6c7df6bec45f7a640434696811ea8", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812b4f10f7f15d5e0d6e5d050708c0946", "guid": "bfdfe7dc352907fc980b868725387e98e69ee1ca0e2fa73c4851dfd4917080c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e0588e621e8c18a36ebb59ed744374", "guid": "bfdfe7dc352907fc980b868725387e98d441388c343649dace73588215b70832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986154b31a184b9fa8dc31bd573dd48397", "guid": "bfdfe7dc352907fc980b868725387e98caaf5201fdfbf16ea5aee50b6acc5e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c83628af1ce703a51641a327cd876f", "guid": "bfdfe7dc352907fc980b868725387e98bb6368b1c4ab73b09a96040d1bbd3697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8fb39eeca83e04deb4b96369fcc0550", "guid": "bfdfe7dc352907fc980b868725387e984069ad8f4dd463849cd55f546f840132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846efaa0863d52abd866e6b814c010021", "guid": "bfdfe7dc352907fc980b868725387e98d9dcbf389b3d1b5a507d5d60a21b0c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1d92a407a60b004691141793dbcb96", "guid": "bfdfe7dc352907fc980b868725387e98a95cab8f4d69c86ff793564af7099191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b742833db3aafeaaea5b9cbf150545b3", "guid": "bfdfe7dc352907fc980b868725387e98c60c36d6b388d64b429d0a4e5aaf86dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3096128a5c44f3eb824c4bad5d2ece2", "guid": "bfdfe7dc352907fc980b868725387e98888657137765bca507e1b4362cfde572"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}