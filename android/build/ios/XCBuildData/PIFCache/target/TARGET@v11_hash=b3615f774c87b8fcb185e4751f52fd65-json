{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98133908c7d58872280b07a02969e70bd1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98827a315825a3458d4c7898aa7c619cfa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98498234bbc00dc2bc1ed67b768663188c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b640fe3f70e62f60ac605d4f448d0da9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98498234bbc00dc2bc1ed67b768663188c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850e5ef5d603e7a89e6fd5ebb9bbe0702", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d53102415d95d23bdb472672c13fb195", "guid": "bfdfe7dc352907fc980b868725387e98e1ed78706ab01dcdc9576fc1e78f6f88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896713d5699d84c410e0ebabd55fa2876", "guid": "bfdfe7dc352907fc980b868725387e989b1e5bd82772f033d9dfe0ba04d328c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efc2d58d14ee9e9db8ae78d488034190", "guid": "bfdfe7dc352907fc980b868725387e98730fb36fefbe6073767679140d43a511", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984965c7d0d0c677eb9f208f7ea0aa7043", "guid": "bfdfe7dc352907fc980b868725387e9877fcf3977277ead494f2d9177502f3c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f57d4f72e309012fb959ae2c0b34446e", "guid": "bfdfe7dc352907fc980b868725387e984a8ce49f90249a1c76d1175bcaac4660", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc8148ce08fe21fc9928258f2e99b0e", "guid": "bfdfe7dc352907fc980b868725387e98fe571716edd76f32f39dd0fcf622c3ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981352c36d4c37fe6a3069e052371e2f5a", "guid": "bfdfe7dc352907fc980b868725387e9823e54d0635576a73cd5b118f335cdb9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6268133abb23372582f44e7e397fc17", "guid": "bfdfe7dc352907fc980b868725387e98ad92876f11b9e967f4efb3126a45fcf7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832219129a51294758319cae87b56ea4f", "guid": "bfdfe7dc352907fc980b868725387e987a8809ee2196d9e9104eff0d130dce4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829e45781408297d6034f69945706732b", "guid": "bfdfe7dc352907fc980b868725387e989edf6620bf0cc92cf19e3d1b8a10ae5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882daf9cbfe857f3310ba7b1c9e0e0bc6", "guid": "bfdfe7dc352907fc980b868725387e989a4655507c8215a7f9cf6a8f74cb6ca0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f056f42ca57842b1e294647d5930576", "guid": "bfdfe7dc352907fc980b868725387e989670e914eb5606a6a0dab219f213c44c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a456613089246a6bab31f1f98adb3639", "guid": "bfdfe7dc352907fc980b868725387e988681c8a7582c5c3582b25dd68395cbfe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbf0750d79c26035c772be1c6efc376f", "guid": "bfdfe7dc352907fc980b868725387e9834517db5dbd73bedf3f13f0fe1b2cf33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859b8185a9a7f1c69d7b17cbfd7e6aba8", "guid": "bfdfe7dc352907fc980b868725387e989e8361d3fef50d8040c0495a6f5ed8ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b931c65f81581d86cb7a592bd05f7840", "guid": "bfdfe7dc352907fc980b868725387e989ceacd3bbe77e15936647e4c1d822101", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f65f38cd0ad99b77ef51c0dd6fc4268", "guid": "bfdfe7dc352907fc980b868725387e98d785dec5f572159348aa6cc9dc7cce48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acfa855f1d4adf79c767cd8570d8811c", "guid": "bfdfe7dc352907fc980b868725387e98644e0a29f7da9459c156e4a8f90c15ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980170dd1dc2755b6107513881224e7b43", "guid": "bfdfe7dc352907fc980b868725387e98ee667953acb1c5bec688017a19fa739c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef05ce09b665f4eee8b1794718e32de9", "guid": "bfdfe7dc352907fc980b868725387e983cb9a981cbe0101941f7392fdda25dc8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cbc0b552388826c8117d69e705a7028", "guid": "bfdfe7dc352907fc980b868725387e980c419838cd279803a5e57b84c7044054", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1c019adfd9f978ca430070844f3cc49", "guid": "bfdfe7dc352907fc980b868725387e988b482b16c1f99c50af8c886e84e4bba1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ae987ae4d16ea971c88eb01a5b8391a", "guid": "bfdfe7dc352907fc980b868725387e98e7b2fa15132355a8abbcbec18385ccab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce7776fc9423609ff7b64c1e4db57e15", "guid": "bfdfe7dc352907fc980b868725387e987d579e8ac1dd86066dd97f006cf85cca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4cad3c9e7e21aeb5b9982714576ec5", "guid": "bfdfe7dc352907fc980b868725387e98288554840d259937fa8ea267b95a3fd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c689dc8c5700c28b20661a67c6845f6c", "guid": "bfdfe7dc352907fc980b868725387e9872b0f207c180ba9666777f155e7c2745", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f88d36439c7f419921d701fbae526d2", "guid": "bfdfe7dc352907fc980b868725387e98b88d3ec8057338fb637a0b4c9f317504", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e580be82d91851ff76be738370a2c6", "guid": "bfdfe7dc352907fc980b868725387e983d639c2f197107a8958fedd7c162722a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c7c4a6c82560fb6fe3f91ec4f23802", "guid": "bfdfe7dc352907fc980b868725387e98f80c826c87cb7e87d154c304224147c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d9064264964e7898e5238ca586c154d", "guid": "bfdfe7dc352907fc980b868725387e989864bc03d44a6a91ac1898b72775feac", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9875f12ec8f4685f776fb322cbef353ce9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98565533c18eacad973a313c6ea28cdcc7", "guid": "bfdfe7dc352907fc980b868725387e98e013791acd0e0b54f6da78c1d15b71cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba3f2a8133466d9e73e9a3ee132214ae", "guid": "bfdfe7dc352907fc980b868725387e9829922ec2ae5c4827cdb1eb1aa6a98f5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367f410fe21d51100f1b6247401fbdaa", "guid": "bfdfe7dc352907fc980b868725387e9886f123870f74af4105f36321bb2edc73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efaf233c4ac3d48c91a1b195d744a602", "guid": "bfdfe7dc352907fc980b868725387e988546c5393cb3a9f897ecdd5f082e1185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9e87e5262a8bbef21d0c3d8b3c4aab", "guid": "bfdfe7dc352907fc980b868725387e985ff7e4e639a2db448d5391f61723a1b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dea6b5dd312c46470fd7d901624590d", "guid": "bfdfe7dc352907fc980b868725387e98c3ebcede6a619f2dd6917327b0d38918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2862d23b1f82395f25d219454a9174", "guid": "bfdfe7dc352907fc980b868725387e982279834710e084e86b0104a16ee7c6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6e0bec3b73a97a20c7e69b204122821", "guid": "bfdfe7dc352907fc980b868725387e9881edd4ebbe1617e9c1ff42cb84541197"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803adcad6d00967ea5c805c807b6cafef", "guid": "bfdfe7dc352907fc980b868725387e980402856db76c7759d66d038df108d1ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcbec8e744a475fa17f1351dfb7a08fd", "guid": "bfdfe7dc352907fc980b868725387e985ad4dfa4a1d080b1e12302b55b8b06fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fc034d814a6f94583be61ef2aa64faf", "guid": "bfdfe7dc352907fc980b868725387e98884b2e981ff1e9cd0af7afd17d6dcad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98120053a139c2afdcce394e62a6bf6ad3", "guid": "bfdfe7dc352907fc980b868725387e987782e5576e9313c1b5b6abfdb5529bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0bce5de60354681d3cb2fca9742df52", "guid": "bfdfe7dc352907fc980b868725387e9860e56d73f9a677ec64766953e2e20f7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871338846222e3ce743ad52537ec34344", "guid": "bfdfe7dc352907fc980b868725387e9835ea4617d48cf0977935cdbc0ad1f89a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a854ec1468e9d48425b8bb48e77e955c", "guid": "bfdfe7dc352907fc980b868725387e98d6e8a41e6815510bba2eb03fa78bb5fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d2c54db52376183dda6ac10864cc1fd", "guid": "bfdfe7dc352907fc980b868725387e98efd41d2d1508a0648c86d7c14c09a83a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988137d1a8afc5df26cff1ee3a53be2570", "guid": "bfdfe7dc352907fc980b868725387e98680f411f0be02b6395b91bdf4fa243af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda34eeb54413dd7df3b4951c76c50bb", "guid": "bfdfe7dc352907fc980b868725387e980ed41bb2e056910d76192917609f9b49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880b89de080f87b884c45ebc0435c9ff5", "guid": "bfdfe7dc352907fc980b868725387e98312541b2f9285356b7d4d3e53f1f580d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98886cb5ec23c9098c14f053ccad0b97fc", "guid": "bfdfe7dc352907fc980b868725387e98c526d25086f2f58fc8982e10de64dc43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a58a3f0e5d70c3ddb01397a23b4f009", "guid": "bfdfe7dc352907fc980b868725387e984798cc7926fb1b75f85b3bfb16557c64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c8599dfb1a2404e2cf33ba81e885574", "guid": "bfdfe7dc352907fc980b868725387e9868cce02486d0ef00b4c49935f908dd4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d850a953680b4c9de83b5081866eabc7", "guid": "bfdfe7dc352907fc980b868725387e98bcbea947ef52d3d303e8371dcd6386e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98429d10306fb7f1c84e7ceb4a11150fc0", "guid": "bfdfe7dc352907fc980b868725387e98e5153bb36d88e9ac7611d3befca98583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d0eea3990fe1f87a2231ea5df91f12e", "guid": "bfdfe7dc352907fc980b868725387e9883162af61d657c19555bda8d7ab990c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988538b738864830f6338b78f126fbfd63", "guid": "bfdfe7dc352907fc980b868725387e9810af6587cf504e9c7c1feea7fa78faf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd09369c8fc1b049be49ec549efc01a6", "guid": "bfdfe7dc352907fc980b868725387e98036197182ef62ddaf3d91a0594ded992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98125371da59d9354799acbda119a97898", "guid": "bfdfe7dc352907fc980b868725387e983849ff6ef0eb434af43cc9efaf9ca16d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823cf90c099fa85287cf35d008f2f7ae7", "guid": "bfdfe7dc352907fc980b868725387e98dd2cb718199b8407693df92021b45d4d"}], "guid": "bfdfe7dc352907fc980b868725387e98d783f88078fd75699d26d414036693eb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e983d91a55dfd998e424ed3250934d962f7"}], "guid": "bfdfe7dc352907fc980b868725387e98b34f7762cbcc79e8da1af5e463ac47ac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ee8088d38b296314c35845bc4b7ec901", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e9882b9921aa6f56b09dc5988d09c7af378", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}