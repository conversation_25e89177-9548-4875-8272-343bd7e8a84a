{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b35b7f2f0db03b5045a286e0d6914071", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bebb0d560f8572c19e40b8bb4fd28613", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852f295f8e083ea888582129893f8f208", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988033eb3d495c02c85ab248cb5632ff68", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9852f295f8e083ea888582129893f8f208", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c80bf2e908b3e0cfc26c7c40b42ddfdf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980837589555fa2f23b0acbe3df7bbad25", "guid": "bfdfe7dc352907fc980b868725387e98ab73098d7a68f0c88bc72208bd71aa16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98326384ef76267a315d3222ba5ab2e5e2", "guid": "bfdfe7dc352907fc980b868725387e988112ac21c21c09825d6318c202d13391"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a50a192d04a24553f348c65c84a2e10a", "guid": "bfdfe7dc352907fc980b868725387e98cf3e777a4e61d5f16eb7bf2e21fe79db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c352ce8c1312241e7409338bc4ac7a82", "guid": "bfdfe7dc352907fc980b868725387e9823e92f368df3222c5ea8dd0824c9e94f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ece0ce4416c0d8b9fc00ddb09a9f939", "guid": "bfdfe7dc352907fc980b868725387e98cff39e1dc5e1ff27b6b890d310427915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df169ff27715401a5a0f50d18c53636d", "guid": "bfdfe7dc352907fc980b868725387e98425cf85e307535412d41db09a342e7a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a27bea42aa469b8ed8d309773e28348", "guid": "bfdfe7dc352907fc980b868725387e9836c7095242c532e0c2d3d14a7ce4df58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e44d65aa7dfcac4ad5baff4c87dde4", "guid": "bfdfe7dc352907fc980b868725387e98e8395a5a023765ca6188821af3f82063", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98554c98e7a37c5ce9349ffe19746370ea", "guid": "bfdfe7dc352907fc980b868725387e98f4b773bed2dd1820c5a09bbc3c995661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379704d1cb61fbfb6e8180bd94b456a4", "guid": "bfdfe7dc352907fc980b868725387e98bc3df6a49ab09a100ebd378a54f1aa9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d7b35b212881a67dde7674a9de7c9b", "guid": "bfdfe7dc352907fc980b868725387e98612609ccfc67c0fc16cdc7520ff159c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e972e55184ef78801c3ca9aae253644", "guid": "bfdfe7dc352907fc980b868725387e98e21d4797257823c5fb6fe14831c83981"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814803766619eac66212c1861770dc50d", "guid": "bfdfe7dc352907fc980b868725387e987dbbf9aa634a5a862d684d46f691691f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850d2d5e54cad254286c7d8c21e11c45a", "guid": "bfdfe7dc352907fc980b868725387e982ad249624cccfc63ff269e44c1fe6bf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841389340ecaf4db363da279c4f12f2dd", "guid": "bfdfe7dc352907fc980b868725387e98ea8badbf52ed495cc169f0a13403d540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e496eaaebd1defd462309c40e149a013", "guid": "bfdfe7dc352907fc980b868725387e984c75876cc32ae62330062dc189484ce7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d084be8b5700575f45394e92f3d3588", "guid": "bfdfe7dc352907fc980b868725387e981c349b5a191bf96cf6d59266becfbd36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858455bdf563e58c9c4042361b9b3d7f2", "guid": "bfdfe7dc352907fc980b868725387e98c3fbac2f7b4663a9843cfcdebc7b4019"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800315b8984a918d4f01ae7c7c0ab87cb", "guid": "bfdfe7dc352907fc980b868725387e9843939ae0815e7dba677277a7a14a58fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797f4e0cbdabce46e91085059be47191", "guid": "bfdfe7dc352907fc980b868725387e98e0dafaa4ba63d57bc59bb07a10e49d10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d3a94057cf5c1edb62c25c1acc3b4c3", "guid": "bfdfe7dc352907fc980b868725387e9803f49db6fdca3f34e2a2203fb64e48a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf78505ae687e4a947e62eea9f21ac4e", "guid": "bfdfe7dc352907fc980b868725387e9886f19e91f357c05132e997f559009ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898dff90e223f4ee1a69d36dfabba6a02", "guid": "bfdfe7dc352907fc980b868725387e98f88773caa7157fb61e290295a8ce830f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98161bf92c811cd760d2659cca10ab178b", "guid": "bfdfe7dc352907fc980b868725387e98293613cc24e7a1af6a8a946612f9c6e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de4c4cfe8480c5671dfe817ac3b990c2", "guid": "bfdfe7dc352907fc980b868725387e98331741152c71244bb98dffd048ecea89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf1024e6ce43c5a2a9f5b3eee70293ff", "guid": "bfdfe7dc352907fc980b868725387e9879ff87fd4009e427c1bbe21ee0cbc6e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d5b3936a35be8da081425c459ea35b", "guid": "bfdfe7dc352907fc980b868725387e98ccd5aa6986a6b407662b51391619a0da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def776976ebfc569ae8a8de13bc87c5c", "guid": "bfdfe7dc352907fc980b868725387e9858f36c6c53a9a8a2d13c356e0d8d8773"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f6479d36db92766e0469999c4fd95a0", "guid": "bfdfe7dc352907fc980b868725387e989750af964b06056ec5f34257ea9566a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1381ec3e5c88683610f718b51cc454e", "guid": "bfdfe7dc352907fc980b868725387e980abd36faaecddc4b9661e0468b936e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba160cfe97954a98afb1bbc8e4a728b1", "guid": "bfdfe7dc352907fc980b868725387e98392e051ca375d1e460e8caa6f7cf2273"}], "guid": "bfdfe7dc352907fc980b868725387e985d5c5530dfea41e1856448df3bef6da6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9894435cad794476ec3c4b2835996932b9", "guid": "bfdfe7dc352907fc980b868725387e9862e116aa6e7c0e5d6da3807c07a1f8c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acea18b03d726a6e5e410e485bf68577", "guid": "bfdfe7dc352907fc980b868725387e98c370324d01ec3242502b1be1b5740b72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2668468e5151d81fbeffc1f63860d8c", "guid": "bfdfe7dc352907fc980b868725387e98558f283bbeb8741f2213675b7582240a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad54f728983f7d366e33851758413334", "guid": "bfdfe7dc352907fc980b868725387e98e10efdaa5617f6810ecc59a38a644c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fea4ba3b76790b341095833deb7b41d", "guid": "bfdfe7dc352907fc980b868725387e98ec15f55e48b75ff513554cc868d84a4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f30c7d350dbffc003aef5aeb9cf636", "guid": "bfdfe7dc352907fc980b868725387e98c1724bed2eedb09bd9c58e40c00728d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d38263c50da6b60b6790dce6a47e26e", "guid": "bfdfe7dc352907fc980b868725387e987451e480aeadae9b60575e9cfd318f73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980db90f2a510e61ae39872cdbb565d2b8", "guid": "bfdfe7dc352907fc980b868725387e986fc3bc32e262cdaea6aefeeac9638cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba3edd66c0f78dc127f564a5becfac1e", "guid": "bfdfe7dc352907fc980b868725387e98f576e2b7d86c8721094aeeaa84fecc18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f6e97a857c8d8b69f021124878aa99d", "guid": "bfdfe7dc352907fc980b868725387e981245169af14438c438a312a8b3cf2e1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5cd3b5c3e2e4f713c82ba36090e7459", "guid": "bfdfe7dc352907fc980b868725387e982616fb392f3bc1152a27a7e30bf01e86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982faf84386e6c4b16e74a46dcc24eeda6", "guid": "bfdfe7dc352907fc980b868725387e985e82e386d32cf5c9eef9bbed19497b75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e717e5f5cd74340b3ba868a1dc4483", "guid": "bfdfe7dc352907fc980b868725387e98138cfa058284dad66b9309d8d9e61f37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4e0599b9c64aaa8dd8468b3f13d731", "guid": "bfdfe7dc352907fc980b868725387e98d52dd2e5e04d96f67aef1d7b362375a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eb38c920a20d08b79c181ad3da49218", "guid": "bfdfe7dc352907fc980b868725387e9810b0b13893bb81f0c67c06f46dfe9b21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8959252d6331f205a6318b9f8ee71df", "guid": "bfdfe7dc352907fc980b868725387e9819a75a0333eb253c402a6a70cc59e407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f734aba2a864cdad976716d7866a88fd", "guid": "bfdfe7dc352907fc980b868725387e98ec5bf0dbbdad615ad58b548e129cb510"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aff7f30cd83b3de2d48db56796fe8bdf", "guid": "bfdfe7dc352907fc980b868725387e9841c587d2a0f071f031133bd170039cbe"}], "guid": "bfdfe7dc352907fc980b868725387e989c20ac7af6c35a8af32da3d184c3c93f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e9853d9f6a4144e36cbb711180c86773ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eba02be3f7c3de08681eb045e5915c67", "guid": "bfdfe7dc352907fc980b868725387e986c7e2433ef949e029e58d7bd1f5ae937"}], "guid": "bfdfe7dc352907fc980b868725387e9844d79d4757785dd1b93d1a7a6acb1cc6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980748d56528ec8e9c030d29d8b974ca96", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98038a9cd7c324db6607be5252e226fdd4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}