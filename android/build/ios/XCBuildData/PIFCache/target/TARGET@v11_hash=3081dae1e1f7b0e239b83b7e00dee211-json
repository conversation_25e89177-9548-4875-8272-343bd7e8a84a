{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98369efedfe6e9be36a780dbcc38edb7e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd6b8215f3d180d549383ca05f26c466", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd6b8215f3d180d549383ca05f26c466", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d394ab3f871564a400a37dcc5953cfea", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc588b80a31053cc65a0f03a0c909d1", "guid": "bfdfe7dc352907fc980b868725387e9855236db105a1790362e0f3f85da2820e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea778d01ca8a2f234105f83c04892e42", "guid": "bfdfe7dc352907fc980b868725387e988114bf115703b57c8ae21d5409dd19e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3d66e09df142ca6ccc6c92cce14bc1", "guid": "bfdfe7dc352907fc980b868725387e98b588a7e8d8ddd4478d80982232420613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98055b83bca94a6681480222a90d4c577b", "guid": "bfdfe7dc352907fc980b868725387e98c25bd75abc455f60626b1b6d2e12965f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41dabd6f0c0f69d87f0127167b70884", "guid": "bfdfe7dc352907fc980b868725387e9886bb1e038f2ec0e9b0706aa88efa09e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2d012e671e9c2f11f49904c581caf0", "guid": "bfdfe7dc352907fc980b868725387e9867d7f020d92c0259475431094f026daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873377decad7e8e9f407c7dedb0e9780d", "guid": "bfdfe7dc352907fc980b868725387e983cbfbd91e9e8b5ed232e4a3e97ee6ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baeaa86e197a392dbe8edc62a1772fcf", "guid": "bfdfe7dc352907fc980b868725387e985677e626286185176d39b4c7a3131d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889f29b59cc435446d2672e8b66442f75", "guid": "bfdfe7dc352907fc980b868725387e98d470e112a689c44d32d123ec1e9a49dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989543e475dfc262bf1fb570bdd3a5cc1e", "guid": "bfdfe7dc352907fc980b868725387e98ca6c1fe797300662ce54309c21e1f90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ba4cf2e68d55de25d873bf5aaf11661", "guid": "bfdfe7dc352907fc980b868725387e98e2db67651016b0e5a242bf079bcd1b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c945d582969d7daa5ffba108e87439", "guid": "bfdfe7dc352907fc980b868725387e9893f552456b99ece4b3b598b98c2d0951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ccd8bcc303c748dd8a4a8a6107fe394", "guid": "bfdfe7dc352907fc980b868725387e98e64e2f7a87cc703185c2211316deb4fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a669b97140abc78e3f165efd15a9da48", "guid": "bfdfe7dc352907fc980b868725387e98831f9cc115b07d97e9aba4500b47d900"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac33f5e3a181a91febe32477e31c900a", "guid": "bfdfe7dc352907fc980b868725387e9813af1cf6f1aa063237c27f62b8da4f4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b7745736c7c5ddf56d210350ea97e1", "guid": "bfdfe7dc352907fc980b868725387e986ed5364d2e170f445d04136ca20dd611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c1949c2744cbd3913c8b6530ed65af2", "guid": "bfdfe7dc352907fc980b868725387e98e480306e9ebc93b19dbf732f19c8e307"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983670cad1a6201712dfc6442f8d7b749b", "guid": "bfdfe7dc352907fc980b868725387e98a0b529fc765425975daa2152012b3d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a0691d9183048d92fb19ee3afb4e3b", "guid": "bfdfe7dc352907fc980b868725387e988ce54280e8249a8ef34a9f874fa2a3d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987562447cad41043d184d90675bf2b66e", "guid": "bfdfe7dc352907fc980b868725387e98c4eb53ccf283dd9fadcbc4ab328696ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2f53ec0b0e74ea1dad6393848d88cbe", "guid": "bfdfe7dc352907fc980b868725387e988c7b5a9b08093316782f2062a6bb914d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eba65c4d07cf883a3b14bd291623107", "guid": "bfdfe7dc352907fc980b868725387e98023d74f18fb22dba3fb8cd08619adb5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d344566757499b90f263603195292e", "guid": "bfdfe7dc352907fc980b868725387e9845ac40f0b799c7c7d7c8542c2caa56cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e4bbb0f282e380b60632e8adb584fbf", "guid": "bfdfe7dc352907fc980b868725387e98c518fd94f66a19215b72cded5acabcb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98514043af8b470e8d51ab1bf8da57c236", "guid": "bfdfe7dc352907fc980b868725387e9865436ccd5e5292ab3eb9a2c726d4fd55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847b3e3692c80defd455a61213075a619", "guid": "bfdfe7dc352907fc980b868725387e98c9c0f8cc39cd1f4c2791c1b6bcd95b24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846bc384132366a2a1039cb77a233891f", "guid": "bfdfe7dc352907fc980b868725387e98222e67f8fb32506a7edcf8a0ac719b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eeb9e8437f5803d178a131b9f2fd9dd", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc6c2c4182453853b57049f29b4c104b", "guid": "bfdfe7dc352907fc980b868725387e9894b1e9a9c15f1d4d6dab730cad9a7356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98355d12813fe72d1e1adebc353af564cb", "guid": "bfdfe7dc352907fc980b868725387e981eed0494494d3067db554c50722a6670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98554d0f8a4d7a1bf6055c39e2751951ed", "guid": "bfdfe7dc352907fc980b868725387e988b7cd12bea006b45c3dbb4e16996e9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf94b0003adc6baee873110e5a8a2d49", "guid": "bfdfe7dc352907fc980b868725387e98d7495c6870d2ecefef04b9c19bbca9b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f8ff0cbe3385b03e36d5d31195f6be", "guid": "bfdfe7dc352907fc980b868725387e983bd56d9a4345e817c88f1f030a933016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd8d9c9888801f5a899c1efa7b95e53", "guid": "bfdfe7dc352907fc980b868725387e98faeeab2baa047c58c10f40717f073aab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b784ae21df856a0cd6731582b308ec", "guid": "bfdfe7dc352907fc980b868725387e98cea48adeb08a5939b6bef50534e25be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dacf20503bcf0a076d6482eb0e1b53f8", "guid": "bfdfe7dc352907fc980b868725387e984c86b1becfedef31481dcbad2b17a5c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b14759fe20e30a4b289781f0b694b01", "guid": "bfdfe7dc352907fc980b868725387e98da3e64c4fb8555d91305261dc4ff94d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98631061b46cb036388fa36d247d65d2db", "guid": "bfdfe7dc352907fc980b868725387e98bda20fd5eba9b97c8a836079351c8cd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac6471dc6bb17302a129b606aff09ae", "guid": "bfdfe7dc352907fc980b868725387e9806a749e93be9d437dd5f0ddf38711a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847637e0c1aa8b98a75910fa38687677d", "guid": "bfdfe7dc352907fc980b868725387e98fdb2b1d48c92bc84d4a594cf3335c801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee7997d2631fc335c6af6acca68cbea7", "guid": "bfdfe7dc352907fc980b868725387e98e15c16685b281da13549a9fa2a0b8813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f378a1d386d23a589b44137136e19ae6", "guid": "bfdfe7dc352907fc980b868725387e98cc497b3c5b4a04b925785d634ff1e14c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847876799cc803a530490eb00a478ace0", "guid": "bfdfe7dc352907fc980b868725387e981ba2cec9e73183ea8decd900c0ab5b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98285f0ff64e202c20077dc7c36aa07707", "guid": "bfdfe7dc352907fc980b868725387e9800260b0ff6bd074c8bdbb7a8c7ac0674"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872f8e2c92ec978b245d44790b732fb02", "guid": "bfdfe7dc352907fc980b868725387e98fe3de5bd3d36a17e0d9aa10a554a91b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ace7eb710b4b3a694e72ac9912372002", "guid": "bfdfe7dc352907fc980b868725387e986d32a954ad28f8654b283264f8000ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a7b57f7cb0b6ab556e85f05527eb32", "guid": "bfdfe7dc352907fc980b868725387e98235040965da858be0295f7b555c2e118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d35be87c5e357d52acbdae7ec56adc73", "guid": "bfdfe7dc352907fc980b868725387e982fbf2a8321bfaa1dd406916c3062e75e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd60ca8e235a7dae650685096a6456e", "guid": "bfdfe7dc352907fc980b868725387e98d2cc5d5d922868210f4715e81b020063"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98219d8264b750be0294ebc6e70ef08b98", "guid": "bfdfe7dc352907fc980b868725387e9817d42fd3684b39a67bed167a984be3b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801e43526d0058f57367eae7435499c6c", "guid": "bfdfe7dc352907fc980b868725387e982186516c38e2b047b8d85b9e3abec8f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ad38bb4c2cbd74e40f39e6217586a5e", "guid": "bfdfe7dc352907fc980b868725387e983ba67aae03bec559656bb0fcb9a543ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98000694e9475482cfde51742cc73b79f1", "guid": "bfdfe7dc352907fc980b868725387e981f15a3129063fc818f963bbd86131b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3ed04b48a00d1271894ab9987f2913d", "guid": "bfdfe7dc352907fc980b868725387e980c04b925abe5a7dbcd2bce08777fa56a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adba5f6453d93dfe747d4173a75a2ce4", "guid": "bfdfe7dc352907fc980b868725387e988a8775245f5f0001bf1ff488c004e6c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2cbfe54c766b7dab6a07e913d6a9f75", "guid": "bfdfe7dc352907fc980b868725387e98b980576b5b2e5f46ce92bf1270d8250c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5a1365f33a2091aa38b34769e2d421c", "guid": "bfdfe7dc352907fc980b868725387e982d2886a197ccb6c2dac6a56d578acaa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4dcc14bd908368fb626d0d89faa85b", "guid": "bfdfe7dc352907fc980b868725387e984e73d88ae412b41a6f15245d2d5c16ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b1d8f9806c4e9338b132be105a0a1b5", "guid": "bfdfe7dc352907fc980b868725387e984eeeaa430cd3c651fec8c5bd40d762e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98879bf7fb9a85266718eebc89e0ca031f", "guid": "bfdfe7dc352907fc980b868725387e984921e40e36d4c16aea97616d7b65ec84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf0c77f9dd7c1c7e93ef0e858326abbb", "guid": "bfdfe7dc352907fc980b868725387e9802fa17a9388f990ed77caff2a3799c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98811697a2b2ed78921b600b030cf0b579", "guid": "bfdfe7dc352907fc980b868725387e98c09a7ffa31156a82f74af06abc9ab20a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b324d90a55e9900f36badc39a4fb9543", "guid": "bfdfe7dc352907fc980b868725387e986531517c4dbf0bee7d8fc09647df214f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b381e17f69e2ac13dbfcedb472f5375", "guid": "bfdfe7dc352907fc980b868725387e98b56566c8310d63c0e874e3feda6f60cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddfb54d60f424d8279bfeb7e6704e82d", "guid": "bfdfe7dc352907fc980b868725387e98fea6e8fdefc722fe9995117b432c1180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899afdf154d7b581e4e6722efb4c89722", "guid": "bfdfe7dc352907fc980b868725387e9800eb72f48284f19e8aeb3435bd73353e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ccbf402257d20049f4e498ea2c75b0c", "guid": "bfdfe7dc352907fc980b868725387e9879e53455071d94a93e77b6a6769ba4ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503284661aa6937c072cc65ce653416f", "guid": "bfdfe7dc352907fc980b868725387e982c0e37396d42e8820d4f94ae1b478e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa08dd19cabe9564e840bf5e33be821b", "guid": "bfdfe7dc352907fc980b868725387e9819b5b42036bb6397bb0db36c68e51c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0ad8e963e55b2966ef155eb6337c601", "guid": "bfdfe7dc352907fc980b868725387e98e5ea4a3398e3e2c8541039f9b5b7ccf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcfa8c73cd757376734ea32a6fc2f140", "guid": "bfdfe7dc352907fc980b868725387e986a94a9d362f4b91d447246f6dd18200a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a47e7ce7f73b5ca1206d268e6e5a3d48", "guid": "bfdfe7dc352907fc980b868725387e98399c41c7701fbf6e0e4b68cf7346d6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e094a4e050288d0d619320a154535f8d", "guid": "bfdfe7dc352907fc980b868725387e983550e714aa8401daab485d95b3476a24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb24a6c83611190daa93e71cb866c07e", "guid": "bfdfe7dc352907fc980b868725387e98c68be0c0d0ac92ea76aae4429e97ee89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855c413375c4a27f8935669054ef11951", "guid": "bfdfe7dc352907fc980b868725387e98ffcb057fbbb7653d76250d4829f8398f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd495ac66c5c5e1f304b290ac5720188", "guid": "bfdfe7dc352907fc980b868725387e9870a2ed7e56708a76e685aa58afdf1ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c86674b01cabde17a65db4913e96af25", "guid": "bfdfe7dc352907fc980b868725387e989d8d8fa1f80f856f4d19e1d444f01fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b611e2424a14e152e98b3e5f207a0c9", "guid": "bfdfe7dc352907fc980b868725387e988e3cdc0b82521f3db176f80d8a58cf55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee2dae763f86202de2d76ad036d34b6", "guid": "bfdfe7dc352907fc980b868725387e98a8cdf1d27bc6d8ac8204f41d35e953bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879eed301a4bab93d81307698aa858c53", "guid": "bfdfe7dc352907fc980b868725387e983fc914a8448b684bcd62fad4f10fc0e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886181f4d4d9d39cd64ccf3c4f75f801e", "guid": "bfdfe7dc352907fc980b868725387e989238987c6d4dc35d3e5a322f03007bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987214ac76a18ad129dae9ae214c7e91dd", "guid": "bfdfe7dc352907fc980b868725387e982eab10a4ae07b13a5937761a89cfc75b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d3b3ea0bfc41aff42218a675d6c88c8", "guid": "bfdfe7dc352907fc980b868725387e98f8571cdf6d6b84a85351970ad65fff0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888615b430e1bfd9f334df8549acd5dcb", "guid": "bfdfe7dc352907fc980b868725387e9881892e040425214db3fb2578de4ee2d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff41bbe3b7214a15144dee11ece16ec", "guid": "bfdfe7dc352907fc980b868725387e98aacee0605a3cd4696ff801429ed87b22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987248467459c281aaac3d3c70466bfc66", "guid": "bfdfe7dc352907fc980b868725387e98d9b5dde53a97a88c61a30832bd559cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45e77306c72f5c21d5216ed5f2d2750", "guid": "bfdfe7dc352907fc980b868725387e98a1aa824512ffbe0aa8647091959b911c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fcfa3acf5232e57ac1beae978ab416c", "guid": "bfdfe7dc352907fc980b868725387e98b188cb742539656a82a35a3f16981bcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c8fd63611ca66a1ed0d66de03be9040", "guid": "bfdfe7dc352907fc980b868725387e982c3450d903e9d96a5538e5a09aaa07b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db83b723a8f93452023d39f0aff9c7ea", "guid": "bfdfe7dc352907fc980b868725387e981f9782dc8dd741f99ab0e81bb8860697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0dc42bd2f2dbf029433cda1a1cc710", "guid": "bfdfe7dc352907fc980b868725387e984025d19e45142061f02f60c129fc05fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986628ee6cb38ea763a38a7d12c9852594", "guid": "bfdfe7dc352907fc980b868725387e98a01b381e328902ff95f15bfd07e38f5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60c471ae5743a380c05f1bb080f420a", "guid": "bfdfe7dc352907fc980b868725387e988a30d3d8acd4e6d20602b67da47193be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb9fcf649f32ff1396927a6b9ece2c2", "guid": "bfdfe7dc352907fc980b868725387e981a0cc038ae1b1fd25719adda27976cce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989353d78e72a84a77848fe3721559fa38", "guid": "bfdfe7dc352907fc980b868725387e9874905207c3e9df630c4cc2cc4b8f326a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98774f58edd7c8fedad893b1bc7d2f20da", "guid": "bfdfe7dc352907fc980b868725387e98dbff2b3ddff5858342d98184e5628d72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981628e8806f34608ae41f6f95c49d4111", "guid": "bfdfe7dc352907fc980b868725387e9815758cc3f525d96d70df78b67a43d851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c2f3085bf4d44b756625eba8b04adb", "guid": "bfdfe7dc352907fc980b868725387e9804e4f56dc3167681cf691e8596c71146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4ce2f34dbd9dc931960d61319b2e922", "guid": "bfdfe7dc352907fc980b868725387e9853ccc12ddfac15e8ffa7df5827562029"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b2ab6dafd5cd546fa126bcee2030d9c", "guid": "bfdfe7dc352907fc980b868725387e986f0a1052aa0ca0cf8f1905181b39665f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b96cf53da7d4ae7d8e7dce45167fd81", "guid": "bfdfe7dc352907fc980b868725387e98f9ac30e6751ea202187da901159227a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a053e711871090950c4a527a62488c75", "guid": "bfdfe7dc352907fc980b868725387e98223c0539cd38711d7a444ccf4ac8bb58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac89b6e12b9a8169e65cab0567378fe0", "guid": "bfdfe7dc352907fc980b868725387e98459f9b8d84f37936b5a3961f1212486a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873e3deb725f3f3826235d94c75ce7a89", "guid": "bfdfe7dc352907fc980b868725387e98163c620f28a83f56ae09907b19a50459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f7ea6f5ee09f1a3ca240d0592b6a20", "guid": "bfdfe7dc352907fc980b868725387e98fac7be43458c86a240c9c227d1209259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d14a5b6fbcd87a8cb8a6892918f89baf", "guid": "bfdfe7dc352907fc980b868725387e98b8750ef6b70c38f5d3efed43794b1885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e34a13753b593fec7e24a6a3738bfe9", "guid": "bfdfe7dc352907fc980b868725387e98f656c306a73ee677f40cdfa9f9ca4557"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ee737aeeda9b8adc46a85eb79bed97", "guid": "bfdfe7dc352907fc980b868725387e9821526f14d99f3d770dc12a04027008fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60ccc23f8d2057e611fb3e012474317", "guid": "bfdfe7dc352907fc980b868725387e987b6b74deff72959662529d87a81ecb8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874aff56e27bbe5c050c56127cc979839", "guid": "bfdfe7dc352907fc980b868725387e984ff2b8aa6691cf2cf57960c7512f48a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7f1e750d9d362e134b11fe3d644406e", "guid": "bfdfe7dc352907fc980b868725387e9837579c29f04e4c3539ec94bc445472f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986db0405cf2fbc4d7c3664213a3ee1003", "guid": "bfdfe7dc352907fc980b868725387e986945e5c3e4e41047a8980cb599c88d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd9ac5176cc8c081b5c54fef152f51ed", "guid": "bfdfe7dc352907fc980b868725387e983bd0acd644bb0ce8df00d1c587ead8ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b858641b5111f799a2da8c1d4e093b0", "guid": "bfdfe7dc352907fc980b868725387e98d946d86b98f2497902130a0062ebb0a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc5a146217d168b91d70d9956c4a0704", "guid": "bfdfe7dc352907fc980b868725387e98d55d34d789a1f86ac8f3ad9acbb944b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc9eee9223c4b782a5aff45db3a14324", "guid": "bfdfe7dc352907fc980b868725387e98981c16ccd4d794cf02683c13599f7221"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cecc7784d9b89035825257838149f04", "guid": "bfdfe7dc352907fc980b868725387e98ddc8ba56e16a719422a7dc504cf71724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8e3eda683b60b21b21da1be0fb962f4", "guid": "bfdfe7dc352907fc980b868725387e98a25d3bfff5529b1dd5969eb1a9425c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fff808f7cbf271edb6a5beacbcb3ddb", "guid": "bfdfe7dc352907fc980b868725387e98456eb65b214512cac8c9321656ce3a72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f69ed607346eadaa3baf8daab7f18323", "guid": "bfdfe7dc352907fc980b868725387e982fae223ba487719295cf409bf39b44e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98164ca30110eca5dafa8a5a111c0730a6", "guid": "bfdfe7dc352907fc980b868725387e98b6fd83cd4dac6dd0f302fc9fddc5720b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831641782df6abfcf968cf7be4d642211", "guid": "bfdfe7dc352907fc980b868725387e98c5276f2250204d3bcbac5138a0874abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeeccc183c41b5d592dbbabee2190cf9", "guid": "bfdfe7dc352907fc980b868725387e9895aecedfcb719baaead44cbb1729c5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed3c56867e5f5a441d39116a28608f15", "guid": "bfdfe7dc352907fc980b868725387e98a24fd2b423e05dd6d9186fd39827ab84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980417f798f66319c2c39709082c40376b", "guid": "bfdfe7dc352907fc980b868725387e98f02306045a58d555bff9be2e61d79d11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da0cfea15b49a9f62159b52ae698b0f4", "guid": "bfdfe7dc352907fc980b868725387e98467fa1467989c14c4013c3a50fa45e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98780323e3faa6908c5147eb18217c68c7", "guid": "bfdfe7dc352907fc980b868725387e985db12a2072ed67e7cf46c4787669da4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983446dbe1eafe9ea5b9e3f693a0109174", "guid": "bfdfe7dc352907fc980b868725387e9872c44737bd90b83b1c578d0a29f262cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98794a0382a832109b1b298fa4e523e3af", "guid": "bfdfe7dc352907fc980b868725387e9883ab9baced37a0d9c4ff931dc6447f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f863661b1e5f7dfb972ba16c0321f42a", "guid": "bfdfe7dc352907fc980b868725387e986dec1fa323eb105bd353a2a879ca4d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd18ae86cffe7701925fbc1cd8364c7f", "guid": "bfdfe7dc352907fc980b868725387e98fd2bf27ca6ef5155fea67bbae44572cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a12c00e526ba210279cb29c6097ce17a", "guid": "bfdfe7dc352907fc980b868725387e9827160be7aaceae07b878cfad218603e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f10ad414a6fa14555960f9de676d7e7b", "guid": "bfdfe7dc352907fc980b868725387e98285146f588a09e3f0c8cc0afcd61543c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e7e6ff2f62f85a81af1b9c9fbbf85e5", "guid": "bfdfe7dc352907fc980b868725387e980e247b30b26c89bee42e7d6f81d0f3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0dabc9c4cc46dcdd46cf2ebda7d1e76", "guid": "bfdfe7dc352907fc980b868725387e98ea3ee11b2da1ad3ceaaf3433256ca91a"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e88b201100cae52d04e013625cfa369c", "guid": "bfdfe7dc352907fc980b868725387e986d52c275603be68942df62606ca0d8e3"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}