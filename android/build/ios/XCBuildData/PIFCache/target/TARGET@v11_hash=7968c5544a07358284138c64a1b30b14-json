{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fab6510d3a8645b7f4278da396427b1c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6bf065f17a80ab99b976c2cfc145f4f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989afd5863a04e6e96032f0d8cac28051a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98865335f695b03239ca8b6d2282e0f613", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989afd5863a04e6e96032f0d8cac28051a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CONTACTS=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_LOCATION_WHENINUSE=0 PERMISSION_NOTIFICATIONS=1 PERMISSION_MEDIA_LIBRARY=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c8a0a310e6e26842aa160e95e068bdc2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d0c693bebe39e39f1f760d24e64b70", "guid": "bfdfe7dc352907fc980b868725387e98c9a2b8ab5059cc2c44b8d77d97018d4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d86e37f387a2d75078fc6faffb928401", "guid": "bfdfe7dc352907fc980b868725387e9801da2094dce88bcbd18ad093999a0a08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984084425c1bba74398705dddacfd6b868", "guid": "bfdfe7dc352907fc980b868725387e98b16f8f27114ae3c4249f4febca71ad29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814b598003192f1261165f1136c6aa0f1", "guid": "bfdfe7dc352907fc980b868725387e9821214f7646beefe7c1dbadbe491cbad6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b78fa0e0ab3cdf8b85c96ad7d09343a", "guid": "bfdfe7dc352907fc980b868725387e9836d80b58615552d2c05594c0e7345a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98328857101e2a4f0253182e5ad102cfa1", "guid": "bfdfe7dc352907fc980b868725387e98d9fe3a2a890fa4ce595969fc5195d242"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834384b34f37ec0ee0f799c32a112d6ad", "guid": "bfdfe7dc352907fc980b868725387e98ba03a575dbcf68273391d9c02f4b1fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d57238dede1e8942b709e0ef8178b0b", "guid": "bfdfe7dc352907fc980b868725387e983090cba8ee8839ec7bb40db23ed6b48c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98923d0a629dc9b1a8b8d85d18f4699a24", "guid": "bfdfe7dc352907fc980b868725387e98c8054289c6d7c84c2745a7f14ee806ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98468ddf81296a05469448a72997cda341", "guid": "bfdfe7dc352907fc980b868725387e9814e7978b13c6eea8dcecd672d09d1677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98090c12be0437e99df5612cf8e50b31e8", "guid": "bfdfe7dc352907fc980b868725387e9849ea80c853171f2da061e42ba898542f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf2d7f53e6fd1b9fbc5187d7d4aaa5d", "guid": "bfdfe7dc352907fc980b868725387e982dee7062d7d760196298cbea9f6cd289", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980de33cf9668385cc4063e4e2e43ec1bc", "guid": "bfdfe7dc352907fc980b868725387e9860dbbce78aecebfcbf72054057ee4f43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bdab435174be39841fb2604bf5d0e9c", "guid": "bfdfe7dc352907fc980b868725387e98ac2bb91db526506137ea9abfd3aaaced"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98570c3a80393947c961d4d69303348a5d", "guid": "bfdfe7dc352907fc980b868725387e9808abbde492f260a072da429373f1c014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675a0cda84e86858d7d541d80f192468", "guid": "bfdfe7dc352907fc980b868725387e986b0eebe6aa19d9f75404fc53da2df0af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d6996ea24a1430013e965458e498d88", "guid": "bfdfe7dc352907fc980b868725387e989fd253d370d6acf183932dcb1fcecb23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c462d690e0f0cc5aba788103b6eb2d", "guid": "bfdfe7dc352907fc980b868725387e98ba4e10617838b995d05d61de7331ec64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f26f916afcef9984422709bccde2375e", "guid": "bfdfe7dc352907fc980b868725387e98e33aa6ceefda86b751b88cb941cc73c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a5bc2defdc03c8979c444946a0e923", "guid": "bfdfe7dc352907fc980b868725387e988ef98ec45bb7eec71cf052e507fa3e7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0cfcd748a565c87f9ef5ab02d6f5389", "guid": "bfdfe7dc352907fc980b868725387e98ab0860ed8b89737e30b5e8eec17cdf15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f756b5c4e989b328c8c2c9f33a633c21", "guid": "bfdfe7dc352907fc980b868725387e98fe4fdacf73a4c18215c396b1d32a257f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f973e1ad5a2b981839ae398fe4edf4f3", "guid": "bfdfe7dc352907fc980b868725387e980e0965b0a5ddca04a44817ffb8f9542c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98aa6b6d560fc6cbc20868a50bd92c4260", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a05fab448253306f9a7605e0197de94", "guid": "bfdfe7dc352907fc980b868725387e9892fe195d803e9949b0394c78730a0b8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7662718a52221e1825692e821d8e93", "guid": "bfdfe7dc352907fc980b868725387e98e90c2583de9bdca32fa7cad618805d89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984533045ab4060bce2521b5a5429f366a", "guid": "bfdfe7dc352907fc980b868725387e981cba43b9d3797cf0de1f913e53e3ae71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2cf4e50de501461983d2ce6389ec5a", "guid": "bfdfe7dc352907fc980b868725387e98347baf422b538892cdfdff087dc1bf4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852dbe0f7e5398795278c0be90eb89a67", "guid": "bfdfe7dc352907fc980b868725387e9806d17a919a82ffcacce191a1c79e7dea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d49d38785127fe1531fa646a53fb11e5", "guid": "bfdfe7dc352907fc980b868725387e9875f92d2f3997436e47240e284b60585e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4de1dd5af1742580dbc3cff07f6ae9a", "guid": "bfdfe7dc352907fc980b868725387e98975f890f61dab8ec5583c9e31ba921fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988adb339b63a2f739b65850314b0db0de", "guid": "bfdfe7dc352907fc980b868725387e98056cadecb3015b08a75f4b461d5dea6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d975397cfc94da177d34992eb5d60388", "guid": "bfdfe7dc352907fc980b868725387e983ec77823adc001e440af15e8991416a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895677a2a7f4236609901b13ef1a6c449", "guid": "bfdfe7dc352907fc980b868725387e982ff5e05c552c9fa50720676e91e1f1d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87211702f198bedfa4df4ddccd68e08", "guid": "bfdfe7dc352907fc980b868725387e98f4df7daa11d9a2940992ff961b8be8fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd011b44385da9fd61526a0214e2bfed", "guid": "bfdfe7dc352907fc980b868725387e98a93ca4b4078831fb2bc7903be17b204e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6cd12fce2ad32eda215e823b90ae52", "guid": "bfdfe7dc352907fc980b868725387e98f8c7ca7231ae0b3aa4b13d66905cab70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e661e236aa595387f13fea7e0a7652bd", "guid": "bfdfe7dc352907fc980b868725387e98df5444dc1de7be4f2ef5e23c203ad3c8"}], "guid": "bfdfe7dc352907fc980b868725387e982a021ddf58a5bf10152c22031f4f874d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d2f84de844dfc934147934ff17f923", "guid": "bfdfe7dc352907fc980b868725387e989eb94618bfb477de0f70afe51345ca49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5be3c0d8db0f9de0f7119fbda7264a0", "guid": "bfdfe7dc352907fc980b868725387e982167f43b27751012ba8298e113d03a3b"}], "guid": "bfdfe7dc352907fc980b868725387e9870d7040400191f1c919582c7f67cbe62", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98355e6b8542c3448b2dae4b36c52487d6", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e989016a8fce7f46333774d1c676f511053", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}