package com.eaglelionsystems.cbrs.cbrs;

import android.os.Build;
import android.provider.Settings;
import android.os.Bundle;
import android.util.Log;
import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class MainActivity extends FlutterFragmentActivity {
    private static final String CHANNEL = "cbrs/device_security";
    private static final String TAG = "DeviceSecurity";

    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL)
            .setMethodCallHandler(
                (call, result) -> {
                    if (call.method.equals("isDeveloperMode")) {
                        boolean isDevMode = isDeveloperModeEnabled();
                        Log.d(TAG, "Developer mode check result: " + isDevMode);
                        result.success(isDevMode);
                    } else {
                        result.notImplemented();
                    }
                }
            );
    }

    private boolean isDeveloperModeEnabled() {
        try {
            int devMode = Settings.Global.getInt(
                getContentResolver(),
                Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0
            );
            Log.d(TAG, "Raw developer mode value: " + devMode);
            return devMode != 0;
        } catch (Exception e) {
            Log.e(TAG, "Error checking developer mode: " + e.getMessage());
            return false;
        }
    }
}
