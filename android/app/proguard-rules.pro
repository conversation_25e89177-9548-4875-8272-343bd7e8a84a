# SafetyNet
-keep class com.google.android.gms.safetynet.** { *; }
-keep class com.google.android.gms.common.** { *; }

# Play Integrity
-keep class com.google.android.play.core.integrity.** { *; }

# Approov
-keep class com.criticalblue.approovsdk.** {*;}


-dontwarn java.beans.ConstructorProperties

-dontwarn java.beans.Transient

-dontwarn org.w3c.dom.bootstrap.DOMImplementationRegistry

-keep class com.hiennv.flutter_callkit_incoming.** { *; }
 
-keep class javax.imageio.** { *; }
-dontwarn javax.imageio.**

-keep class com.github.jaiimageio.** { *; }
-dontwarn com.github.jaiimageio.**

# Agora Chat SDK - Chinese push notification services
-dontwarn com.heytap.msp.push.**
-dontwarn com.meizu.cloud.pushsdk.**
-dontwarn com.vivo.push.**
-dontwarn com.xiaomi.mipush.sdk.**

# A<PERSON>a Chat SDK - Keep main classes
-keep class com.hyphenate.** { *; }
-keep class io.agora.** { *; }
-dontwarn com.hyphenate.push.platform.**

# Agora Chat SDK - Prevent obfuscation of callback interfaces
-keep interface com.hyphenate.** { *; }
-keep class * implements com.hyphenate.** { *; }

# Agora Chat SDK - Additional rules for reflection and serialization
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Agora Chat SDK - Keep model classes that might be serialized
-keep class com.hyphenate.chat.** { *; }
-keep class com.hyphenate.util.** { *; }

# Agora Chat SDK - Prevent warnings for optional dependencies
-dontwarn com.hyphenate.push.platform.oppo.**
-dontwarn com.hyphenate.push.platform.meizu.**
-dontwarn com.hyphenate.push.platform.vivo.**
-dontwarn com.hyphenate.push.platform.mi.**

# ComplyCube SDK - Keep all ComplyCube classes
-keep class com.complycube.** { *; }
-keep interface com.complycube.** { *; }
-dontwarn com.complycube.**

# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Keep all native method names and the names of their classes
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep all classes that have native methods
-keepclasseswithmembers class * {
    native <methods>;
}

# Additional rules for reflection-based libraries
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes RuntimeInvisibleParameterAnnotations

# Keep all serializable classes
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep all Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Additional rules for common Flutter plugins that might cause issues
-keep class com.google.** { *; }
-dontwarn com.google.**

# Keep all classes used by Firebase/Google services
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Keep WebView related classes
-keep class android.webkit.** { *; }
-dontwarn android.webkit.**

# Keep all classes that might be accessed via JNI
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Critical Flutter rules to prevent APK corruption
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.embedding.android.** { *; }
-keep class io.flutter.embedding.engine.** { *; }

# Keep Flutter's main activity and application classes
-keep class **.MainActivity { *; }
-keep class **.MainApplication { *; }

# Keep all Flutter plugin registrants
-keep class io.flutter.plugins.GeneratedPluginRegistrant { *; }

# Prevent obfuscation of Flutter method channels
-keepclassmembers class * {
    @io.flutter.plugin.common.MethodCall *;
}

# Keep all classes with @Keep annotation
-keep @androidx.annotation.Keep class * { *; }
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# Additional rules for plugins that use reflection
-keepattributes *Annotation*,Signature,Exception,InnerClasses,EnclosingMethod
-keepattributes SourceFile,LineNumberTable

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Prevent stripping of Flutter's native libraries
-keep class **.so { *; }
-keep class **.dll { *; }

# Keep all classes that might be instantiated via reflection
-keepclassmembers class * {
    public <init>(...);
}
